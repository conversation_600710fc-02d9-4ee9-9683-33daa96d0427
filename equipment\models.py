"""
Module: models.py

Contains Django models for handling equipment categories, equipment items, and their properties.

This module defines the following classes:
- Category: Represents a category for equipment.
- SolutionTechnologique: Represents a technological solution used by equipment.
- Item: Represents an equipment item to be audited.

Each class provides specific fields and methods for handling related data.
"""

from django.db import models
from django.urls import reverse
from django.forms import model_to_dict
from django_extensions.db.fields import AutoSlugField


class Category(models.Model):
    """
    Represents a category for equipment items.
    """
    name = models.CharField(max_length=50)
    slug = AutoSlugField(unique=True, populate_from='name')

    def __str__(self):
        return f"Category: {self.name}"

    class Meta:
        verbose_name_plural = 'Categories'


class SolutionTechnologique(models.Model):
    """
    Represents a technological solution used by an equipment.
    """
    name = models.CharField(max_length=100, unique=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = 'Technlogical Solutions'



class Item(models.Model):
    """
    Represents an equipment item to be audited.
    """
    slug = AutoSlugField(unique=True, populate_from='name')
    name = models.CharField(max_length=50)
    description = models.TextField(max_length=256)
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    expiring_date = models.DateTimeField(null=True, blank=True)

    # New Fields
    solution = models.ForeignKey(SolutionTechnologique, on_delete=models.SET_NULL, null=True, blank=True)
    version = models.CharField(max_length=50)
    compliance_points = models.CharField(max_length=50)

    def __str__(self):
        return f"{self.name} ({self.category.name})"

    def get_absolute_url(self):
        return reverse('item-detail', kwargs={'slug': self.slug})

    def to_json(self):
        product = model_to_dict(self)
        product['id'] = self.id
        product['text'] = self.name
        product['category'] = self.category.name
        #product['solution'] = self.solution.name #new
        product['total_product'] = 0
        return product

    class Meta:
        ordering = ['name']
        verbose_name_plural = 'Items'


# Legacy Delivery model removed as it's no longer needed in the audit system
