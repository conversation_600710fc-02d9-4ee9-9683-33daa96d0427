{% extends "equipment/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock title %}

{% block content %}
<div class="container p-5">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'audit_script_list' %}">Audit Scripts</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ title }}</li>
                </ol>
            </nav>
            <h2 class="text-success">{{ title }}</h2>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}

                        <div class="row">
                            <div class="col-md-6">
                                <h4 class="mb-3">Script Information</h4>

                                <div class="mb-3">
                                    {{ form.name|as_crispy_field }}
                                </div>

                                <div class="mb-3">
                                    {{ form.version|as_crispy_field }}
                                </div>

                                <div class="mb-3">
                                    {{ form.category|as_crispy_field }}
                                </div>

                                <div class="mb-3">
                                    {{ form.is_active|as_crispy_field }}
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h4 class="mb-3">Script File</h4>

                                <div class="mb-3">
                                    {{ form.script_file|as_crispy_field }}
                                </div>

                                {% if form.instance.pk and form.instance.script_file %}
                                <div class="mb-3">
                                    <p class="text-muted">Current file: <a href="{{ form.instance.script_file.url }}" download>{{ form.instance.script_file.name }}</a></p>
                                    <p class="text-muted">Leave the file field empty to keep the current file.</p>
                                </div>
                                {% endif %}

                                <h4 class="mb-3">Description</h4>

                                <div class="mb-3">
                                    {{ form.description|as_crispy_field }}
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12 text-center">
                                <button type="submit" class="btn btn-success">
                                    <i class="fa-solid fa-save me-2"></i> {{ button_text }}
                                </button>
                                <a href="{% url 'audit_script_list' %}" class="btn btn-secondary">
                                    <i class="fa-solid fa-times me-2"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
