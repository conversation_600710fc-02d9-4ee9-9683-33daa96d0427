
<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        {% load static %}
        <!-- Google Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600;700;800&display=swap" rel="stylesheet">

        <!-- Bootstrap CSS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" type="text/css" href="https://unpkg.com/@webpixels/css@1.1.5/dist/index.css">

        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

        <!-- Custom CSS -->
        <link rel="stylesheet" href="{% static 'css/style.css' %}" type="text/css">
        <link rel="stylesheet" href="{% static 'css/auditor-dashboard.css' %}" type="text/css">
        <link rel="stylesheet" href="{% static 'css/react-components.css' %}" type="text/css">
        {% block stylesheets %}{% endblock stylesheets %}
        <title>AUDIT: {% block title %} {% endblock title %}</title>

        <!-- Favicon -->
        <link rel="icon" href="{% static 'images/logo/favicon.png' %}" type="image/png">

    </head>
    <body>
      <style>
        .paginator {
          display: flex;
          justify-content: center;
        }
      </style>
        {% include "equipment/sidebar.html" %}
        <section id="wrapper">
            <nav class="navbar navbar-expand-md" style="background-color: #023047; box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);">
                <div class="container-fluid mx-2">
                  <div class="navbar-header">
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#toggle-navbar" aria-controls="toggle-navbar" aria-expanded="false" aria-label="Toggle navigation">
                      <i class="fa fa-bars text-white"></i>
                    </button>
                    <a class="navbar-brand text-white" style="font-size: 18px; font-weight: 700;" href="{% url 'dashboard' %}">Audit<span style="color: #ffb703;">Manager</span></a>
                  </div>
                  <div class="collapse navbar-collapse" id="toggle-navbar">
                    <ul class="navbar-nav ms-auto">
                      <li class="nav-item dropdown">
                        <a class="nav-link text-white dropdown-toggle" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                          <i class="fa fa-bell me-1"></i> Notifications
                        </a>
                        <div class="dropdown-menu dropdown-menu-end p-0" aria-labelledby="notificationsDropdown" style="width: 300px;">
                          <div id="notification-center">
                            <!-- React notification component will be mounted here -->
                            <div class="p-3 text-center">
                              <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </li>
                      <li class="nav-item">
                        <a class="nav-link text-white" href="{% url 'user-profile' %}">
                          <i class="fa fa-user me-1"></i> Profile
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
              </nav>
          {% block content%}

          {% endblock content%}
        </section>
        </div>
        <!-- Bootstrap core JavaScript-->
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

        <!-- Core plugin JavaScript-->
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

        <!-- React JS Bundles -->
        <script src="{% static 'js/dist/main.bundle.js' %}"></script>
        <script src="{% static 'js/dist/notifications.bundle.js' %}"></script>

        <!-- Initialize Bootstrap dropdowns for navbar only (not sidebar) -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Initialize navbar dropdowns only
                var dropdownElementList = [].slice.call(document.querySelectorAll('.navbar .dropdown-toggle'));
                dropdownElementList.map(function(dropdownToggleEl) {
                    return new bootstrap.Dropdown(dropdownToggleEl);
                });
            });
        </script>

        {% block javascripts %}{% endblock javascripts %}
      </body>
</html>
