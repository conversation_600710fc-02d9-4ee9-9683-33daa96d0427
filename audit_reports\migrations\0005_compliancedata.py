# Generated by Django 4.2.20 on 2025-05-12 12:45

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('equipment', '0002_delete_delivery'),
        ('audit_reports', '0004_scriptgeneratedreport_auditor_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ComplianceData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_checked', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Date Checked')),
                ('total_checks', models.IntegerField(default=0, verbose_name='Total Checks')),
                ('passed_checks', models.IntegerField(default=0, verbose_name='Passed Checks')),
                ('failed_checks', models.IntegerField(default=0, verbose_name='Failed Checks')),
                ('compliance_score', models.FloatField(default=0.0, verbose_name='Compliance Score (%)')),
                ('detailed_data', models.JSONField(blank=True, null=True, verbose_name='Detailed Compliance Data')),
                ('categories', models.JSONField(blank=True, null=True, verbose_name='Compliance Categories')),
                ('equipment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='compliance_data', to='equipment.item', verbose_name='Equipment')),
                ('report', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='compliance_data', to='audit_reports.scriptgeneratedreport', verbose_name='Related Report')),
            ],
            options={
                'verbose_name': 'Compliance Data',
                'verbose_name_plural': 'Compliance Data',
                'ordering': ['-date_checked'],
            },
        ),
    ]
