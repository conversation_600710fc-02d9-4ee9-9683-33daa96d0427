{% load static %}
<aside class="sidebar" id="show-side-navigation1">
    <div class="sidebar-inner">
        <!-- Close Button -->
        <button class="btn btn-dark d-md-none d-lg-none position-absolute top-0 end-0 mt-2 me-2 rounded-circle" data-close="show-side-navigation1" aria-label="Close">
            <i class="fa fa-times text-white"></i>
        </button>

        <!-- Sidebar Header -->
        <div class="user-card d-flex align-items-center">
            <div class="d-flex align-items-center w-100">
                <div class="avatar-sm me-3">
                    {% if request.user.profile.profile_picture %}
                        <img class="rounded-circle border-white" width="40" src="{{ request.user.profile.profile_picture.url }}" alt="Profile Picture" />
                    {% else %}
                        <img class="rounded-circle border-white" width="40" src="{% static 'assets/img/profile_pics/default.jpg' %}" alt="Profile Picture" />
                    {% endif %}
                </div>
                <div class="d-flex flex-column">
                    <h2 class="h6 mb-0 text-white">
                        {{ request.user.username }}{% if request.user.profile.role == 'AD' %} <i class="fa-solid fa-circle-check text-success"></i>{% endif %}
                    </h2>
                    <span class="badge bg-success mt-1" style="width: fit-content;">
                        {% if request.user.profile.role == 'AD' %}
                            Admin
                        {% elif request.user.profile.role == 'CL' %}
                            Client
                        {% else %}
                            Auditor
                        {% endif %}
                    </span>
                </div>
            </div>
        </div>

        <!-- Navigation Container -->
        <div class="nav-container">
            <!-- Navigation Links -->
            <ul class="nav flex-column pt-3 pt-md-0">
                <li class="nav-item mb-2">
                    <a class="nav-link d-flex align-items-center {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}" href="{% url 'dashboard' %}">
                        <span class="sidebar-icon me-2">
                            <i class="fa fa-tachometer-alt"></i>
                        </span>
                        <span class="sidebar-text">Dashboard</span>
                    </a>
                </li>
            <li class="nav-item mb-2">
                <a class="nav-link d-flex align-items-center sidebar-dropdown-toggle" href="#" id="productsDropdown">
                    <span class="sidebar-icon me-2">
                        <i class="fa fa-folder"></i>
                    </span>
                    <span class="sidebar-text">Audited Equipment</span>
                    <span class="ms-auto">
                        <i class="fa fa-chevron-down"></i>
                    </span>
                </a>
                <ul class="sidebar-dropdown-menu" style="display: none;">
                    <li><a class="dropdown-item text-light {% if request.resolver_match.url_name == 'product-list' %}active{% endif %}" href="{% url 'product-list' %}">All Equipments</a></li>
                    <li><a class="dropdown-item text-light" href="{% url 'category-list' %}">Categories</a></li>
                    <li><a class="dropdown-item text-light" href="{% url 'solutiontechnologique-list' %}">Technological solutions</a></li>
                </ul>
            </li>
            <li class="nav-item mb-2">
                <a class="nav-link d-flex align-items-center {% if request.resolver_match.url_name == 'audit_request_list' %}active{% endif %}" href="{% url 'audit_request_list' %}">
                    <span class="sidebar-icon me-2">
                        <i class="fa-solid fa-receipt"></i>
                    </span>
                    <span class="sidebar-text">Audit Requests</span>
                </a>
            </li>
            <li class="nav-item mb-2">
                <a class="nav-link d-flex align-items-center {% if request.resolver_match.url_name == 'ongoing_audit_list' %}active{% endif %}" href="{% url 'ongoing_audit_list' %}">
                    <span class="sidebar-icon me-2">
                        <i class="fa-solid fa-clipboard-check"></i>
                    </span>
                    <span class="sidebar-text">Ongoing Audit</span>
                </a>
            </li>
            <li class="nav-item mb-2">
                <a class="nav-link d-flex align-items-center {% if request.resolver_match.url_name == 'manual_audit_compliance_list' %}active{% endif %}" href="{% url 'manual_audit_compliance_list' %}">
                    <span class="sidebar-icon me-2">
                        <i class="fa-solid fa-list-check"></i>
                    </span>
                    <span class="sidebar-text">Manual Audit Points</span>
                </a>
            </li>
            <li class="nav-item mb-2">
                <a class="nav-link d-flex align-items-center {% if request.resolver_match.url_name == 'audit_script_list' %}active{% endif %}" href="{% url 'audit_script_list' %}">
                    <span class="sidebar-icon me-2">
                        <i class="fa-solid fa-code"></i>
                    </span>
                    <span class="sidebar-text">Audit Scripts</span>
                </a>
            </li>
             <!-- Delivery link removed as the Delivery model is no longer needed -->
            <li class="nav-item mb-2">
                <a class="nav-link d-flex align-items-center {% if request.resolver_match.url_name == 'auditreportlist' %}active{% endif %}" href="{% url 'auditreportlist' %}">
                    <span class="sidebar-icon me-2">
                        <i class="fa-solid fa-file-alt"></i>
                    </span>
                    <span class="sidebar-text">Audit Reports</span>
                </a>
            </li>
            <li class="nav-item mb-2">
                <a class="nav-link d-flex align-items-center sidebar-dropdown-toggle" href="#" id="reportsDropdown">
                    <span class="sidebar-icon me-2">
                        <i class="fa-solid fa-chart-pie"></i>
                    </span>
                    <span class="sidebar-text">Reports</span>
                    <span class="ms-auto">
                        <i class="fa fa-chevron-down"></i>
                    </span>
                </a>
                <ul class="sidebar-dropdown-menu" style="display: none;">
                    <li>
                        <a class="dropdown-item text-light {% if request.resolver_match.url_name == 'reports_dashboard' %}active{% endif %}" href="{% url 'reports_dashboard' %}">
                            Equipment Reports
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item text-light {% if request.resolver_match.url_name == 'script_reports_dashboard' %}active{% endif %}" href="{% url 'script_reports_dashboard' %}">
                            Script Reports
                        </a>
                    </li>
                </ul>
            </li>
            <li class="nav-item mb-2">
                <a class="nav-link d-flex align-items-center sidebar-dropdown-toggle" href="#" id="accountsDropdown">
                    <span class="sidebar-icon me-2">
                        <i class="fa fa-users"></i>
                    </span>
                    <span class="sidebar-text">Accounts</span>
                    <span class="ms-auto">
                        <i class="fa fa-chevron-down"></i>
                    </span>
                </a>
                <ul class="sidebar-dropdown-menu" style="display: none;">
                    <li><a class="dropdown-item text-light {% if request.resolver_match.url_name == 'profile_list' %}active{% endif %}" href="{% url 'profile_list' %}">All Staff</a></li>
                    <li><a class="dropdown-item text-light {% if request.resolver_match.url_name == 'client_list' %}active{% endif %}" href="{% url 'client_list' %}">Clients</a></li>
                    <li><a class="dropdown-item text-light {% if request.resolver_match.url_name == 'auditor-list' %}active{% endif %}" href="{% url 'auditor-list' %}">Auditors</a></li>
                </ul>
            </li>
            <li role="separator" class="dropdown-divider mt-4 mb-3 border-gray-700"></li>
            <li class="nav-item">
                <form id="logout-form" action="{% url 'user-logout' %}" method="post" style="display: none;">
                    {% csrf_token %}
                </form>
                <a href="javascript:void(0)" onclick="document.getElementById('logout-form').submit();" class="nav-link d-flex align-items-center">
                    <span class="sidebar-icon me-2">
                        <i class="fas fa-sign-out-alt"></i>
                    </span>
                    <span class="sidebar-text">Logout</span>
                </a>
            </li>
        </ul>
    </div>
</div>
</aside>

<!-- Sidebar dropdown styles and script -->
<style>
    .sidebar-dropdown-menu {
        list-style: none;
        padding-left: 2.5rem;
        margin-top: 0.25rem;
        margin-bottom: 0.5rem;
    }

    .sidebar-dropdown-menu .dropdown-item {
        padding: 0.5rem 0;
        display: block;
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.8) !important;
        text-decoration: none;
        transition: all 0.2s ease;
    }

    .sidebar-dropdown-menu .dropdown-item:hover {
        color: #fff !important;
        padding-left: 0.25rem;
    }

    .sidebar-dropdown-menu .dropdown-item.active {
        color: #fff !important;
        font-weight: 600;
    }

    .sidebar-dropdown-toggle .fa-chevron-down {
        transition: transform 0.2s ease;
    }

    .sidebar-dropdown-toggle.active .fa-chevron-down {
        transform: rotate(180deg);
    }
</style>

<script>
    // Wait for the DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Add click handlers for sidebar dropdowns
        document.querySelectorAll('.sidebar-dropdown-toggle').forEach(function(element) {
            element.addEventListener('click', function(e) {
                e.preventDefault();

                // Toggle active class on the dropdown toggle
                this.classList.toggle('active');

                // Toggle the dropdown menu
                var dropdownMenu = this.nextElementSibling;
                if (dropdownMenu.style.display === 'none' || dropdownMenu.style.display === '') {
                    dropdownMenu.style.display = 'block';
                } else {
                    dropdownMenu.style.display = 'none';
                }
            });
        });

        // Check if any dropdown should be open based on active items
        document.querySelectorAll('.sidebar-dropdown-menu').forEach(function(menu) {
            if (menu.querySelector('.active')) {
                menu.style.display = 'block';
                menu.previousElementSibling.classList.add('active');
            }
        });
    });
</script>
