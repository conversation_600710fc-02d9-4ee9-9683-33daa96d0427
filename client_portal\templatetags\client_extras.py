from django import template
import re

register = template.Library()

@register.filter
def extract_equipment(description):
    """
    Extract equipment items from the description.
    Returns a list of equipment items.
    """
    if not description or "Selected Equipment:" not in description:
        return []
    
    # Split the description at "Selected Equipment:"
    parts = description.split("Selected Equipment:")
    if len(parts) < 2:
        return []
    
    # Get the equipment section
    equipment_section = parts[1]
    
    # Extract lines that start with "- "
    equipment_items = []
    for line in equipment_section.split("\n"):
        line = line.strip()
        if line.startswith("- "):
            equipment_items.append(line[2:])
    
    return equipment_items
