# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
env/
venv/
__pycache__/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
# Usually these files are written by a python script from a template
# before PyInstaller builds the exe, so as not to infect the source
# distribution with pre-built binaries contain nasty things like virii
# and to do it without setting up all the other eggs
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django
*.log
*.pot
*.pyc
__pycache__/
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/
static_root/

# Virtual Environment
venv/
env/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Project specific
profile_pics/
audit_platform_backup_*/
node_modules/
.env
.env.*
!.env.example
*.csv
*dump*.sql
*backup*.sql
*backup*.json
add_*.py
update_*.py
debug_*.py
test_*.py
test_*.csv
list_*.py
check_*.py

# System Files
.DS_Store
Thumbs.db

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# IDE - Visual Studio Code
.vscode/
.idea

# IDE - PyCharm
.idea/

# Microsoft Visual Studio
.vscode/
.vs2017/
.vs2019/

# Sublime Text
.sublime-project
.sublime-workspace
.cache/

# Egg Meta Data
*.egg-info/

# mypy
.mypy_cache/

# Pyre type checker
.pyre/

# pytype
.pytype/
