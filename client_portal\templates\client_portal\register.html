{% extends 'client_portal/auth_base.html' %}
{% load crispy_forms_tags %}

{% block title %}Client Registration{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Client Registration</h4>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.username|as_crispy_field }}
                                {{ form.email|as_crispy_field }}
                                {{ form.first_name|as_crispy_field }}
                                {{ form.last_name|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.company_name|as_crispy_field }}
                                {{ form.phone|as_crispy_field }}
                                {{ form.address|as_crispy_field }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.password1|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.password2|as_crispy_field }}
                            </div>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Register</button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center">
                    <p class="mb-0">Already have an account? <a href="{% url 'user-login' %}">Login</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
