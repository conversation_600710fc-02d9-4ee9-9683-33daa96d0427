import django_tables2 as tables
from .models import Item


class ItemTable(tables.Table):
    """
    Table representation for Item model.
    """
    class Meta:
        model = Item
        template_name = "django_tables2/semantic.html"
        fields = (
            'id', 'name', 'category',
            'solution', 'version', 'compliance_points'
        )
        order_by_field = 'sort'
        order_by = 'id'  # Default ordering by ID


# DeliveryTable removed as the Delivery model is no longer needed
