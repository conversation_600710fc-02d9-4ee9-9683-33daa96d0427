/**
 * Auditor Dashboard - Bootstrap 5 Admin Dashboard
 *
 * This is a custom CSS file for the auditor side of the application.
 */

:root {
    /* Main colors */
    --bs-primary: #023047;
    --bs-secondary: #219ebc;
    --bs-tertiary: #8ecae6;
    --bs-quaternary: #ffb703;
    --bs-light: #f0f5fa;
    --bs-dark: #1a1f2b;

    /* Bootstrap overrides */
    --bs-blue: #023047;
    --bs-indigo: #6610f2;
    --bs-purple: #6f42c1;
    --bs-pink: #d63384;
    --bs-red: #dc3545;
    --bs-orange: #fd7e14;
    --bs-yellow: #ffb703;
    --bs-green: #198754;
    --bs-teal: #20c997;
    --bs-cyan: #0dcaf0;
}

body {
    font-family: 'Nunito', 'Inter', sans-serif;
    background-color: var(--bs-light);
    color: var(--bs-dark);
    overflow-x: hidden;
    line-height: 1.6;
}

/* Typography */
h1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6 {
    font-weight: 700;
    color: var(--bs-dark);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.fw-extrabold {
    font-weight: 800 !important;
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    width: 250px;
    background: var(--bs-primary);
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease-in-out;
    box-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.15);
    overflow-y: auto;
    overflow-x: hidden;
}

.sidebar-inner {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.9);
    padding: 1rem 1.5rem;
    font-weight: 700;
    border-radius: 0.75rem;
    margin: 0.35rem 0.75rem;
    transition: all 0.3s ease;
    font-size: 1rem;
    position: relative;
    border-left: 4px solid transparent;
    letter-spacing: 0.025em;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1);
}

.sidebar .nav-link:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.15);
    border-left: 4px solid rgba(255, 255, 255, 0.7);
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.2);
    border-left: 4px solid #fff;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateX(5px);
}

.sidebar .nav-link i,
.sidebar .nav-link svg {
    margin-right: 0.5rem;
}

.sidebar-text {
    font-size: 0.875rem;
}

.sidebar-icon {
    width: 1.25rem;
    height: 1.25rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.dropdown-divider {
    border-color: rgba(255, 255, 255, 0.15);
}

/* Content */
.content {
    margin-left: 250px;
    min-height: 100vh;
    padding-top: 0;
    transition: all 0.3s ease-in-out;
    background-color: var(--bs-light);
    position: relative;
    z-index: 1;
}

@media (max-width: 991.98px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .content {
        margin-left: 0;
    }
}

/* Navbar */
.navbar-dashboard {
    background-color: #fff;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    padding: 1rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
    letter-spacing: -0.025em;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.5rem 2rem rgba(33, 40, 50, 0.15);
    margin-bottom: 2rem;
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
    border-top: 4px solid var(--bs-primary);
}

.card::before {
    content: '';
    position: absolute;
    top: -4px;
    left: 0;
    right: 0;
    height: 4px;
    background-color: var(--bs-primary);
    z-index: 2;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: 0 1rem 3rem rgba(33, 40, 50, 0.2);
}

.card:hover::before {
    background-color: var(--bs-secondary);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid var(--bs-gray-200);
    padding: 1.5rem 1.75rem;
    border-radius: 1rem 1rem 0 0 !important;
    display: flex;
    align-items: center;
    position: relative;
}

.card-header h2, .card-header h3, .card-header h4, .card-header h5, .card-header h6 {
    margin-bottom: 0;
    font-weight: 700;
    color: var(--bs-dark);
}

.card-body {
    padding: 1.75rem;
    background-color: #fff;
    position: relative;
}

.card-footer {
    background-color: #fff;
    border-top: 1px solid var(--bs-gray-200);
    padding: 1.5rem 1.75rem;
    border-radius: 0 0 1rem 1rem !important;
    position: relative;
}

/* Buttons */
.btn {
    font-weight: 600;
    padding: 0.625rem 1.25rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    letter-spacing: 0.025em;
    text-transform: uppercase;
    font-size: 0.85rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background-color: var(--bs-primary);
    border: none;
    color: #fff;
}

.btn-primary:hover {
    background-color: #01253a;
    color: #fff;
}

.btn-secondary {
    background-color: var(--bs-secondary);
    border: none;
    color: #fff;
}

.btn-secondary:hover {
    background-color: #1a7d94;
    color: #fff;
}

.btn-tertiary {
    background-color: var(--bs-tertiary);
    border: none;
    color: var(--bs-primary);
}

.btn-tertiary:hover {
    background-color: #6fb8d8;
    color: var(--bs-primary);
}

.btn-quaternary {
    background-color: var(--bs-quaternary);
    border: none;
    color: var(--bs-dark);
}

.btn-quaternary:hover {
    background-color: #e69500;
    color: var(--bs-dark);
}

/* Icons */
.icon-shape {
    width: 48px;
    height: 48px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.75rem;
    color: #fff;
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.icon-shape::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    z-index: -1;
    transform: scale(0);
    transition: transform 0.3s ease;
    border-radius: 50%;
}

.icon-shape:hover {
    transform: translateY(-5px);
}

.icon-shape:hover::before {
    transform: scale(1.5);
}

.icon-shape-primary {
    background-color: var(--bs-primary);
}

.icon-shape-secondary {
    background-color: var(--bs-secondary);
}

.icon-shape-tertiary {
    background-color: var(--bs-tertiary);
    color: var(--bs-primary);
}

.icon-shape-quaternary {
    background-color: var(--bs-quaternary);
    color: var(--bs-primary);
}

.icon-shape-success {
    background-color: #198754;
}

.icon-shape-danger {
    background-color: #dc3545;
}

.icon-shape-warning {
    background-color: var(--bs-quaternary);
    color: #212529;
}

.icon-shape-info {
    background-color: var(--bs-secondary);
}

.icon-shape-sm {
    width: 32px;
    height: 32px;
}

.icon {
    width: 1.25rem;
    height: 1.25rem;
}

.icon-xxs {
    width: 0.875rem;
    height: 0.875rem;
}

/* Badges */
.badge {
    padding: 0.5em 0.85em;
    font-weight: 800;
    font-size: 0.75em;
    border-radius: 0.5rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
    letter-spacing: 0.05em;
    text-transform: uppercase;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.2);
}

.badge.bg-primary {
    background-color: var(--bs-primary) !important;
    border-color: rgba(255, 255, 255, 0.2);
}

.badge.bg-secondary {
    background-color: var(--bs-secondary) !important;
    border-color: rgba(255, 255, 255, 0.2);
}

.badge.bg-success {
    background-color: #198754 !important;
    border-color: rgba(255, 255, 255, 0.2);
}

.badge.bg-info {
    background-color: var(--bs-tertiary) !important;
    border-color: rgba(255, 255, 255, 0.2);
    color: var(--bs-primary);
}

.badge.bg-warning {
    background-color: var(--bs-quaternary) !important;
    color: #212529;
    border-color: rgba(0, 0, 0, 0.1);
}

.badge.bg-danger {
    background-color: #dc3545 !important;
    border-color: rgba(255, 255, 255, 0.2);
}

/* Tables */
.table-centered th,
.table-centered td {
    vertical-align: middle;
}

.table-nowrap th,
.table-nowrap td {
    white-space: nowrap;
}

.table {
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 1.5rem;
}

.table thead th {
    padding: 1rem 1.25rem;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    font-weight: 700;
    border-top: 0;
    border-bottom: 1px solid var(--bs-gray-200);
    color: var(--bs-dark);
    background-color: var(--bs-light);
}

.table tbody td {
    font-size: 0.875rem;
    font-weight: 400;
    padding: 1rem 1.25rem;
    color: var(--bs-gray-700);
    border-top: 1px solid var(--bs-gray-200);
    transition: all 0.2s ease;
}

.table tbody tr:hover td {
    background-color: rgba(2, 48, 71, 0.05);
}

.table-rounded {
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 0.25rem 1.5rem rgba(33, 40, 50, 0.1);
}

.thead-light th {
    background-color: var(--bs-light);
    color: var(--bs-dark);
    font-weight: 700;
}

/* User card */
.user-card {
    padding: 1.75rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.1) 100%);
    border-radius: 1rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.user-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.user-card:hover {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0.15) 100%);
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.2);
}

.user-card:hover::before {
    opacity: 1;
}

/* Custom text colors */
.text-primary {
    color: var(--bs-primary) !important;
}

.text-secondary {
    color: var(--bs-secondary) !important;
}

.text-tertiary {
    color: var(--bs-tertiary) !important;
}

.text-quaternary {
    color: var(--bs-quaternary) !important;
}

/* Custom background colors */
.bg-primary {
    background-color: var(--bs-primary) !important;
}

.bg-secondary {
    background-color: var(--bs-secondary) !important;
}

.bg-tertiary {
    background-color: var(--bs-tertiary) !important;
}

.bg-quaternary {
    background-color: var(--bs-quaternary) !important;
}

.bg-light {
    background-color: var(--bs-light) !important;
}

.bg-dark {
    background-color: var(--bs-dark) !important;
}

/* Custom gradients */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--bs-primary) 0%, #01253a 100%) !important;
}

.bg-gradient-secondary {
    background: linear-gradient(135deg, var(--bs-secondary) 0%, #1a7d94 100%) !important;
}

.bg-gradient-tertiary {
    background: linear-gradient(135deg, var(--bs-tertiary) 0%, #6fb8d8 100%) !important;
}

.bg-gradient-quaternary {
    background: linear-gradient(135deg, var(--bs-quaternary) 0%, #e69500 100%) !important;
}
