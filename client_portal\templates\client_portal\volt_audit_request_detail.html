{% extends 'client_portal/volt_base.html' %}

{% block title %}Audit Request #{{ audit_request.id }}{% endblock %}
{% block page_title %}Audit Request #{{ audit_request.id }}{% endblock %}

{% block content %}
<div class="py-4">
    <div class="d-flex justify-content-between w-100 flex-wrap mb-4">
        <div class="mb-3 mb-lg-0">
            <h1 class="h4">Audit Request #{{ audit_request.id }}</h1>
            <p class="mb-0">Detailed information about your audit request.</p>
        </div>
        <div>
            <a href="{% url 'client_audit_request_list' %}" class="btn btn-sm btn-gray-200 d-inline-flex align-items-center">
                <i class="fas fa-arrow-left me-2"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-12 col-xl-4 mb-4">
            <div class="card border-0 shadow">
                <div class="card-header border-bottom d-flex align-items-center justify-content-between">
                    <h2 class="fs-5 fw-bold mb-0">Request Information</h2>
                    <div>
                        {% if audit_request.status == 'pending' %}
                        <span class="badge bg-warning">Pending Review</span>
                        {% elif audit_request.status == 'approved' %}
                        <span class="badge bg-success">Accepted - Audit in Progress</span>
                        {% elif audit_request.status == 'rejected' %}
                        <span class="badge bg-danger">Rejected</span>
                        {% elif audit_request.status == 'completed' %}
                        <span class="badge bg-info">Completed</span>
                        {% else %}
                        <span class="badge bg-secondary">Unknown</span>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0 pb-0">
                            <span class="fw-bold">Request ID:</span>
                            <span>{{ audit_request.id }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0 pb-0">
                            <span class="fw-bold">Date:</span>
                            <span>{{ audit_request.date|date:"F d, Y" }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0 pb-0">
                            <span class="fw-bold">Institution:</span>
                            <span>{{ audit_request.institution_name }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0 pb-0">
                            <span class="fw-bold">Contact:</span>
                            <span>{{ audit_request.phone_number }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0 pb-0">
                            <span class="fw-bold">Email:</span>
                            <span>{{ audit_request.email }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                            <span class="fw-bold">Status:</span>
                            <span class="fw-extrabold">{{ audit_request.status|title }}</span>
                        </li>
                    </ul>
                </div>
            </div>

            {% if audit_request.status == 'R' %}
            <div class="card border-0 shadow mt-4">
                <div class="card-header border-bottom bg-danger text-white">
                    <h2 class="fs-5 fw-bold mb-0">Rejection Reason</h2>
                </div>
                <div class="card-body">
                    {% if audit_request.notes %}
                    <p>{{ audit_request.notes }}</p>
                    {% else %}
                    <p class="text-muted">No specific reason provided.</p>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>

        <div class="col-12 col-xl-8 mb-4">
            <div class="card border-0 shadow">
                <div class="card-header border-bottom d-flex align-items-center justify-content-between">
                    <h2 class="fs-5 fw-bold mb-0">Audit Request Details</h2>
                </div>
                <div class="card-body">
                    <h5 class="fw-bold mb-3">Description</h5>
                    <div class="mb-4">
                        <div class="card card-body bg-light">
                            <pre class="mb-0">{{ audit_request.description }}</pre>
                        </div>
                    </div>

                    {% if audit_request.equipment.all %}
                    <h5 class="fw-bold mb-3">Selected Equipment</h5>
                    <div class="mb-4">
                        <div class="card card-body bg-light">
                            <ul class="list-group list-group-flush">
                                {% for item in audit_request.equipment.all %}
                                <li class="list-group-item bg-light">
                                    <i class="fas fa-server me-2"></i> {{ item.name }} ({{ item.category.name }})
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    {% endif %}

                    {% if audit_request.config_file %}
                    <h5 class="fw-bold mb-3">Configuration File</h5>
                    <div class="mb-4">
                        <p class="mb-0">
                            <a href="{{ audit_request.config_file.url }}" class="btn btn-sm btn-primary" target="_blank">
                                <i class="fas fa-download me-2"></i> Download Configuration File
                            </a>
                        </p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12 mb-4">
            <div class="card border-0 shadow">
                <div class="card-header border-bottom d-flex align-items-center justify-content-between">
                    <h2 class="fs-5 fw-bold mb-0">Audit Request Timeline</h2>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item border-bottom">
                            <div class="row align-items-center">
                                <div class="col-auto">
                                    <div class="icon-shape icon-sm bg-success text-white rounded-circle">
                                        <i class="fas fa-paper-plane"></i>
                                    </div>
                                </div>
                                <div class="col ms-n2">
                                    <h4 class="h6 mb-0">
                                        <span class="text-dark">Audit Request Submitted</span>
                                    </h4>
                                    <span class="text-muted small">{{ audit_request.date|date:"M d, Y" }}</span>
                                </div>
                            </div>
                        </div>

                        {% if audit_request.status != 'pending' %}
                        <div class="list-group-item border-bottom">
                            <div class="row align-items-center">
                                <div class="col-auto">
                                    <div class="icon-shape icon-sm {% if audit_request.status == 'rejected' %}bg-danger{% else %}bg-success{% endif %} text-white rounded-circle">
                                        {% if audit_request.status == 'rejected' %}
                                        <i class="fas fa-times"></i>
                                        {% else %}
                                        <i class="fas fa-check"></i>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col ms-n2">
                                    <h4 class="h6 mb-0">
                                        <span class="text-dark">
                                            {% if audit_request.status == 'rejected' %}
                                            Audit Request Rejected
                                            {% else %}
                                            Audit Request Accepted
                                            {% endif %}
                                        </span>
                                    </h4>
                                    <span class="text-muted small">{{ audit_request.date|date:"M d, Y" }}</span>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        {% if audit_request.status == 'approved' %}
                        <div class="list-group-item border-bottom">
                            <div class="row align-items-center">
                                <div class="col-auto">
                                    <div class="icon-shape icon-sm bg-info text-white rounded-circle">
                                        <i class="fas fa-cogs"></i>
                                    </div>
                                </div>
                                <div class="col ms-n2">
                                    <h4 class="h6 mb-0">
                                        <span class="text-dark">Audit In Progress</span>
                                    </h4>
                                    <span class="text-muted small">{{ audit_request.date|date:"M d, Y" }}</span>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        {% if audit_request.status == 'completed' %}
                        <div class="list-group-item border-bottom">
                            <div class="row align-items-center">
                                <div class="col-auto">
                                    <div class="icon-shape icon-sm bg-info text-white rounded-circle">
                                        <i class="fas fa-flag-checkered"></i>
                                    </div>
                                </div>
                                <div class="col ms-n2">
                                    <h4 class="h6 mb-0">
                                        <span class="text-dark">Audit Completed</span>
                                    </h4>
                                    <span class="text-muted small">{{ audit_request.date|date:"M d, Y" }}</span>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
