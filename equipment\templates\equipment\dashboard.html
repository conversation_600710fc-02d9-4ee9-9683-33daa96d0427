{% extends "equipment/base.html" %}
{% load static %}
{% block title %}Dashboard{% endblock title %}

{% block content %}
<!-- Dashboard -->
<div class="d-flex flex-column flex-lg-row h-lg-full bg-surface-secondary">
    {% load static %}
    <script src="https://cdn.jsdelivr.net/npm/chart.js@2.9.4/dist/Chart.min.js"></script>
    <!-- Main content -->
    <div class="h-screen flex-grow-1 overflow-y-lg-auto">
        <!-- Main -->
        <main class="py-6 bg-surface-secondary">
            <div class="container-fluid">
                <!-- React App Component -->
                <div id="react-root" class="mb-4">
                    <!-- React app will be mounted here -->
                    <div class="text-center py-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>

                <!-- Card stats -->
                <div id="dashboard-widgets" style="display: none;">
                    <!-- React dashboard widgets will be mounted here -->
                </div>

                <!-- Traditional Django stats -->
                <div id="traditional-stats" class="row g-6 mb-6">
                    <style>
                        a {
                            text-decoration: none;
                        }
                    </style>
                    <div class="col-xl-3 col-sm-6 col-12" id="products">
                        <a href="{% url 'product-list' %}">
                            <div class="card shadow border-0">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col">
                                            <span class="h6 font-bold text-dark text-sm d-block mb-2">Total Audited Equipment</span>
                                            <span class="h3 font-bold mb-0">{{total_items}}</span>
                                        </div>
                                        <div class="col-auto">
                                            <div class="icon icon-shape bg-tertiary text-white text-lg rounded-circle">
                                                <i class="fa fa-clipboard-list"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-sm-6 col-12" id="profiles">
                        <a href="{% url 'profile_list' %}">
                            <div class="card shadow border-0">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col">
                                            <span class="h6 font-bold text-dark text-sm d-block mb-2">Clients & Auditors</span>
                                            <span class="h3 font-bold mb-0">{{profiles_count}}</span>
                                        </div>
                                        <div class="col-auto">
                                            <div class="icon icon-shape bg-primary text-white text-lg rounded-circle">
                                                <i class="fa-solid fa fa-users"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-sm-6 col-12" id="ongoing_audits">
                        <a href="{% url 'ongoing_audit_list' %}">
                            <div class="card shadow border-0">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col">
                                            <span class="h6 font-bold text-dark text-sm d-block mb-2">Ongoing Audits</span>
                                            <span class="h3 font-bold mb-0">{{ongoing_audits.count}}</span>
                                        </div>
                                        <div class="col-auto">
                                            <div class="icon icon-shape bg-info text-white text-lg rounded-circle">
                                                <i class="fa fa-clipboard-check"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-sm-6 col-12" id="audit_requests">
                        <a href="{% url 'audit_request_list' %}">
                            <div class="card shadow border-0">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col">
                                            <span class="h6 font-bold text-dark text-sm d-block mb-2">Audit Requests</span>
                                            <span class="h3 font-bold mb-0">{{audit_requests_count}}</span>
                                        </div>
                                        <div class="col-auto">
                                            <div class="icon icon-shape bg-primary text-white text-lg rounded-circle">
                                                <i class="fa-solid fa fa-users"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
                {% include 'equipment/charts.html' %}
            </div>
        </main>
    </div>
</div>
{% endblock content %}

{% block javascripts %}
<script src="{% static 'js/dist/dashboard.bundle.js' %}"></script>
<script>
    // Simple script to ensure traditional stats are always visible
    document.addEventListener('DOMContentLoaded', function() {
        const traditionalStats = document.getElementById('traditional-stats');

        // Ensure traditional stats are visible
        if (traditionalStats) {
            traditionalStats.style.display = 'flex';
        }

        // Log dashboard data when it's loaded (for debugging)
        window.addEventListener('reactDashboardLoaded', function(event) {
            console.log('React dashboard loaded with data:', event.detail);
            console.log('Using traditional stats since there is no audit data');
        });
    });
</script>
{% endblock javascripts %}
