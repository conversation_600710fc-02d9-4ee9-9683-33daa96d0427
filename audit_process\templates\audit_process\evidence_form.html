{% extends "equipment/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>{{ title }}</h2>
                <div>
                    <a href="{% url 'manual_compliance_list' audit_id=audit.id %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Compliance Points
                    </a>
                </div>
            </div>
            <p class="text-muted">
                Audit: <strong>{{ audit.item.name }}</strong> | 
                Compliance Point: <strong>{{ compliance_point.point_id }}</strong> - {{ compliance_point.description|truncatechars:50 }}
            </p>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Upload Evidence</h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            {{ form.file|as_crispy_field }}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.description|as_crispy_field }}
                        </div>
                        
                        <div class="d-flex justify-content-end mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload me-2"></i> {{ button_text }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Evidence Information</h5>
                </div>
                <div class="card-body">
                    <p class="mb-4">Evidence files provide proof of compliance or non-compliance for audit points. You can upload screenshots, logs, configuration files, or any other relevant documentation.</p>
                    
                    <div class="d-flex mb-3">
                        <div class="icon-shape icon-shape-sm icon-shape-primary me-3">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">Supported File Types</h5>
                            <p class="text-muted small mb-0">Images (.jpg, .png), Documents (.pdf, .docx), Text files (.txt), and more.</p>
                        </div>
                    </div>
                    
                    <div class="d-flex mb-3">
                        <div class="icon-shape icon-shape-sm icon-shape-secondary me-3">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">Description</h5>
                            <p class="text-muted small mb-0">Provide a brief description of what this evidence shows.</p>
                        </div>
                    </div>
                    
                    <div class="d-flex">
                        <div class="icon-shape icon-shape-sm icon-shape-warning me-3">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">File Size Limit</h5>
                            <p class="text-muted small mb-0">Maximum file size is 10MB. For larger files, consider compressing them first.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Existing Evidence -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Existing Evidence</h5>
                </div>
                <div class="card-body">
                    {% if compliance_point.evidence_files.all %}
                        <ul class="list-group">
                            {% for evidence in compliance_point.evidence_files.all %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <a href="{{ evidence.file.url }}" target="_blank">
                                            <i class="fas fa-file me-2"></i>
                                            {{ evidence.file.name|slice:"20:" }}
                                        </a>
                                        {% if evidence.description %}
                                            <div class="text-muted small">{{ evidence.description }}</div>
                                        {% endif %}
                                    </div>
                                    <form method="POST" action="{% url 'delete_compliance_evidence' evidence_id=evidence.id %}">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this evidence?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p class="text-muted mb-0">No evidence files have been uploaded yet.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
