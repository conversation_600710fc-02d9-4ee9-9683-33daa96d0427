{% extends "equipment/base.html" %}
{% load static %}

{% block title %}Delete Audit Script{% endblock title %}

{% block content %}
<div class="container p-5">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'audit_script_list' %}">Audit Scripts</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Delete Script</li>
                </ol>
            </nav>
            <h2 class="text-danger">Delete Audit Script</h2>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h4 class="card-title text-center mb-4">Are you sure you want to delete this audit script?</h4>
                    
                    <div class="alert alert-warning">
                        <p><strong>Warning:</strong> This action cannot be undone. All data associated with this script will be permanently deleted.</p>
                    </div>
                    
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Script Details</h5>
                        </div>
                        <div class="card-body">
                            <p><strong>Name:</strong> {{ object.name }}</p>
                            <p><strong>Version:</strong> {{ object.version|default:"N/A" }}</p>
                            <p><strong>Category:</strong> {{ object.category.name|default:"General" }}</p>
                            <p><strong>Date Added:</strong> {{ object.date_added|date:"F d, Y" }}</p>
                            <p><strong>Description:</strong> {{ object.description|default:"No description provided." }}</p>
                        </div>
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="d-flex justify-content-center">
                            <button type="submit" class="btn btn-danger me-2">
                                <i class="fa-solid fa-trash me-2"></i> Yes, Delete
                            </button>
                            <a href="{% url 'audit_script_list' %}" class="btn btn-secondary">
                                <i class="fa-solid fa-times me-2"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
