from django.contrib import admin
from .models import Profile, Auditor, Client, AuditorNotification


@admin.register(Profile)
class ProfileAdmin(admin.ModelAdmin):
    """Admin interface for the Profile model."""
    list_display = ('user', 'telephone', 'email', 'role', 'status')


@admin.register(Auditor)
class AuditorAdmin(admin.ModelAdmin):
    """Admin interface for the Auditor model."""
    fields = ('name', 'phone_number', 'address')
    list_display = ('name', 'phone_number', 'address')
    search_fields = ('name', 'phone_number', 'address')


@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    """Admin interface for the Client model."""
    fields = ('first_name', 'last_name', 'address', 'email', 'phone', 'loyalty_points')
    list_display = ('first_name', 'last_name', 'email', 'phone', 'loyalty_points')
    search_fields = ('first_name', 'last_name', 'email', 'phone')


@admin.register(AuditorNotification)
class AuditorNotificationAdmin(admin.ModelAdmin):
    """Admin interface for the AuditorNotification model."""
    list_display = ('title', 'auditor', 'notification_type', 'is_read', 'created_at')
    list_filter = ('notification_type', 'is_read', 'created_at')
    search_fields = ('title', 'message', 'auditor__user__username')
    date_hierarchy = 'created_at'
