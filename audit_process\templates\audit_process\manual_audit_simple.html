{% extends "equipment/base.html" %}
{% load static %}

{% block title %}Manual Audit Control Points{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>Manual Audit Control Points</h2>
                <div>
                    <a href="{% url 'manual_audit_compliance_list' %}?refresh=true" class="btn btn-success me-2">
                        <i class="fas fa-sync-alt me-2"></i> Refresh Control Points
                    </a>
                    <a href="{% url 'ongoing_audit_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Audits
                    </a>
                </div>
            </div>
            {% if audit %}
            <p class="text-muted">
                Equipment: <strong>{{ audit.item.name }}</strong>
            </p>
            {% endif %}
        </div>
    </div>

    <!-- Control Points Table -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">Control Points</h5>
                </div>
                <div class="card-body">
                    {% if compliance_points %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Description</th>
                                    <th>Command</th>
                                    <th>Evidence</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for point in compliance_points %}
                                <tr>
                                    <td><strong>{{ point.point_id }}</strong></td>
                                    <td>{{ point.description }}</td>
                                    <td>
                                        {% if point.command %}
                                        <code>{{ point.command }}</code>
                                        {% else %}
                                        <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% for evidence in point.evidence_files.all %}
                                        <div class="evidence-file mb-2">
                                            <div class="card">
                                                <div class="card-body p-2">
                                                    <!-- Display image preview for common image formats -->
                                                    {% with file_url=evidence.file.url|lower %}
                                                        {% if ".jpg" in file_url or ".jpeg" in file_url or ".png" in file_url or ".gif" in file_url %}
                                                            <img src="{{ evidence.file.url }}" class="img-fluid img-thumbnail mb-2" style="max-height: 100px;" alt="Evidence Screenshot">
                                                        {% else %}
                                                            <i class="fas fa-file fa-2x text-muted mb-2"></i>
                                                        {% endif %}
                                                    {% endwith %}
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <a href="{{ evidence.file.url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-external-link-alt me-1"></i> View
                                                        </a>
                                                        <form method="POST" action="{% url 'delete_compliance_evidence' evidence_id=evidence.id %}" class="d-inline">
                                                            {% csrf_token %}
                                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this evidence?')">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {% empty %}
                                        <a href="{% url 'add_compliance_evidence' point_id=point.id %}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-plus me-1"></i> Add Screenshot
                                        </a>
                                        {% endfor %}
                                        {% if point.evidence_files.all %}
                                        <div class="mt-2">
                                            <a href="{% url 'add_compliance_evidence' point_id=point.id %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-plus me-1"></i> Add More
                                            </a>
                                        </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <p class="mb-0">No control points found for this equipment. Please select a different equipment or create a new manual audit.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
