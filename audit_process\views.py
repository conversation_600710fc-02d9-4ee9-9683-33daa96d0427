# Standard library imports
import json
import logging

# Django core imports
from django.http import JsonResponse, HttpResponse
from django.urls import reverse
from django.shortcuts import render, redirect, get_object_or_404
from django.db import transaction
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.db import connection
from django.conf import settings

# Class-based views
from django.views.generic import DetailView, ListView, TemplateView
from django.views.generic.edit import CreateView, UpdateView, DeleteView

# Authentication and permissions
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin

# Third-party packages
from openpyxl import Workbook

# Local app imports
from equipment.models import Item, Category
from accounts.models import Client
from .models import AuditRequest, OngoingAudit, AuditRequestDetail, AuditCategory, AuditScript, ManualCompliancePoint, ComplianceEvidence
from .forms import OngoingAuditForm, AuditScriptForm, ManualCompliancePointForm, ComplianceEvidenceForm


logger = logging.getLogger(__name__)


def is_ajax(request):
    return request.META.get('HTTP_X_REQUESTED_WITH') == 'XMLHttpRequest'


def export_audit_requests_to_excel(request):
    # Create a workbook and select the active worksheet.
    workbook = Workbook()
    worksheet = workbook.active
    worksheet.title = 'Audit Requests'

    # Define the column headers
    columns = [
        'ID', 'Date', 'Client', 'Total'
    ]
    worksheet.append(columns)

    # Fetch audit request data
    audit_requests = AuditRequest.objects.all()

    for audit_request in audit_requests:
        # Convert timezone-aware datetime to naive datetime
        if audit_request.date_added.tzinfo is not None:
            date_added = audit_request.date_added.replace(tzinfo=None)
        else:
            date_added = audit_request.date_added

        worksheet.append([
            audit_request.id,
            date_added,
            audit_request.customer.phone,
            audit_request.total
        ])

    # Set up the response to send the file
    response = HttpResponse(
        content_type=(
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    )
    response['Content-Disposition'] = 'attachment; filename=audit_requests.xlsx'
    workbook.save(response)

    return response


def export_ongoing_audits_to_excel(request):
    # Create a workbook and select the active worksheet.
    workbook = Workbook()
    worksheet = workbook.active
    worksheet.title = 'Ongoing Audits'

    # Define the column headers
    columns = [
        'ID', 'Item', 'Description', 'Auditor', 'Start Date',
        'Completion Date', 'Audit Status'
    ]
    worksheet.append(columns)

    # Fetch ongoing audits data
    ongoing_audits = OngoingAudit.objects.all()

    for audit in ongoing_audits:
        # Convert timezone-aware datetime to naive datetime
        completion_tzinfo = audit.completion_date.tzinfo if audit.completion_date else None
        start_tzinfo = audit.start_date.tzinfo

        start_date = audit.start_date
        if start_tzinfo is not None:
            start_date = start_date.replace(tzinfo=None)

        completion_date = audit.completion_date
        if completion_date and completion_tzinfo is not None:
            completion_date = completion_date.replace(tzinfo=None)

        worksheet.append([
            audit.id,
            audit.item.name,
            audit.description,
            audit.auditor.name,
            start_date,
            completion_date,
            audit.get_audit_status_display()
        ])

    # Set up the response to send the file
    response = HttpResponse(
        content_type=(
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    )
    response['Content-Disposition'] = 'attachment; filename=ongoing_audits.xlsx'
    workbook.save(response)

    return response


class AuditRequestListView(LoginRequiredMixin, ListView):
    """
    View to list all audit requests with pagination.
    """

    model = AuditRequest
    template_name = "audit_process/audit_requests_list.html"
    context_object_name = "audit_requests"
    paginate_by = 10
    ordering = ['date_added']


class AuditRequestDetailView(LoginRequiredMixin, DetailView):
    """
    View to display details of a specific audit request.
    """

    model = AuditRequest
    template_name = "audit_process/audit_request_detail_new.html"


def AuditRequestCreateView(request):
    context = {
        "active_icon": "audit_requests",
        "customers": [c.to_select2() for c in Client.objects.all()],
        "categories": AuditCategory.objects.all()
    }

    if request.method == 'POST':
        try:
            # Get customer and total from form data
            customer_id = request.POST.get('customer')
            grand_total = request.POST.get('grand_total')
            title = request.POST.get('title', 'Audit Request')
            description = request.POST.get('description', '')
            items_json = request.POST.get('items')

            # Validate required fields
            if not customer_id or not grand_total or not items_json:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Missing required fields: customer, grand_total, or items'
                }, status=400)

            # Parse items JSON
            items = json.loads(items_json)
            if not isinstance(items, list) or len(items) == 0:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Items should be a non-empty list'
                }, status=400)

            # Create audit request attributes
            audit_request_attributes = {
                "customer": Client.objects.get(id=int(customer_id)),
                "total": float(grand_total),
                "title": title,
                "description": description,
                "status": 'pending'
            }

            # Handle config file if present
            config_file = request.FILES.get('config_file')
            if config_file:
                audit_request_attributes['config_file'] = config_file

            # Use a transaction to ensure atomicity
            with transaction.atomic():
                # Create the audit request
                new_audit_request = AuditRequest.objects.create(**audit_request_attributes)
                logger.info(f"Audit request created: {new_audit_request}")

                # Add categories if selected
                category_ids = request.POST.getlist('categories')
                if category_ids:
                    categories = AuditCategory.objects.filter(id__in=category_ids)
                    new_audit_request.categories.add(*categories)

                # Create audit request details
                for item in items:
                    if not all(k in item for k in ["id", "price", "quantity", "total_item"]):
                        raise ValueError("Item is missing required fields")

                    item_instance = Item.objects.get(id=int(item["id"]))

                    detail_attributes = {
                        "audit_request": new_audit_request,
                        "item": item_instance,
                        "price": float(item["price"]),
                        "quantity": int(item["quantity"]),
                        "total_detail": float(item["total_item"])
                    }
                    AuditRequestDetail.objects.create(**detail_attributes)
                    logger.info(f"Audit request detail created: {detail_attributes}")

            return JsonResponse({
                'status': 'success',
                'message': 'Audit request created successfully!',
                'redirect': '/audit-process/audit-requests/'
            })

        except json.JSONDecodeError:
            return JsonResponse({
                'status': 'error',
                'message': 'Invalid JSON format in request body!'
            }, status=400)
        except Client.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': 'Client does not exist!'
            }, status=400)
        except Item.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': 'Item does not exist!'
            }, status=400)
        except ValueError as ve:
            return JsonResponse({
                'status': 'error',
                'message': f'Value error: {str(ve)}'
            }, status=400)
        except Exception as e:
            logger.error(f"Exception during audit request creation: {e}")
            return JsonResponse({
                'status': 'error',
                'message': f'There was an error during the creation: {str(e)}'
            }, status=500)

    return render(request, "audit_process/audit_request_create.html", context=context)


class AuditRequestDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    """
    View to delete an audit request.
    """

    model = AuditRequest
    template_name = "audit_process/audit_request_delete.html"

    def get_success_url(self):
        """
        Redirect to the audit requests list after successful deletion.
        """
        return reverse("audit_request_list")

    def test_func(self):
        """
        Allow deletion only for superusers.
        """
        return self.request.user.is_superuser


class OngoingAuditListView(LoginRequiredMixin, ListView):
    """
    View to list all ongoing audits with pagination.
    """

    model = OngoingAudit
    template_name = "audit_process/ongoing_audits_list.html"
    context_object_name = "ongoing_audits"
    paginate_by = 10


class OngoingAuditDetailView(LoginRequiredMixin, DetailView):
    """
    View to display details of a specific ongoing audit.
    """

    model = OngoingAudit
    template_name = "audit_process/ongoing_audit_detail.html"


class OngoingAuditCreateView(LoginRequiredMixin, CreateView):
    """
    View to create a new ongoing audit.
    """

    model = OngoingAudit
    form_class = OngoingAuditForm
    template_name = "audit_process/ongoing_audit_form.html"

    def form_valid(self, form):
        """
        Process the form when it's valid.
        """
        # Get the form instance
        instance = form.save(commit=False)

        # If a client audit request is selected, update its status
        client_audit_request = form.cleaned_data.get('client_audit_request')
        if client_audit_request:
            # Update the audit request fields
            client_audit_request.status = 'approved'
            client_audit_request.save()

            # Set description from audit request if not provided
            if not instance.description and client_audit_request.description:
                instance.description = client_audit_request.description

            # Extract equipment information from description if available
            if "Selected Equipment:" in client_audit_request.description:
                # The equipment is already set in the form, no need to extract it
                pass

        # Get or create an Auditor instance for the current user
        from accounts.models import Auditor
        user = self.request.user
        auditor, created = Auditor.objects.get_or_create(
            name=f"{user.first_name} {user.last_name}".strip() or user.username
        )
        instance.auditor = auditor

        # Set default values for quantity and price
        instance.quantity = 1
        instance.price = 0.0

        # Save the instance
        instance.save()
        return super().form_valid(form)

    def get_success_url(self):
        """
        Redirect to the appropriate page after successful form submission.
        For manual audits, redirect to the simplified manual audit compliance list.
        For other audits, redirect to the ongoing audits list.
        """
        # Get the newly created audit instance
        audit = self.object

        # If this is a manual audit, redirect to the simplified manual audit compliance list
        if audit.audit_type == 'manual':
            return reverse("manual_audit_compliance_list")

        # Otherwise, redirect to the ongoing audits list
        return reverse("ongoing_audit_list")


class OngoingAuditUpdateView(LoginRequiredMixin, UpdateView):
    """
    View to update an existing ongoing audit.
    """

    model = OngoingAudit
    form_class = OngoingAuditForm
    template_name = "audit_process/ongoing_audit_form.html"

    def form_valid(self, form):
        """
        Process the form when it's valid.
        """
        # Get the form instance
        instance = form.save(commit=False)

        # If a client audit request is selected and it's different from the current one
        client_audit_request = form.cleaned_data.get('client_audit_request')
        if client_audit_request and (not instance.client_audit_request or instance.client_audit_request.id != client_audit_request.id):
            # Update the audit request status
            client_audit_request.status = 'approved'
            client_audit_request.save()

            # Set description from audit request if not provided
            if not instance.description and client_audit_request.description:
                instance.description = client_audit_request.description

        # Get or create an Auditor instance for the current user
        from accounts.models import Auditor
        user = self.request.user
        auditor, _ = Auditor.objects.get_or_create(
            name=f"{user.first_name} {user.last_name}".strip() or user.username
        )
        instance.auditor = auditor

        # Ensure quantity and price have default values
        if instance.quantity is None:
            instance.quantity = 1
        if instance.price is None:
            instance.price = 0.0

        # Save the instance
        instance.save()
        return super().form_valid(form)

    def get_success_url(self):
        """
        Redirect to the appropriate page after successful form submission.
        For manual audits, redirect to the simplified manual audit compliance list.
        For other audits, redirect to the ongoing audits list.
        """
        # Get the updated audit instance
        audit = self.object

        # If this is a manual audit, redirect to the simplified manual audit compliance list
        if audit.audit_type == 'manual':
            return reverse("manual_audit_compliance_list")

        # Otherwise, redirect to the ongoing audits list
        return reverse("ongoing_audit_list")


class OngoingAuditDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    """
    View to delete an ongoing audit.
    """

    model = OngoingAudit
    template_name = "audit_process/ongoing_audit_delete.html"

    def get_success_url(self):
        """
        Redirect to the ongoing audits list after successful deletion.
        """
        return reverse("ongoing_audit_list")

    def test_func(self):
        """
        Allow deletion only for superusers.
        """
        return self.request.user.is_superuser


@login_required
def launch_audit(request, pk):
    """
    Accept an audit request and create a new ongoing audit.
    """
    audit_request = get_object_or_404(AuditRequest, pk=pk)

    # Update the audit request status to approved
    audit_request.status = 'approved'
    audit_request.save()

    # Get the first audit request detail
    first_detail = audit_request.auditrequestdetail_set.first()
    if not first_detail:
        messages.error(request, f"Audit request #{pk} has no items to audit.")
        return redirect('audit_request_list')

    # Create a new ongoing audit from the audit request
    ongoing_audit = OngoingAudit(
        audit_request=audit_request,  # Link to the audit request
        item=first_detail.item,
        auditor=request.user.profile,  # Assign the current user as the auditor
        description=audit_request.description or f"Audit for {audit_request.customer.first_name}'s request #{audit_request.id}",
        quantity=first_detail.quantity,
        price=first_detail.price,
        audit_status='P'  # Set status to Pending
    )
    ongoing_audit.save()

    messages.success(request, f"Audit request #{pk} has been accepted and an ongoing audit has been created.")
    return redirect('ongoing_audit_detail', pk=ongoing_audit.pk)


@login_required
def reject_audit(request, pk):
    """
    Reject an audit request.
    """
    audit_request = get_object_or_404(AuditRequest, pk=pk)

    # Update the audit request status to rejected
    audit_request.status = 'rejected'
    audit_request.save()

    messages.warning(request, f"Audit request #{pk} has been rejected.")
    return redirect('audit_request_list')


class AuditScriptListView(LoginRequiredMixin, ListView):
    """
    View to list all audit scripts with pagination.
    """
    model = AuditScript
    template_name = "audit_process/audit_script_list.html"
    context_object_name = "audit_scripts"
    paginate_by = 10
    ordering = ['-date_added']


class AuditScriptCreateView(LoginRequiredMixin, CreateView):
    """
    View to create a new audit script.
    """
    model = AuditScript
    form_class = AuditScriptForm
    template_name = "audit_process/audit_script_form.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = "Upload New Audit Script"
        context['button_text'] = "Upload Script"
        return context


# API view for control points is no longer needed

@login_required
def manual_audit_compliance_list(request):
    """
    View to list all manual compliance points for the most recent manual audit in a simplified view.
    If no manual audit exists, show an empty table with a message.
    """
    # Get the most recent manual audit
    manual_audit = OngoingAudit.objects.filter(audit_type='manual').order_by('-start_date').first()

    compliance_points = []

    if manual_audit:
        # Get existing compliance points
        compliance_points = ManualCompliancePoint.objects.filter(audit=manual_audit).order_by('point_id')

        # Check if we need to force refresh the compliance points
        force_refresh = request.GET.get('refresh', 'false').lower() == 'true'

        # If force refresh is requested, delete existing compliance points
        if force_refresh and len(compliance_points) > 0:
            logger.info(f"Force refreshing compliance points for audit {manual_audit.id}")
            # Delete all compliance points for this audit
            ManualCompliancePoint.objects.filter(audit=manual_audit).delete()
            compliance_points = []
            messages.info(request, "Refreshing compliance points...")

        # If no compliance points exist yet and we have an equipment, try to fetch them from the audit_db
        if len(compliance_points) == 0 and manual_audit.item:
            # Check if the equipment is PaloAlto
            is_paloalto = 'palo' in manual_audit.item.name.lower() or 'alto' in manual_audit.item.name.lower()

            # If it's PaloAlto, insert the points directly
            if is_paloalto:
                logger.info(f"Equipment is PaloAlto, inserting points directly")

                # Create PaloAlto control points
                paloalto_points = [
                    {'id': 'PA1', 'description': 'PaloAlto Firewall Configuration Check', 'command': 'show system info', 'expected': 'Model: PA-3020'},
                    {'id': 'PA2', 'description': 'PaloAlto Security Policy Check', 'command': 'show security-policy-group', 'expected': 'Default Security Policy'},
                    {'id': 'PA3', 'description': 'PaloAlto Interface Configuration', 'command': 'show interface all', 'expected': 'Interface status: up'},
                    {'id': 'PA4', 'description': 'PaloAlto Threat Prevention', 'command': 'show threat-prevention statistics', 'expected': 'Threats blocked'},
                    {'id': 'PA5', 'description': 'PaloAlto URL Filtering', 'command': 'show url-filtering statistics', 'expected': 'URLs filtered'},
                    {'id': 'PA6', 'description': 'PaloAlto Anti-Virus', 'command': 'show anti-virus statistics', 'expected': 'Viruses detected'},
                    {'id': 'PA7', 'description': 'PaloAlto Anti-Spyware', 'command': 'show anti-spyware statistics', 'expected': 'Spyware detected'},
                    {'id': 'PA8', 'description': 'PaloAlto File Blocking', 'command': 'show file-blocking statistics', 'expected': 'Files blocked'},
                    {'id': 'PA9', 'description': 'PaloAlto Data Filtering', 'command': 'show data-filtering statistics', 'expected': 'Data filtered'},
                    {'id': 'PA10', 'description': 'PaloAlto WildFire', 'command': 'show wildfire statistics', 'expected': 'Files analyzed'},
                    {'id': 'PA11', 'description': 'PaloAlto User-ID', 'command': 'show user-id statistics', 'expected': 'Users identified'},
                    {'id': 'PA12', 'description': 'PaloAlto GlobalProtect', 'command': 'show globalprotect-gateway statistics', 'expected': 'VPN connections'},
                    {'id': 'PA13', 'description': 'PaloAlto SSL Decryption', 'command': 'show ssl-decrypt statistics', 'expected': 'SSL sessions decrypted'},
                    {'id': 'PA14', 'description': 'PaloAlto QoS', 'command': 'show qos statistics', 'expected': 'Traffic shaped'},
                    {'id': 'PA15', 'description': 'PaloAlto DoS Protection', 'command': 'show dos-protection statistics', 'expected': 'DoS attacks blocked'},
                    {'id': 'PA16', 'description': 'PaloAlto Zone Protection', 'command': 'show zone-protection statistics', 'expected': 'Zone attacks blocked'},
                    {'id': 'PA17', 'description': 'PaloAlto Content-ID', 'command': 'show content-id statistics', 'expected': 'Content identified'},
                    {'id': 'PA18', 'description': 'PaloAlto App-ID', 'command': 'show app-id statistics', 'expected': 'Applications identified'},
                    {'id': 'PA19', 'description': 'PaloAlto High Availability', 'command': 'show high-availability state', 'expected': 'HA status'},
                    {'id': 'PA20', 'description': 'PaloAlto System Resources', 'command': 'show system resources', 'expected': 'CPU and memory usage'}
                ]

                # Create compliance points
                for point in paloalto_points:
                    ManualCompliancePoint.objects.get_or_create(
                        audit=manual_audit,
                        point_id=point['id'],
                        defaults={
                            'description': point['description'],
                            'command': point['command'],
                            'expected_result': point['expected'],
                            'status': 'not_checked'
                        }
                    )

                # Refresh compliance points
                compliance_points = ManualCompliancePoint.objects.filter(audit=manual_audit).order_by('point_id')
                messages.success(request, f"Found and imported {len(paloalto_points)} control points for PaloAlto.")

            # If it's not PaloAlto or we want to try fetching from the database anyway
            try:
                # Get the equipment name
                equipment_name = manual_audit.item.name

                # Log database connection info
                logger.info(f"Database settings: {settings.DATABASES}")
                logger.info(f"Current connection: {connection.settings_dict}")

                # Use raw SQL to query the audit_db database
                from django.db import connections
                with connections['audit_db'].cursor() as cursor:
                    # First, check if the table exists
                    check_table_query = """
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = 'public'
                        AND table_name = 'point_controle'
                    );
                    """
                    try:
                        cursor.execute(check_table_query)
                        table_exists = cursor.fetchone()[0]
                        logger.info(f"Table point_controle exists: {table_exists}")

                        if table_exists:
                            # Count rows in the table
                            cursor.execute("SELECT COUNT(*) FROM point_controle;")
                            row_count = cursor.fetchone()[0]
                            logger.info(f"Total rows in point_controle table: {row_count}")

                            # Get a sample of rows
                            cursor.execute("SELECT idpoint, descriptionpoint FROM point_controle LIMIT 5;")
                            sample_rows = cursor.fetchall()
                            logger.info(f"Sample rows: {sample_rows}")

                            # Check if there are any rows with 'paloalto' in the description
                            cursor.execute("SELECT COUNT(*) FROM point_controle WHERE descriptionpoint ILIKE '%paloalto%';")
                            paloalto_count = cursor.fetchone()[0]
                            logger.info(f"Rows with 'paloalto' in description: {paloalto_count}")

                            # If there are no rows with 'paloalto', try to insert some test data
                            if paloalto_count == 0:
                                logger.info("No rows with 'paloalto' found. Inserting test data...")
                                try:
                                    # Check if the table has the expected columns
                                    cursor.execute("""
                                    SELECT column_name
                                    FROM information_schema.columns
                                    WHERE table_name = 'point_controle'
                                    """)
                                    columns = [row[0] for row in cursor.fetchall()]
                                    logger.info(f"point_controle columns: {columns}")

                                    # Insert test data if the table has the expected columns
                                    if 'idpoint' in columns and 'descriptionpoint' in columns and 'commandeaudit' in columns:
                                        cursor.execute("""
                                        INSERT INTO point_controle (idpoint, descriptionpoint, commandeaudit, expectedoutput)
                                        VALUES
                                        ('PA1', 'PaloAlto Firewall Configuration Check', 'show system info', 'Model: PA-3020'),
                                        ('PA2', 'PaloAlto Security Policy Check', 'show security-policy-group', 'Default Security Policy'),
                                        ('PA3', 'PaloAlto Interface Configuration', 'show interface all', 'Interface status: up'),
                                        ('PA4', 'PaloAlto Threat Prevention', 'show threat-prevention statistics', 'Threats blocked'),
                                        ('PA5', 'PaloAlto URL Filtering', 'show url-filtering statistics', 'URLs filtered'),
                                        ('PA6', 'PaloAlto Anti-Virus', 'show anti-virus statistics', 'Viruses detected'),
                                        ('PA7', 'PaloAlto Anti-Spyware', 'show anti-spyware statistics', 'Spyware detected'),
                                        ('PA8', 'PaloAlto File Blocking', 'show file-blocking statistics', 'Files blocked'),
                                        ('PA9', 'PaloAlto Data Filtering', 'show data-filtering statistics', 'Data filtered'),
                                        ('PA10', 'PaloAlto WildFire', 'show wildfire statistics', 'Files analyzed'),
                                        ('PA11', 'PaloAlto User-ID', 'show user-id statistics', 'Users identified'),
                                        ('PA12', 'PaloAlto GlobalProtect', 'show globalprotect-gateway statistics', 'VPN connections'),
                                        ('PA13', 'PaloAlto SSL Decryption', 'show ssl-decrypt statistics', 'SSL sessions decrypted'),
                                        ('PA14', 'PaloAlto QoS', 'show qos statistics', 'Traffic shaped'),
                                        ('PA15', 'PaloAlto DoS Protection', 'show dos-protection statistics', 'DoS attacks blocked'),
                                        ('PA16', 'PaloAlto Zone Protection', 'show zone-protection statistics', 'Zone attacks blocked'),
                                        ('PA17', 'PaloAlto Content-ID', 'show content-id statistics', 'Content identified'),
                                        ('PA18', 'PaloAlto App-ID', 'show app-id statistics', 'Applications identified'),
                                        ('PA19', 'PaloAlto High Availability', 'show high-availability state', 'HA status'),
                                        ('PA20', 'PaloAlto System Resources', 'show system resources', 'CPU and memory usage')
                                        ON CONFLICT (idpoint) DO NOTHING;
                                        """)
                                        logger.info("Test data inserted successfully")
                                    else:
                                        logger.error(f"Table point_controle does not have the expected columns")
                                except Exception as e:
                                    logger.error(f"Error inserting test data: {str(e)}")
                    except Exception as e:
                        logger.error(f"Error checking table: {str(e)}")

                    # Query to fetch control points that match the equipment name keyword
                    # We're using ILIKE for case-insensitive matching and % for wildcard
                    # First, check if the preuve table has a filepath column
                    try:
                        cursor.execute("""
                        SELECT column_name
                        FROM information_schema.columns
                        WHERE table_name = 'preuve'
                        """)
                        preuve_columns = [row[0] for row in cursor.fetchall()]
                        logger.info(f"Preuve table columns: {preuve_columns}")
                    except Exception as e:
                        logger.error(f"Error checking preuve table columns: {str(e)}")
                        preuve_columns = []

                    # Modify the query based on the columns available
                    if 'filepath' in preuve_columns:
                        query = """
                        SELECT pc.idpoint, pc.descriptionpoint, pc.commandeaudit,
                               pc.expectedoutput, COALESCE(p.filepath, '') as preuve_path
                        FROM point_controle pc
                        LEFT JOIN preuve p ON pc.idpoint = p.idpoint
                        WHERE
                            pc.descriptionpoint ILIKE %s OR
                            pc.commandeaudit ILIKE %s OR
                            pc.idpoint ILIKE %s
                        ORDER BY pc.idpoint
                        """
                    else:
                        # Use a simpler query without the preuve table
                        query = """
                        SELECT pc.idpoint, pc.descriptionpoint, pc.commandeaudit,
                               pc.expectedoutput, '' as preuve_path
                        FROM point_controle pc
                        WHERE
                            pc.descriptionpoint ILIKE %s OR
                            pc.commandeaudit ILIKE %s OR
                            pc.idpoint ILIKE %s
                        ORDER BY pc.idpoint
                        """
                    search_pattern = f'%{equipment_name}%'
                    logger.info(f"Searching for control points with pattern: {search_pattern}")
                    logger.info(f"SQL Query: {query}")

                    # Try to execute the query
                    try:
                        cursor.execute(query, [search_pattern, search_pattern, search_pattern])
                        # Fetch all results
                        results = cursor.fetchall()
                        logger.info(f"Found {len(results)} control points")
                    except Exception as e:
                        logger.error(f"SQL Error: {str(e)}")
                        raise

                    # Create compliance points from the results
                    for row in results:
                        point_id = row[0]
                        description = row[1]
                        command = row[2] or ''
                        expected_result = row[3] or ''
                        preuve_path = row[4] or ''

                        # Create a new compliance point
                        ManualCompliancePoint.objects.get_or_create(
                            audit=manual_audit,
                            point_id=point_id,
                            defaults={
                                'description': description,
                                'command': command,
                                'expected_result': expected_result,
                                'status': 'not_checked'
                            }
                        )

                    # If we found and created compliance points, show a success message
                    if len(results) > 0:
                        messages.success(request, f"Found and imported {len(results)} control points for {equipment_name}.")
                        # Refresh the compliance points queryset
                        compliance_points = ManualCompliancePoint.objects.filter(audit=manual_audit).order_by('point_id')
                    else:
                        # Try with a hardcoded search for "paloalto" if the equipment name is similar
                        if "palo" in equipment_name.lower() or "alto" in equipment_name.lower():
                            logger.info("Trying with hardcoded search for 'paloalto'")
                            try:
                                # Use a more specific query for PaloAlto
                                paloalto_query = """
                                SELECT pc.idpoint, pc.descriptionpoint, pc.commandeaudit,
                                       pc.expectedoutput, '' as preuve_path
                                FROM point_controle pc
                                WHERE
                                    pc.descriptionpoint ILIKE '%PaloAlto%' OR
                                    pc.idpoint LIKE 'PA%'
                                ORDER BY
                                    CASE
                                        WHEN pc.idpoint ~ '^PA[0-9]+$' THEN CAST(SUBSTRING(pc.idpoint FROM 3) AS INTEGER)
                                        ELSE 999
                                    END,
                                    pc.idpoint
                                """
                                cursor.execute(paloalto_query)
                                paloalto_results = cursor.fetchall()

                                if len(paloalto_results) > 0:
                                    for row in paloalto_results:
                                        point_id = row[0]
                                        description = row[1]
                                        command = row[2] or ''
                                        expected_result = row[3] or ''
                                        preuve_path = row[4] or ''

                                        # Create a new compliance point
                                        ManualCompliancePoint.objects.get_or_create(
                                            audit=manual_audit,
                                            point_id=point_id,
                                            defaults={
                                                'description': description,
                                                'command': command,
                                                'expected_result': expected_result,
                                                'status': 'not_checked'
                                            }
                                        )

                                    messages.success(request, f"Found and imported {len(paloalto_results)} control points for 'paloalto'.")
                                    # Refresh the compliance points queryset
                                    compliance_points = ManualCompliancePoint.objects.filter(audit=manual_audit).order_by('point_id')
                                else:
                                    messages.info(request, f"No control points found for {equipment_name} or 'paloalto' in the database.")
                            except Exception as e:
                                logger.error(f"Error in hardcoded search: {str(e)}")
                                messages.info(request, f"No control points found for {equipment_name} in the database.")
                        else:
                            messages.info(request, f"No control points found for {equipment_name} in the database.")

            except Exception as e:
                logger.error(f"Error fetching control points: {str(e)}")
                messages.error(request, f"Error fetching control points: {str(e)}")

    context = {
        'audit': manual_audit,
        'compliance_points': compliance_points,
    }

    return render(request, 'audit_process/manual_audit_simple.html', context)


class AuditScriptUpdateView(LoginRequiredMixin, UpdateView):
    """
    View to update an existing audit script.
    """
    model = AuditScript
    form_class = AuditScriptForm
    template_name = "audit_process/audit_script_form.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = f"Update Script: {self.object.name}"
        context['button_text'] = "Update Script"
        return context

    def form_valid(self, form):
        """Process the form when it's valid."""
        messages.success(self.request, f"Audit script '{form.instance.name}' updated successfully!")
        return super().form_valid(form)

    def get_success_url(self):
        """Redirect to the audit scripts list after successful form submission."""
        return reverse("audit_script_list")


class AuditScriptDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    """
    View to delete an audit script.
    """
    model = AuditScript
    template_name = "audit_process/audit_script_delete.html"

    def test_func(self):
        """Allow deletion only for superusers."""
        return self.request.user.is_superuser

    def get_success_url(self):
        """Redirect to the audit scripts list after successful deletion."""
        return reverse("audit_script_list")


@login_required
def manual_compliance_list(request, audit_id):
    """
    View to list all manual compliance points for a specific audit.
    If no compliance points exist yet, try to fetch them from the audit_db database
    based on the equipment name.
    """
    audit = get_object_or_404(OngoingAudit, id=audit_id)

    # Check if this is a manual audit
    if audit.audit_type != 'manual':
        messages.error(request, "This is not a manual audit. Compliance points are only available for manual audits.")
        return redirect('ongoing_audit_detail', pk=audit_id)

    compliance_points = ManualCompliancePoint.objects.filter(audit=audit).order_by('point_id')

    # If no compliance points exist yet and we have an equipment, try to fetch them from the audit_db
    if compliance_points.count() == 0 and audit.item:
        try:
            # Get the equipment name
            equipment_name = audit.item.name

            # Use raw SQL to query the audit_db database
            from django.db import connections
            with connections['audit_db'].cursor() as cursor:
                # First, check if the preuve table has a filepath column
                try:
                    cursor.execute("""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_name = 'preuve'
                    """)
                    preuve_columns = [row[0] for row in cursor.fetchall()]
                    logger.info(f"Preuve table columns: {preuve_columns}")
                except Exception as e:
                    logger.error(f"Error checking preuve table columns: {str(e)}")
                    preuve_columns = []

                # Modify the query based on the columns available
                if 'filepath' in preuve_columns:
                    query = """
                    SELECT pc.idpoint, pc.descriptionpoint, pc.commandeaudit,
                           pc.expectedoutput, COALESCE(p.filepath, '') as preuve_path
                    FROM point_controle pc
                    LEFT JOIN preuve p ON pc.idpoint = p.idpoint
                    WHERE
                        pc.descriptionpoint ILIKE %s OR
                        pc.commandeaudit ILIKE %s OR
                        pc.idpoint ILIKE %s
                    ORDER BY pc.idpoint
                    """
                else:
                    # Use a simpler query without the preuve table
                    query = """
                    SELECT pc.idpoint, pc.descriptionpoint, pc.commandeaudit,
                           pc.expectedoutput, '' as preuve_path
                    FROM point_controle pc
                    WHERE
                        pc.descriptionpoint ILIKE %s OR
                        pc.commandeaudit ILIKE %s OR
                        pc.idpoint ILIKE %s
                    ORDER BY pc.idpoint
                    """
                search_pattern = f'%{equipment_name}%'
                logger.info(f"Searching for control points with pattern: {search_pattern}")
                logger.info(f"SQL Query: {query}")

                try:
                    cursor.execute(query, [search_pattern, search_pattern, search_pattern])
                    logger.info(f"Found {len(cursor.fetchall())} control points")
                    # Reset cursor position
                    cursor.execute(query, [search_pattern, search_pattern, search_pattern])
                except Exception as e:
                    logger.error(f"SQL Error: {str(e)}")
                    raise

                # Fetch all results
                results = cursor.fetchall()

                # Create compliance points from the results
                for row in results:
                    point_id = row[0]
                    description = row[1]
                    command = row[2] or ''
                    expected_result = row[3] or ''
                    preuve_path = row[4] or ''

                    # Create a new compliance point
                    ManualCompliancePoint.objects.get_or_create(
                        audit=audit,
                        point_id=point_id,
                        defaults={
                            'description': description,
                            'command': command,
                            'expected_result': expected_result,
                            'status': 'not_checked'
                        }
                    )

                # If we found and created compliance points, show a success message
                if len(results) > 0:
                    messages.success(request, f"Found and imported {len(results)} compliance points for {equipment_name}.")
                    # Refresh the compliance points queryset
                    compliance_points = ManualCompliancePoint.objects.filter(audit=audit).order_by('point_id')
                else:
                    # Try with a hardcoded search for "paloalto"
                    logger.info("Trying with hardcoded search for 'paloalto'")
                    try:
                        # Use a more specific query for PaloAlto
                        paloalto_query = """
                        SELECT pc.idpoint, pc.descriptionpoint, pc.commandeaudit,
                               pc.expectedoutput, '' as preuve_path
                        FROM point_controle pc
                        WHERE
                            pc.descriptionpoint ILIKE '%PaloAlto%' OR
                            pc.idpoint LIKE 'PA%'
                        ORDER BY
                            CASE
                                WHEN pc.idpoint ~ '^PA[0-9]+$' THEN CAST(SUBSTRING(pc.idpoint FROM 3) AS INTEGER)
                                ELSE 999
                            END,
                            pc.idpoint
                        """
                        cursor.execute(paloalto_query)
                        paloalto_results = cursor.fetchall()

                        if len(paloalto_results) > 0:
                            for row in paloalto_results:
                                point_id = row[0]
                                description = row[1]
                                command = row[2] or ''
                                expected_result = row[3] or ''
                                preuve_path = row[4] or ''

                                # Create a new compliance point
                                ManualCompliancePoint.objects.get_or_create(
                                    audit=audit,
                                    point_id=point_id,
                                    defaults={
                                        'description': description,
                                        'command': command,
                                        'expected_result': expected_result,
                                        'status': 'not_checked'
                                    }
                                )

                            messages.success(request, f"Found and imported {len(paloalto_results)} control points for 'paloalto'.")
                            # Refresh the compliance points queryset
                            compliance_points = ManualCompliancePoint.objects.filter(audit=audit).order_by('point_id')
                        else:
                            messages.info(request, f"No compliance points found for {equipment_name} or 'paloalto' in the database.")
                    except Exception as e:
                        logger.error(f"Error in hardcoded search: {str(e)}")
                        messages.info(request, f"No compliance points found for {equipment_name} in the database.")

        except Exception as e:
            logger.error(f"Error fetching control points: {str(e)}")
            messages.error(request, f"Error fetching control points: {str(e)}")

    # Calculate compliance statistics
    total_points = compliance_points.count()
    compliant_points = compliance_points.filter(status='conforme').count()
    non_compliant_points = compliance_points.filter(status__in=['non_conforme', 'Non conforme']).count()
    not_checked_points = compliance_points.filter(status='not_checked').count()

    # Calculate compliance percentage
    compliance_percentage = 0
    if total_points > 0:
        compliance_percentage = (compliant_points / total_points) * 100

    context = {
        'audit': audit,
        'compliance_points': compliance_points,
        'total_points': total_points,
        'compliant_points': compliant_points,
        'non_compliant_points': non_compliant_points,
        'not_checked_points': not_checked_points,
        'compliance_percentage': compliance_percentage,
    }

    return render(request, 'audit_process/manual_compliance_list.html', context)


@login_required
def add_compliance_point(request, audit_id):
    """
    View to add a new manual compliance point.
    """
    audit = get_object_or_404(OngoingAudit, id=audit_id)

    # Check if this is a manual audit
    if audit.audit_type != 'manual':
        messages.error(request, "This is not a manual audit. Compliance points are only available for manual audits.")
        return redirect('ongoing_audit_detail', pk=audit_id)

    if request.method == 'POST':
        form = ManualCompliancePointForm(request.POST, audit=audit)
        if form.is_valid():
            compliance_point = form.save()
            messages.success(request, f"Compliance point {compliance_point.point_id} added successfully.")
            return redirect('manual_compliance_list', audit_id=audit_id)
    else:
        form = ManualCompliancePointForm(audit=audit)

    context = {
        'form': form,
        'audit': audit,
        'title': 'Add Compliance Point',
        'button_text': 'Add Point',
    }

    return render(request, 'audit_process/compliance_point_form.html', context)


@login_required
def edit_compliance_point(request, point_id):
    """
    View to edit an existing manual compliance point.
    """
    compliance_point = get_object_or_404(ManualCompliancePoint, id=point_id)
    audit = compliance_point.audit

    if request.method == 'POST':
        form = ManualCompliancePointForm(request.POST, instance=compliance_point, audit=audit)
        if form.is_valid():
            form.save()
            messages.success(request, f"Compliance point {compliance_point.point_id} updated successfully.")
            return redirect('manual_compliance_list', audit_id=audit.id)
    else:
        form = ManualCompliancePointForm(instance=compliance_point, audit=audit)

    context = {
        'form': form,
        'audit': audit,
        'compliance_point': compliance_point,
        'title': f'Edit Compliance Point: {compliance_point.point_id}',
        'button_text': 'Update Point',
    }

    return render(request, 'audit_process/compliance_point_form.html', context)


@login_required
def delete_compliance_point(request, point_id):
    """
    View to delete a manual compliance point.
    """
    compliance_point = get_object_or_404(ManualCompliancePoint, id=point_id)
    audit = compliance_point.audit

    if request.method == 'POST':
        compliance_point.delete()
        messages.success(request, f"Compliance point {compliance_point.point_id} deleted successfully.")
        return redirect('manual_compliance_list', audit_id=audit.id)

    context = {
        'compliance_point': compliance_point,
        'audit': audit,
    }

    return render(request, 'audit_process/compliance_point_delete.html', context)


@login_required
def add_compliance_evidence(request, point_id):
    """
    View to add evidence for a compliance point.
    """
    compliance_point = get_object_or_404(ManualCompliancePoint, id=point_id)
    audit = compliance_point.audit

    if request.method == 'POST':
        form = ComplianceEvidenceForm(request.POST, request.FILES, compliance_point=compliance_point)
        if form.is_valid():
            evidence = form.save()
            messages.success(request, f"Evidence added successfully for compliance point {compliance_point.point_id}.")
            return redirect('manual_compliance_list', audit_id=audit.id)
    else:
        form = ComplianceEvidenceForm(compliance_point=compliance_point)

    context = {
        'form': form,
        'compliance_point': compliance_point,
        'audit': audit,
        'title': f'Add Evidence for Point: {compliance_point.point_id}',
        'button_text': 'Upload Evidence',
    }

    return render(request, 'audit_process/evidence_form.html', context)


@login_required
def delete_compliance_evidence(request, evidence_id):
    """
    View to delete evidence for a compliance point.
    """
    evidence = get_object_or_404(ComplianceEvidence, id=evidence_id)
    compliance_point = evidence.compliance_point
    audit = compliance_point.audit

    if request.method == 'POST':
        evidence.delete()
        messages.success(request, "Evidence deleted successfully.")
        return redirect('manual_compliance_list', audit_id=audit.id)

    context = {
        'evidence': evidence,
        'compliance_point': compliance_point,
        'audit': audit,
    }

    return render(request, 'audit_process/evidence_delete.html', context)


@login_required
def bulk_import_compliance_points(request, audit_id):
    """
    View to import multiple compliance points from a CSV file.
    """
    audit = get_object_or_404(OngoingAudit, id=audit_id)

    # Check if this is a manual audit
    if audit.audit_type != 'manual':
        messages.error(request, "This is not a manual audit. Compliance points are only available for manual audits.")
        return redirect('ongoing_audit_detail', pk=audit_id)

    if request.method == 'POST':
        if 'csv_file' not in request.FILES:
            messages.error(request, "Please select a CSV file to upload.")
            return redirect('bulk_import_compliance_points', audit_id=audit_id)

        csv_file = request.FILES['csv_file']

        # Check if file is a CSV
        if not csv_file.name.endswith('.csv'):
            messages.error(request, "File must be a CSV file.")
            return redirect('bulk_import_compliance_points', audit_id=audit_id)

        # Process the CSV file
        try:
            # Read the CSV file
            csv_data = csv_file.read().decode('utf-8')
            import csv
            from io import StringIO

            csv_reader = csv.reader(StringIO(csv_data))

            # Skip header row
            header = next(csv_reader)

            # Check if the CSV has the required columns
            required_columns = ["idPoint", "descriptionPoint", "commandExecuted", "expectedOutput", "actualOutput", "statut"]
            if not all(col in header for col in required_columns):
                messages.error(request, f"CSV file must contain the following columns: {', '.join(required_columns)}")
                return redirect('bulk_import_compliance_points', audit_id=audit_id)

            # Get column indices
            id_index = header.index("idPoint")
            desc_index = header.index("descriptionPoint")
            command_index = header.index("commandExecuted")
            expected_index = header.index("expectedOutput")
            actual_index = header.index("actualOutput")
            status_index = header.index("statut")

            # Process each row
            points_created = 0
            points_updated = 0

            for row in csv_reader:
                if len(row) < len(header):
                    continue  # Skip incomplete rows

                point_id = row[id_index].strip()
                description = row[desc_index].strip()
                command = row[command_index].strip()
                expected_result = row[expected_index].strip()
                actual_result = row[actual_index].strip()
                status = row[status_index].strip().lower()

                # Map status values
                if 'conforme' in status:
                    status = 'conforme'
                elif 'non conforme' in status or 'non-conforme' in status:
                    status = 'non_conforme'
                else:
                    status = 'not_checked'

                # Create or update the compliance point
                compliance_point, created = ManualCompliancePoint.objects.update_or_create(
                    audit=audit,
                    point_id=point_id,
                    defaults={
                        'description': description,
                        'command': command,
                        'expected_result': expected_result,
                        'actual_result': actual_result,
                        'status': status,
                    }
                )

                if created:
                    points_created += 1
                else:
                    points_updated += 1

            messages.success(request, f"Successfully imported {points_created} new compliance points and updated {points_updated} existing points.")
            return redirect('manual_compliance_list', audit_id=audit_id)

        except Exception as e:
            messages.error(request, f"Error processing CSV file: {str(e)}")
            return redirect('bulk_import_compliance_points', audit_id=audit_id)

    context = {
        'audit': audit,
        'title': 'Import Compliance Points from CSV',
    }

    return render(request, 'audit_process/bulk_import_form.html', context)


@login_required
def audit_db_os_list(request):
    """
    View to list all OS versions from the audit_db database.
    """
    # Import models here to avoid circular imports
    from .models_audit_db import VersionOS

    os_versions = VersionOS.objects.using('audit_db').all()

    context = {
        'os_versions': os_versions,
        'title': 'OS Versions',
    }

    return render(request, 'audit_process/audit_db_os_list.html', context)


@login_required
def audit_db_all_tables(request):
    """
    View to list all tables and their content from the audit_db database.
    """
    from django.db import connections

    tables_data = {}

    try:
        with connections['audit_db'].cursor() as cursor:
            # Get all tables in the database
            cursor.execute("""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
            ORDER BY table_name
            """)
            tables = [row[0] for row in cursor.fetchall()]
            logger.info(f"Tables in audit_db: {tables}")

            # For each table, get its structure and content
            for table in tables:
                # Get table structure
                cursor.execute(f"""
                SELECT column_name, data_type
                FROM information_schema.columns
                WHERE table_name = '{table}'
                ORDER BY ordinal_position
                """)
                columns = [(row[0], row[1]) for row in cursor.fetchall()]

                # Get table content (limit to 100 rows)
                cursor.execute(f"SELECT * FROM {table} LIMIT 100")
                rows = cursor.fetchall()

                # Store table data
                tables_data[table] = {
                    'columns': columns,
                    'rows': rows
                }
    except Exception as e:
        logger.error(f"Error fetching database tables: {str(e)}")
        messages.error(request, f"Error fetching database tables: {str(e)}")

    context = {
        'tables_data': tables_data,
        'title': 'All Database Tables',
    }

    return render(request, 'audit_process/audit_db_all_tables.html', context)


@login_required
def audit_db_points_list(request, os_id=None, version_id=None):
    """
    View to list all control points for a specific OS version and point version.
    """
    # Import models here to avoid circular imports
    from .models_audit_db import VersionOS, VersionPoint, PointControle

    os_versions = VersionOS.objects.using('audit_db').all()

    selected_os = None
    selected_version = None
    point_versions = []
    control_points = []

    if os_id:
        selected_os = get_object_or_404(VersionOS.objects.using('audit_db'), idversion=os_id)
        point_versions = VersionPoint.objects.using('audit_db').filter(idversion=selected_os)

        if version_id:
            selected_version = get_object_or_404(VersionPoint.objects.using('audit_db'), id=version_id)
            # Récupérer les points de contrôle associés à cette version
            version_point_ids = VersionPoint.objects.using('audit_db').filter(id=version_id).values_list('idpoint', flat=True)
            control_points = PointControle.objects.using('audit_db').filter(idpoint__in=version_point_ids)

    context = {
        'os_versions': os_versions,
        'selected_os': selected_os,
        'point_versions': point_versions,
        'selected_version': selected_version,
        'control_points': control_points,
        'title': 'Control Points',
    }

    return render(request, 'audit_process/audit_db_points_list.html', context)


@login_required
def audit_db_import_points(request, audit_id, version_id):
    """
    View to import control points from the audit_db database to a manual audit.
    """
    # Import models here to avoid circular imports
    from .models_audit_db import VersionPoint, PointControle, Preuve

    audit = get_object_or_404(OngoingAudit, id=audit_id)
    version = get_object_or_404(VersionPoint.objects.using('audit_db'), id=version_id)

    # Check if this is a manual audit
    if audit.audit_type != 'manual':
        messages.error(request, "This is not a manual audit. Control points can only be imported for manual audits.")
        return redirect('ongoing_audit_detail', pk=audit_id)

    if request.method == 'POST':
        # Get selected control points
        selected_points = request.POST.getlist('control_points')

        if not selected_points:
            messages.error(request, "Please select at least one control point to import.")
            return redirect('audit_db_import_points', audit_id=audit_id, version_id=version_id)

        # Import selected control points
        points_created = 0

        for point_id in selected_points:
            control_point = get_object_or_404(PointControle.objects.using('audit_db'), idpoint=point_id)

            # Create a new manual compliance point
            compliance_point, created = ManualCompliancePoint.objects.get_or_create(
                audit=audit,
                point_id=control_point.idpoint,
                defaults={
                    'description': control_point.descriptionpoint,
                    'command': control_point.commandeaudit,
                    'expected_result': control_point.expectedoutput,
                    'status': 'not_checked',
                }
            )

            if created:
                points_created += 1

                # Create a new preuve in the audit_db database
                Preuve.objects.using('audit_db').create(
                    idpoint=control_point,
                    audit_id=audit.id,
                    statut='not_checked'
                )

        messages.success(request, f"Successfully imported {points_created} control points.")
        return redirect('manual_compliance_list', audit_id=audit_id)

    # Get control points for the selected version
    control_points = PointControle.objects.using('audit_db').filter(version_point=version)

    context = {
        'audit': audit,
        'version': version,
        'control_points': control_points,
        'title': f'Import Control Points from {version.name}',
    }

    return render(request, 'audit_process/audit_db_import_points.html', context)


@login_required
def audit_db_add_preuve(request, audit_id, point_id):
    """
    View to add evidence for a control point in the audit_db database.
    """
    # Import models here to avoid circular imports
    from .models_audit_db import PointControle, Preuve

    audit = get_object_or_404(OngoingAudit, id=audit_id)
    compliance_point = get_object_or_404(ManualCompliancePoint, id=point_id)

    # Find the corresponding control point in the audit_db database
    control_point = None
    try:
        control_point = PointControle.objects.using('audit_db').get(idpoint=compliance_point.point_id)
    except PointControle.DoesNotExist:
        messages.error(request, f"Control point {compliance_point.point_id} not found in the audit database.")
        return redirect('manual_compliance_list', audit_id=audit_id)

    # Find or create the preuve in the audit_db database
    try:
        preuve = Preuve.objects.using('audit_db').get(idpoint=control_point, audit_id=audit.id)
    except Preuve.DoesNotExist:
        preuve = Preuve.objects.using('audit_db').create(
            idpoint=control_point,
            audit_id=audit.id,
            statut='not_checked'
        )

    if request.method == 'POST':
        # Update the preuve
        actual_result = request.POST.get('actual_result', '')
        status = request.POST.get('status', 'not_checked')
        notes = request.POST.get('notes', '')

        preuve.actualoutput = actual_result
        preuve.statut = status
        preuve.notes = notes

        # Handle file upload
        if 'evidence_file' in request.FILES:
            file = request.FILES['evidence_file']

            # Check if file is an image
            import imghdr
            import os
            from django.conf import settings

            # Get the file content type
            content_type = file.content_type
            is_image = content_type.startswith('image/')

            if not is_image:
                # Try to determine if it's an image using imghdr
                file_data = file.read()
                file.seek(0)  # Reset file pointer after reading
                image_type = imghdr.what(None, file_data)
                is_image = image_type is not None

            if not is_image:
                messages.error(request, "The uploaded file is not an image. Please upload a screenshot (PNG, JPG, etc.).")
                return redirect('audit_db_add_preuve', audit_id=audit.id, point_id=point_id)

            # Generate a unique filename to avoid overwriting
            import uuid
            filename = f"{uuid.uuid4()}_{file.name}"

            # Save the file to the media directory
            file_path = os.path.join('compliance_evidence', f"audit_{audit.id}", f"point_{compliance_point.point_id}", filename)
            full_path = os.path.join(settings.MEDIA_ROOT, file_path)

            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(full_path), exist_ok=True)

            # Save the file
            with open(full_path, 'wb+') as destination:
                for chunk in file.chunks():
                    destination.write(chunk)

            # Update the preuve with the file path
            preuve.screenshot_path = file_path

        # Save the preuve
        preuve.save(using='audit_db')

        # Update the compliance point
        compliance_point.actual_result = actual_result
        compliance_point.status = status
        compliance_point.save()

        messages.success(request, f"Evidence added successfully for control point {compliance_point.point_id}.")
        return redirect('manual_compliance_list', audit_id=audit_id)

    context = {
        'audit': audit,
        'compliance_point': compliance_point,
        'control_point': control_point,
        'preuve': preuve,
        'title': f'Add Evidence for Point: {compliance_point.point_id}',
    }

    return render(request, 'audit_process/audit_db_add_preuve.html', context)
