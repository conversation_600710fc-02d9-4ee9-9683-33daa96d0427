{% extends "equipment/base.html" %}
{% load static %}

{% block title %}Script Generated Reports{% endblock title %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'audit_reports/css/dashboard.css' %}">
<style>
    .action-card {
        transition: transform 0.3s;
        height: 100%;
    }
    .action-card:hover {
        transform: translateY(-5px);
    }
    .action-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }
    .action-description {
        font-size: 0.9rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container dashboard-container">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="text-success">Script Generated Reports</h2>
            <p class="text-muted">Manage audit scripts and reports for your equipment.</p>
        </div>
    </div>

    <!-- Main Action Cards -->
    <div class="row mb-5 justify-content-center">
        <div class="col-md-5 mb-3">
            <div class="card action-card shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="action-icon text-warning">
                        <i class="fa-solid fa-code"></i>
                    </div>
                    <h5 class="card-title">Manage Scripts</h5>
                    <p class="action-description mb-4">Add, edit or delete audit scripts used for equipment auditing.</p>
                    <a href="{% url 'audit_script_list' %}" class="btn btn-warning w-100">
                        <i class="fa-solid fa-code me-2"></i> Manage Scripts
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-5 mb-3">
            <div class="card action-card shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="action-icon text-primary">
                        <i class="fa-solid fa-file-csv"></i>
                    </div>
                    <h5 class="card-title">Upload CSV</h5>
                    <p class="action-description mb-4">Upload CSV data to update compliance information for a report.</p>
                    <a href="{% url 'csv_upload' %}" class="btn btn-primary w-100">
                        <i class="fa-solid fa-file-csv me-2"></i> Upload CSV
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Client Information Alert -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle me-2"></i> Information for Clients</h5>
                <p>Clients can view detailed compliance information and download CSV files from their dashboard. When you upload a CSV file, clients will be automatically notified about the updated compliance data.</p>
            </div>
        </div>
    </div>

    <!-- Reports Table -->
    <div class="row">
        <div class="col-12">
            <div class="card dashboard-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Available Audit Reports</h5>
                    <a href="{% url 'audit_process_info' %}" class="btn btn-sm btn-outline-info">
                        <i class="fas fa-info-circle me-1"></i> How Audit Scripts Work
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table table-striped table-hover dashboard-table">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Date</th>
                                    <th>Equipment</th>
                                    <th>Compliance</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for report in reports %}
                                <tr>
                                    <td>{{ report.title }}</td>
                                    <td>{{ report.date_created|date:"Y-m-d" }}</td>
                                    <td>{{ report.equipment.name }}</td>
                                    <td>
                                        <div class="progress compliance-progress">
                                            <div class="progress-bar {% if report.is_compliant %}bg-success{% else %}bg-danger{% endif %}"
                                                 role="progressbar"
                                                 style="width: {{ report.compliance_score }}%;"
                                                 aria-valuenow="{{ report.compliance_score }}"
                                                 aria-valuemin="0"
                                                 aria-valuemax="100">
                                                {{ report.compliance_score }}%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if report.status == 'pending' %}
                                            <span class="badge bg-warning status-badge">Pending</span>
                                        {% elif report.status == 'approved' %}
                                            <span class="badge bg-success status-badge">Approved</span>
                                        {% elif report.status == 'rejected' %}
                                            <span class="badge bg-danger status-badge">Rejected</span>
                                        {% elif report.status == 'archived' %}
                                            <span class="badge bg-secondary status-badge">Archived</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'script_report_detail' report.slug %}" class="btn btn-sm btn-info action-btn" title="View Report">
                                                <i class="fa-solid fa-eye"></i>
                                            </a>
                                            <a href="{{ report.report_file.url }}" class="btn btn-sm btn-primary action-btn" download title="Download Report">
                                                <i class="fa-solid fa-download"></i>
                                            </a>
                                            <a href="{% url 'csv_upload' %}?report_id={{ report.id }}" class="btn btn-sm btn-success action-btn" title="Upload CSV Data">
                                                <i class="fa-solid fa-file-csv"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center">No script-generated reports found.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}
