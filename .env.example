# Django settings
DEBUG=True
SECRET_KEY=change_this_to_a_secure_random_string_in_production
DJANGO_ALLOWED_HOSTS=localhost 127.0.0.1 [::1]

# Database settings
DB_ENGINE=django.db.backends.postgresql
DB_NAME=auditconfig
DB_USER=audituser
DB_PASSWORD=change_this_password_in_production
DB_HOST=db
DB_PORT=5432

# Email settings
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_email_password
DEFAULT_FROM_EMAIL=<EMAIL>

# Superuser settings (optional, for automatic creation)
DJANGO_SUPERUSER_USERNAME=admin
DJANGO_SUPERUSER_EMAIL=<EMAIL>
DJANGO_SUPERUSER_PASSWORD=change_this_password_in_production
