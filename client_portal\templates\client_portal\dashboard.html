{% extends 'client_portal/base.html' %}

{% block title %}Client Dashboard{% endblock %}
{% block page_title %}Dashboard{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Welcome, {{ user.get_full_name|default:user.username }}!</h5>
                </div>
                <div class="card-body">
                    <p>Welcome to your client dashboard. Here you can manage your audit requests, view audit reports, and more.</p>
                    <a href="{% url 'client_create_audit_request' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i> Create New Audit Request
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Recent Audit Requests -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Recent Audit Requests</h5>
                    <a href="{% url 'client_audit_request_list' %}" class="btn btn-sm btn-outline-light">View All</a>
                </div>
                <div class="card-body">
                    {% if audit_requests %}
                    <div class="list-group">
                        {% for request in audit_requests %}
                        <a href="{% url 'client_audit_request_detail' request.id %}" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">Audit Request #{{ request.id }}</h6>
                                <small>{{ request.date|date:"M d, Y" }}</small>
                            </div>
                            <p class="mb-1">Items: {{ request.details.count }}</p>
                        </a>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted">No audit requests found.</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Recent Audit Reports -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Recent Audit Reports</h5>
                    <a href="{% url 'client_audit_report_list' %}" class="btn btn-sm btn-outline-light">View All</a>
                </div>
                <div class="card-body">
                    {% if audit_reports %}
                    <div class="list-group">
                        {% for report in audit_reports %}
                        <a href="{% url 'client_audit_report_detail' report.slug %}" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">{{ report.report_name }}</h6>
                                <small>{{ report.date|date:"M d, Y" }}</small>
                            </div>
                            <p class="mb-1">
                                Result: 
                                {% if report.result %}
                                <span class="badge bg-success">Pass</span>
                                {% else %}
                                <span class="badge bg-danger">Fail</span>
                                {% endif %}
                            </p>
                        </a>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted">No audit reports found.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Notifications -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Recent Notifications</h5>
                    <a href="{% url 'client_notifications' %}" class="btn btn-sm btn-outline-light">View All</a>
                </div>
                <div class="card-body">
                    {% if notifications %}
                    <div class="list-group">
                        {% for notification in notifications %}
                        <a href="{% url 'client_mark_notification_read' notification.id %}" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">{{ notification.title }}</h6>
                                <small>{{ notification.created_at|date:"M d, Y" }}</small>
                            </div>
                            <p class="mb-1">{{ notification.message|truncatechars:100 }}</p>
                            <small>
                                {% if notification.notification_type == 'audit_request' %}
                                <span class="badge bg-primary">Audit Request</span>
                                {% elif notification.notification_type == 'audit_report' %}
                                <span class="badge bg-info">Audit Report</span>
                                {% else %}
                                <span class="badge bg-secondary">System</span>
                                {% endif %}
                            </small>
                        </a>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted">No unread notifications.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
