{% extends 'client_portal/volt_base.html' %}
{% load crispy_forms_tags %}
{% load static %}

{% block title %}My Profile{% endblock %}

{% block extra_css %}
<style>
    .profile-image-container {
        position: relative;
        display: inline-block;
        border-radius: 50%;
        overflow: hidden;
        cursor: pointer;
    }

    .profile-image {
        object-fit: cover;
        transition: filter 0.3s;
    }

    .profile-image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: white;
        opacity: 0;
        transition: opacity 0.3s;
    }

    .profile-image-container:hover .profile-image {
        filter: blur(2px);
    }

    .profile-image-container:hover .profile-image-overlay {
        opacity: 1;
    }

    .profile-image-overlay i {
        font-size: 24px;
        margin-bottom: 5px;
    }

    .custom-file-upload {
        display: none;
    }

    /* Hide the file input but make it accessible */
    input[type="file"] {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        border: 0;
    }
</style>
{% endblock %}
{% block page_title %}My Profile{% endblock %}

{% block content %}
<div class="py-4">
    <div class="row">
        <div class="col-12 col-xl-8">
            <div class="card border-0 shadow">
                <div class="card-header">
                    <h5 class="mb-0">Profile Information</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-12 mb-4">
                                <div class="text-center">
                                    <div class="profile-image-container mb-3">
                                        <img src="{{ user.client_profile.image_url }}" class="rounded-circle profile-image" alt="Profile Picture" width="150" height="150">
                                        <div class="profile-image-overlay">
                                            <i class="fas fa-camera"></i>
                                            <span>Change</span>
                                        </div>
                                    </div>
                                    <div class="custom-file-upload">
                                        {{ form.profile_picture }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                {{ form.first_name|as_crispy_field }}
                            </div>
                            <div class="col-md-6 mb-3">
                                {{ form.last_name|as_crispy_field }}
                            </div>
                            <div class="col-md-12 mb-3">
                                {{ form.email|as_crispy_field }}
                            </div>
                            <div class="col-md-12 mb-3">
                                {{ form.company_name|as_crispy_field }}
                            </div>
                            <div class="col-md-12 mb-3">
                                {{ form.address|as_crispy_field }}
                            </div>
                            <div class="col-md-12 mb-3">
                                {{ form.phone|as_crispy_field }}
                            </div>
                        </div>
                        <div class="mt-3">
                            <button type="submit" class="btn btn-primary">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-12 col-xl-4">
            <div class="card border-0 shadow">
                <div class="card-header">
                    <h5 class="mb-0">Account Information</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-4">
                        <div class="avatar-lg me-4">
                            <img src="{{ user.client_profile.image_url }}" class="card-img-top rounded-circle border-white" alt="Profile Picture">
                        </div>
                        <div>
                            <h5 class="mb-1">{{ user.get_full_name|default:user.username }}</h5>
                            <p class="text-muted mb-0">{{ user.email }}</p>
                        </div>
                    </div>

                    <div class="list-group list-group-flush">
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-0">Username</h6>
                                    <small class="text-muted">Your login username</small>
                                </div>
                                <span>{{ user.username }}</span>
                            </div>
                        </div>
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-0">Email</h6>
                                    <small class="text-muted">Your contact email</small>
                                </div>
                                <span>{{ user.email }}</span>
                            </div>
                        </div>
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-0">Account Type</h6>
                                    <small class="text-muted">Your account type</small>
                                </div>
                                <span class="badge bg-primary">Client</span>
                            </div>
                        </div>
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-0">Date Joined</h6>
                                    <small class="text-muted">When you created your account</small>
                                </div>
                                <span>{{ user.date_joined|date:"M d, Y" }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <a href="{% url 'client_password_change' %}" class="btn btn-tertiary">Change Password</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle profile picture upload
        const profileImageContainer = document.querySelector('.profile-image-container');
        const fileInput = document.querySelector('input[type="file"]');

        if (profileImageContainer && fileInput) {
            // Click on profile image to trigger file input
            profileImageContainer.addEventListener('click', function() {
                fileInput.click();
            });

            // Preview image when selected
            fileInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        document.querySelector('.profile-image').src = e.target.result;
                    };
                    reader.readAsDataURL(this.files[0]);
                }
            });
        }
    });
</script>
{% endblock %}