import os
from django.core.management.base import BaseCommand
from django.db import connections


class Command(BaseCommand):
    help = 'Setup the audit_db database and tables'

    def handle(self, *args, **options):
        # Créer la base de données audit_db si elle n'existe pas
        self.create_database()
        
        # Créer les tables dans la base de données audit_db
        self.create_tables()
        
        # Ajouter des données de test
        self.add_test_data()
        
        self.stdout.write(self.style.SUCCESS('Successfully setup audit_db database and tables'))

    def create_database(self):
        """
        Créer la base de données audit_db si elle n'existe pas
        """
        with connections['default'].cursor() as cursor:
            # Vérifier si la base de données existe
            cursor.execute("SELECT 1 FROM pg_database WHERE datname = 'audit_db'")
            exists = cursor.fetchone()
            
            if not exists:
                self.stdout.write('Creating audit_db database...')
                # Fermer toutes les connexions à la base de données
                cursor.execute("SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = 'audit_db'")
                # Créer la base de données
                cursor.execute("CREATE DATABASE audit_db WITH OWNER = audituser")
                self.stdout.write(self.style.SUCCESS('Created audit_db database'))
            else:
                self.stdout.write('audit_db database already exists')

    def create_tables(self):
        """
        Créer les tables dans la base de données audit_db
        """
        with connections['audit_db'].cursor() as cursor:
            # Vérifier si les tables existent
            cursor.execute("SELECT 1 FROM information_schema.tables WHERE table_name = 'version_os'")
            exists = cursor.fetchone()
            
            if not exists:
                self.stdout.write('Creating tables in audit_db database...')
                
                # Créer la table version_os
                cursor.execute("""
                CREATE TABLE version_os (
                    id_version_os SERIAL PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)
                
                # Créer la table version_point
                cursor.execute("""
                CREATE TABLE version_point (
                    id_version_point SERIAL PRIMARY KEY,
                    id_version_os INTEGER REFERENCES version_os(id_version_os),
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)
                
                # Créer la table point_controle
                cursor.execute("""
                CREATE TABLE point_controle (
                    id_point_controle SERIAL PRIMARY KEY,
                    id_version_point INTEGER REFERENCES version_point(id_version_point),
                    point_id VARCHAR(20) NOT NULL,
                    description TEXT NOT NULL,
                    command TEXT,
                    expected_result TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)
                
                # Créer la table preuve
                cursor.execute("""
                CREATE TABLE preuve (
                    id_preuve SERIAL PRIMARY KEY,
                    id_point_controle INTEGER REFERENCES point_controle(id_point_controle),
                    audit_id INTEGER NOT NULL,
                    actual_result TEXT,
                    status VARCHAR(20) DEFAULT 'not_checked',
                    file_path VARCHAR(255),
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)
                
                self.stdout.write(self.style.SUCCESS('Created tables in audit_db database'))
            else:
                self.stdout.write('Tables already exist in audit_db database')

    def add_test_data(self):
        """
        Ajouter des données de test dans les tables
        """
        with connections['audit_db'].cursor() as cursor:
            # Vérifier s'il y a déjà des données dans la table version_os
            cursor.execute("SELECT COUNT(*) FROM version_os")
            count = cursor.fetchone()[0]
            
            if count == 0:
                self.stdout.write('Adding test data to audit_db database...')
                
                # Ajouter des versions d'OS
                cursor.execute("""
                INSERT INTO version_os (name, description) VALUES
                ('Windows 10', 'Microsoft Windows 10 Enterprise'),
                ('Windows 11', 'Microsoft Windows 11 Enterprise'),
                ('Ubuntu 22.04', 'Ubuntu 22.04 LTS (Jammy Jellyfish)'),
                ('CentOS 8', 'CentOS Linux 8')
                RETURNING id_version_os
                """)
                os_ids = cursor.fetchall()
                
                # Ajouter des versions de points pour chaque OS
                for os_id in os_ids:
                    cursor.execute(f"""
                    INSERT INTO version_point (id_version_os, name, description) VALUES
                    ({os_id[0]}, 'Base', 'Points de contrôle de base'),
                    ({os_id[0]}, 'Sécurité', 'Points de contrôle de sécurité'),
                    ({os_id[0]}, 'Performance', 'Points de contrôle de performance')
                    RETURNING id_version_point
                    """)
                    point_ids = cursor.fetchall()
                    
                    # Ajouter des points de contrôle pour chaque version de point
                    for i, point_id in enumerate(point_ids):
                        prefix = 'BASE' if i == 0 else 'SEC' if i == 1 else 'PERF'
                        for j in range(1, 6):  # 5 points par version
                            cursor.execute(f"""
                            INSERT INTO point_controle (id_version_point, point_id, description, command, expected_result) VALUES
                            ({point_id[0]}, '{prefix}-{j:03d}', 'Point de contrôle {prefix}-{j:03d}', 'commande {j}', 'résultat attendu {j}')
                            """)
                
                self.stdout.write(self.style.SUCCESS('Added test data to audit_db database'))
            else:
                self.stdout.write('Test data already exists in audit_db database')
