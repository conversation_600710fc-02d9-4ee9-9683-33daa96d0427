import React, { useState, useEffect } from 'react';
import <PERSON>actD<PERSON> from 'react-dom';
import axios from 'axios';

// Set up CSRF token for all axios requests
function getCookie(name) {
  let cookieValue = null;
  if (document.cookie && document.cookie !== '') {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.substring(0, name.length + 1) === (name + '=')) {
        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
        break;
      }
    }
  }
  return cookieValue;
}

const csrftoken = getCookie('csrftoken');
axios.defaults.headers.common['X-CSRFToken'] = csrftoken;

const DashboardWidgets = () => {
  const [stats, setStats] = useState({
    totalAudits: 0,
    pendingAudits: 0,
    completedAudits: 0,
    clientCount: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  

  useEffect(() => {
    // Function to fetch dashboard data
    const fetchDashboardData = async () => {
      try {
        // Fetch data from our Django API endpoint
        console.log('Fetching dashboard data from API...');
        // Use only the working URL
        const response = await axios.get('/api/dashboard/stats/');
        console.log('API response:', response.data);
        setStats(response.data);
        setLoading(false);

        // Dispatch a custom event to signal that the dashboard has loaded with data
        const event = new CustomEvent('reactDashboardLoaded', {
          detail: response.data
        });
        window.dispatchEvent(event);
      } catch (err) {
        // Fallback to dummy data if API fails
        console.error('Error fetching dashboard data:', err);
        console.log('Using fallback data');

        // Use the fallback data
        const fallbackData = {
          totalAudits: 156,
          pendingAudits: 23,
          completedAudits: 133,
          clientCount: 45
        };

        setStats(fallbackData);
        setLoading(false);

        // Dispatch a custom event to signal that the dashboard has loaded with fallback data
        const event = new CustomEvent('reactDashboardLoaded', {
          detail: fallbackData
        });
        window.dispatchEvent(event);
      }
    };

    fetchDashboardData();
  }, []);

  // Don't render anything - let the traditional stats show
  // Check if we have any meaningful data
  const hasData = stats.totalAudits > 0 || stats.pendingAudits > 0 ||
                  stats.completedAudits > 0 || stats.clientCount > 0;

  // If we're loading, have an error, or don't have meaningful data, don't render anything
  if (loading || error || !hasData) return null;

  return (
    <div className="dashboard-widgets">
      <div className="row g-6 mb-6">
        <div className="col-xl-3 col-sm-6 col-12">
          <a href="/audit-process/ongoing-audits/" style={{ textDecoration: 'none' }}>
            <div className="card shadow border-0">
              <div className="card-body">
                <div className="row">
                  <div className="col">
                    <span className="h6 font-bold text-dark text-sm d-block mb-2">Total Audits</span>
                    <span className="h3 font-bold mb-0">{stats.totalAudits || 0}</span>
                  </div>
                  <div className="col-auto">
                    <div className="icon icon-shape bg-tertiary text-white text-lg rounded-circle">
                      <i className="fa fa-clipboard-list"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
        <div className="col-xl-3 col-sm-6 col-12">
          <a href="/audit-process/ongoing-audits/?audit_status=P" style={{ textDecoration: 'none' }}>
            <div className="card shadow border-0">
              <div className="card-body">
                <div className="row">
                  <div className="col">
                    <span className="h6 font-bold text-dark text-sm d-block mb-2">Pending Audits</span>
                    <span className="h3 font-bold mb-0">{stats.pendingAudits || 0}</span>
                  </div>
                  <div className="col-auto">
                    <div className="icon icon-shape bg-primary text-white text-lg rounded-circle">
                      <i className="fa fa-clock"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
        <div className="col-xl-3 col-sm-6 col-12">
          <a href="/audit-process/ongoing-audits/?audit_status=S" style={{ textDecoration: 'none' }}>
            <div className="card shadow border-0">
              <div className="card-body">
                <div className="row">
                  <div className="col">
                    <span className="h6 font-bold text-dark text-sm d-block mb-2">Completed Audits</span>
                    <span className="h3 font-bold mb-0">{stats.completedAudits || 0}</span>
                  </div>
                  <div className="col-auto">
                    <div className="icon icon-shape bg-info text-white text-lg rounded-circle">
                      <i className="fa fa-check-circle"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
        <div className="col-xl-3 col-sm-6 col-12">
          <a href="/accounts/profiles/" style={{ textDecoration: 'none' }}>
            <div className="card shadow border-0">
              <div className="card-body">
                <div className="row">
                  <div className="col">
                    <span className="h6 font-bold text-dark text-sm d-block mb-2">Total Clients</span>
                    <span className="h3 font-bold mb-0">{stats.clientCount || 0}</span>
                  </div>
                  <div className="col-auto">
                    <div className="icon icon-shape bg-primary text-white text-lg rounded-circle">
                      <i className="fa fa-users"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
      </div>
    </div>
  );
};

// Find all dashboard widget mount points in the page
document.addEventListener('DOMContentLoaded', () => {
  const dashboardRoot = document.getElementById('dashboard-widgets');
  if (dashboardRoot) {
    ReactDOM.render(<DashboardWidgets />, dashboardRoot);
  }
});
