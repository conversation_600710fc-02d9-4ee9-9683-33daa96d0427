# Generated by Django 4.2.9 on 2025-04-25 16:08

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("audit_requests", "0001_initial"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="auditrequest",
            options={
                "ordering": ["-date"],
                "verbose_name": "Client Audit Request",
                "verbose_name_plural": "Client Audit Requests",
            },
        ),
        migrations.RemoveField(
            model_name="auditrequest",
            name="client_name",
        ),
        migrations.RemoveField(
            model_name="auditrequest",
            name="equipment_details",
        ),
        migrations.RemoveField(
            model_name="auditrequest",
            name="estimated_fee",
        ),
        migrations.AddField(
            model_name="auditrequest",
            name="amount",
            field=models.FloatField(
                blank=True,
                help_text="Amount for the audit (optional)",
                null=True,
                verbose_name="Amount",
            ),
        ),
        migrations.AddField(
            model_name="auditrequest",
            name="institution_name",
            field=models.<PERSON>r<PERSON><PERSON>(
                default="Unknown Institution",
                max_length=30,
                verbose_name="Institution Name",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="auditrequest",
            name="payment_details",
            field=models.CharField(
                blank=True,
                help_text="Payment details for the audit (optional)",
                max_length=255,
                null=True,
            ),
        ),
        migrations.AlterModelTable(
            name="auditrequest",
            table="client_audit_requests",
        ),
    ]
