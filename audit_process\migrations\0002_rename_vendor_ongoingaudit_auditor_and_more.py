# Generated by Django 4.2.9 on 2025-04-25 16:08

from django.db import migrations, models
import django_extensions.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ("audit_process", "0001_initial"),
    ]

    operations = [
        migrations.RenameField(
            model_name="ongoingaudit",
            old_name="vendor",
            new_name="auditor",
        ),
        migrations.AlterField(
            model_name="auditrequest",
            name="total",
            field=models.DecimalField(
                blank=True, decimal_places=2, default=0.0, max_digits=10, null=True
            ),
        ),
        migrations.AlterField(
            model_name="auditrequestdetail",
            name="price",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=10, null=True
            ),
        ),
        migrations.AlterField(
            model_name="auditrequestdetail",
            name="quantity",
            field=models.PositiveIntegerField(default=1),
        ),
        migrations.AlterField(
            model_name="auditrequestdetail",
            name="total_detail",
            field=models.DecimalField(
                blank=True, decimal_places=2, default=0.0, max_digits=10, null=True
            ),
        ),
        migrations.AlterField(
            model_name="ongoingaudit",
            name="price",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0.0,
                max_digits=10,
                null=True,
                verbose_name="Audit Fee (optional)",
            ),
        ),
        migrations.AlterField(
            model_name="ongoingaudit",
            name="slug",
            field=django_extensions.db.fields.AutoSlugField(
                blank=True, editable=False, populate_from="auditor", unique=True
            ),
        ),
        migrations.AlterField(
            model_name="ongoingaudit",
            name="total_value",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=10, null=True
            ),
        ),
    ]
