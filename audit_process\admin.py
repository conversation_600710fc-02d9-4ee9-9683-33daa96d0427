from django.contrib import admin
from .models import AuditRequest, AuditRequestDetail, OngoingAudit, AuditScript, AuditCategory


@admin.register(AuditRequest)
class AuditRequestAdmin(admin.ModelAdmin):
    """
    Admin interface configuration for the AuditRequest model.
    """
    list_display = (
        'id',
        'customer',  # This field is still named 'customer' in the model
        'date_added',
        'total'
    )
    search_fields = ('customer__name', 'id')
    list_filter = ('date_added', 'customer')
    ordering = ('-date_added',)
    readonly_fields = ('date_added',)
    date_hierarchy = 'date_added'

    def save_model(self, request, obj, form, change):
        """
        Save the AuditRequest instance, overriding the default save behavior.
        """
        super().save_model(request, obj, form, change)


@admin.register(AuditRequestDetail)
class AuditRequestDetailAdmin(admin.ModelAdmin):
    """
    Admin interface configuration for the AuditRequestDetail model.
    """
    list_display = (
        'id',
        'audit_request',
        'item',
        'price',
        'quantity',
        'total_detail'
    )
    search_fields = ('audit_request__id', 'item__name')
    list_filter = ('audit_request', 'item')
    ordering = ('audit_request', 'item')

    def save_model(self, request, obj, form, change):
        """
        Save the AuditRequestDetail instance, overriding the default save behavior.
        """
        super().save_model(request, obj, form, change)


@admin.register(OngoingAudit)
class OngoingAuditAdmin(admin.ModelAdmin):
    """
    Admin interface configuration for the OngoingAudit model.
    """
    list_display = (
        'slug',
        'item',
        'auditor',  # Renamed from vendor for clarity
        'start_date',
        'completion_date',
        'quantity',
        'price',
        'total_value',
        'audit_status'
    )
    search_fields = ('item__name', 'auditor__name', 'slug')
    list_filter = ('start_date', 'auditor', 'audit_status')
    ordering = ('-start_date',)
    readonly_fields = ('total_value',)

    def save_model(self, request, obj, form, change):
        """
        Save the OngoingAudit instance and compute the total value.
        """
        if obj.price is not None and obj.quantity is not None:
            obj.total_value = obj.price * obj.quantity
        super().save_model(request, obj, form, change)


@admin.register(AuditScript)
class AuditScriptAdmin(admin.ModelAdmin):
    """
    Admin interface configuration for the AuditScript model.
    """
    list_display = (
        'name',
        'version',
        'category',
        'date_added',
        'is_active'
    )
    search_fields = ('name', 'description')
    list_filter = ('category', 'is_active', 'date_added')
    ordering = ('-date_added',)


@admin.register(AuditCategory)
class AuditCategoryAdmin(admin.ModelAdmin):
    """
    Admin interface configuration for the AuditCategory model.
    """
    list_display = ('name', 'description')
    search_fields = ('name', 'description')
