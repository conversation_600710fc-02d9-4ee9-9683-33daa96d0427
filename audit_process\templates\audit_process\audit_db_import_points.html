{% extends "equipment/base.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>{{ title }}</h2>
                <div>
                    <a href="{% url 'audit_db_points_list_version' os_id=version.version_os.id version_id=version.id %}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i> Back to Control Points
                    </a>
                    <a href="{% url 'manual_compliance_list' audit_id=audit.id %}" class="btn btn-primary">
                        <i class="fas fa-clipboard-list me-2"></i> View Audit Compliance Points
                    </a>
                </div>
            </div>
            <p class="text-muted">
                Audit: <strong>{{ audit.item.name }}</strong> |
                OS: <strong>{{ version.idversion.nomversion }}</strong> |
                Version: <strong>Version Point {{ version.id }}</strong>
            </p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Select Control Points to Import</h5>
                </div>
                <div class="card-body">
                    {% if control_points %}
                    <form method="POST">
                        {% csrf_token %}

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="selectAll">
                                <label class="form-check-label" for="selectAll">
                                    <strong>Select All</strong>
                                </label>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th style="width: 50px;"></th>
                                        <th>ID</th>
                                        <th>Description</th>
                                        <th>Command</th>
                                        <th>Expected Result</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for point in control_points %}
                                    <tr>
                                        <td>
                                            <div class="form-check">
                                                <input class="form-check-input control-point-checkbox" type="checkbox" name="control_points" value="{{ point.id }}" id="point_{{ point.id }}">
                                                <label class="form-check-label" for="point_{{ point.id }}"></label>
                                            </div>
                                        </td>
                                        <td><strong>{{ point.idpoint }}</strong></td>
                                        <td>{{ point.descriptionpoint }}</td>
                                        <td>
                                            {% if point.commandeaudit %}
                                            <code>{{ point.commandeaudit }}</code>
                                            {% else %}
                                            <span class="text-muted">N/A</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ point.expectedoutput|default:"N/A" }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <div class="d-flex justify-content-end mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-file-import me-2"></i> Import Selected Points
                            </button>
                        </div>
                    </form>
                    {% else %}
                    <div class="alert alert-info">
                        <p class="mb-0">No control points found for this version. Please contact the administrator to add control points.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Select All checkbox
        const selectAllCheckbox = document.getElementById('selectAll');
        const controlPointCheckboxes = document.querySelectorAll('.control-point-checkbox');

        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const isChecked = this.checked;

                controlPointCheckboxes.forEach(function(checkbox) {
                    checkbox.checked = isChecked;
                });
            });
        }

        // Update Select All checkbox when individual checkboxes change
        controlPointCheckboxes.forEach(function(checkbox) {
            checkbox.addEventListener('change', function() {
                const allChecked = Array.from(controlPointCheckboxes).every(function(cb) {
                    return cb.checked;
                });

                const anyChecked = Array.from(controlPointCheckboxes).some(function(cb) {
                    return cb.checked;
                });

                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = allChecked;
                    selectAllCheckbox.indeterminate = anyChecked && !allChecked;
                }
            });
        });
    });
</script>
{% endblock %}
{% endblock %}
