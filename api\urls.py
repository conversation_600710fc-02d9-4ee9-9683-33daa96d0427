from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView

from .views import (
    CustomTokenObtainPairView,
    RegisterView,
    get_user_profile,
    get_user_data,
    logout_view,
    UserListView,
    ProfileListView,
    AuditorListView,
    ClientListView
)

urlpatterns = [
    # Authentication endpoints
    path('token/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('register/', RegisterView.as_view(), name='register'),
    path('logout/', logout_view, name='logout'),
    
    # User data endpoints
    path('user/profile/', get_user_profile, name='user_profile'),
    path('user/data/', get_user_data, name='user_data'),
    
    # List endpoints
    path('users/', UserListView.as_view(), name='user_list'),
    path('profiles/', ProfileListView.as_view(), name='profile_list'),
    path('auditors/', AuditorListView.as_view(), name='auditor_list'),
    path('clients/', ClientListView.as_view(), name='client_list'),
]
