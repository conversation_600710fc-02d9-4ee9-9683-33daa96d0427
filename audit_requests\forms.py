"""
Module: forms.py

Contains Django forms for handling audit requests.
"""

from django import forms
from .models import AuditRequest as ClientAuditRequest  # Renamed to avoid confusion
from equipment.models import Category, Item
from audit_process.models import AuditScript
from accounts.models import Profile

class AuditRequestForm(forms.ModelForm):
    """
    Form for creating and updating audit requests.
    """
    # Add fields that are used in the template but not in the model
    title = forms.CharField(
        max_length=100,
        required=True,
        help_text='Title of the audit request',
        widget=forms.TextInput(attrs={'placeholder': 'Enter a title for your audit request'})
    )
    categories = forms.ChoiceField(
        choices=[],  # Will be populated in __init__
        required=True,
        help_text='Select a category for the audit',
        widget=forms.Select(attrs={'class': 'form-control', 'required': 'required'})
    )
    config_file = forms.FileField(
        required=False,
        help_text='Upload a configuration file for the audit (optional)',
        widget=forms.ClearableFileInput(attrs={'class': 'form-control'})
    )

    equipment = forms.MultipleChoiceField(
        choices=[],  # Will be populated in __init__
        required=True,
        help_text='Select equipment for the audit',
        widget=forms.SelectMultiple(attrs={'class': 'form-control', 'required': 'required'})
    )

    script = forms.ChoiceField(
        choices=[],  # Will be populated in __init__
        required=True,
        help_text='Select a script file for the audit',
        widget=forms.Select(attrs={'class': 'form-control', 'required': 'required'})
    )

    class Meta:
        model = ClientAuditRequest
        fields = [
            'institution_name',
            'phone_number',
            'email',
            'address',
            'description',
            'script',
        ]
        widgets = {
            'description': forms.Textarea(attrs={'rows': 4}),
            'phone_number': forms.TextInput(),
            'email': forms.EmailInput(),
            'address': forms.TextInput(),
        }

    # Add a company name field that will be used to set institution_name
    company_name = forms.CharField(
        max_length=30,
        required=True,
        label="Company/Institution Name",
        help_text="Name of your company or institution"
    )

    def __init__(self, *args, **kwargs):
        self.client = kwargs.pop('client', None)
        self.user = kwargs.pop('user', None)  # Add user parameter
        super().__init__(*args, **kwargs)

        # Remove institution_name from the form as we'll handle it separately
        if 'institution_name' in self.fields:
            del self.fields['institution_name']

        # Set initial values for client-related fields if client is provided
        if self.client and hasattr(self.client, 'client_profile') and self.client.client_profile.client:
            client = self.client.client_profile.client
            client_name = f"{client.first_name} {client.last_name}".strip()

            # Set initial values
            self.fields['company_name'].initial = client_name
            self.fields['phone_number'].initial = client.phone
            self.fields['email'].initial = client.email
            self.fields['address'].initial = client.address

        # Check if the user is an auditor
        is_auditor = self.user and hasattr(self.user, 'profile') and self.user.profile.role == 'AU'

        # Set initial status based on user role
        if is_auditor:
            # Auditors can create pre-approved requests
            self.initial['status'] = 'approved'
        else:
            # For clients, completely remove the script field from the form
            if 'script' in self.fields:
                # Get the first active script or None to use as default
                default_script = AuditScript.objects.filter(is_active=True).first()

                # Store the default script ID in the initial data
                if default_script:
                    self.initial['script'] = str(default_script.id)

                # Remove the script field from the form
                del self.fields['script']

            # Make phone_number, email, and address optional for clients
            if 'phone_number' in self.fields:
                self.fields['phone_number'].required = False
            if 'email' in self.fields:
                self.fields['email'].required = False
            if 'address' in self.fields:
                self.fields['address'].required = False

        # Populate categories from the database
        categories = Category.objects.all()
        # Add an empty choice at the beginning
        category_choices = [('', '---------')]
        category_choices.extend([(str(cat.id), cat.name) for cat in categories])
        self.fields['categories'].choices = category_choices

        # Populate equipment from the database
        equipment_items = Item.objects.all()
        equipment_choices = [(str(item.id), f"{item.name} ({item.category.name})") for item in equipment_items]
        self.fields['equipment'].choices = equipment_choices

        # Only populate script choices for auditors
        if is_auditor and isinstance(self.fields['script'], forms.ChoiceField):
            scripts = AuditScript.objects.filter(is_active=True)
            # Add an empty choice at the beginning
            script_choices = [('', '---------')]
            script_choices.extend([
                (str(script.id),
                f"{script.name} {f'(v{script.version})' if script.version else ''} - {script.category.name if script.category else 'General'}")
                for script in scripts
            ])
            self.fields['script'].choices = script_choices

        # Add Bootstrap classes to form fields
        for field_name, field in self.fields.items():
            if not isinstance(field.widget, forms.HiddenInput):
                field.widget.attrs['class'] = 'form-control'

    def clean(self):
        cleaned_data = super().clean()

        # Validate equipment field
        equipment = cleaned_data.get('equipment')
        if not equipment:
            self.add_error('equipment', 'Please select at least one equipment item')

        # Validate categories field
        categories = cleaned_data.get('categories')
        if not categories:
            self.add_error('categories', 'Please select a category')

        # Validate script field only for auditors
        is_auditor = self.user and hasattr(self.user, 'profile') and self.user.profile.role == 'AU'

        # Only validate script if the user is an auditor and the script field is in the form
        if is_auditor and 'script' in self.fields:
            script = cleaned_data.get('script')
            if not script:
                self.add_error('script', 'Please select a script file')

        return cleaned_data

    def save(self, commit=True):
        # Get the instance but don't save it yet
        instance = super().save(commit=False)

        # Set the institution_name from the company_name field
        if 'company_name' in self.cleaned_data:
            instance.institution_name = self.cleaned_data['company_name']

        # Set the script if provided in cleaned_data
        if 'script' in self.cleaned_data and self.cleaned_data['script']:
            script_id = self.cleaned_data['script']
            try:
                script = AuditScript.objects.get(pk=script_id)
                instance.script = script
            except AuditScript.DoesNotExist:
                pass
        # If script is not in cleaned_data but in initial (for clients)
        elif 'script' not in self.cleaned_data and 'script' in self.initial:
            script_id = self.initial['script']
            try:
                script = AuditScript.objects.get(pk=script_id)
                instance.script = script
            except AuditScript.DoesNotExist:
                # If no script is found, try to get the first active script
                default_script = AuditScript.objects.filter(is_active=True).first()
                if default_script:
                    instance.script = default_script

        # Save the instance if commit is True
        if commit:
            instance.save()

            # Add selected equipment to the many-to-many relationship
            if 'equipment' in self.cleaned_data and self.cleaned_data['equipment']:
                # Clear existing equipment first
                instance.equipment.clear()

                # Add new equipment
                for equipment_id in self.cleaned_data['equipment']:
                    try:
                        equipment = Item.objects.get(pk=equipment_id)
                        instance.equipment.add(equipment)
                    except Item.DoesNotExist:
                        pass

            self.save_m2m()

        return instance


class AuditorAuditRequestForm(AuditRequestForm):
    """
    Form for auditors to create audit requests.
    """
    client_profile = forms.ModelChoiceField(
        queryset=Profile.objects.filter(role='CL'),
        required=False,
        label="Client",
        widget=forms.Select(attrs={'class': 'form-control'}),
        help_text='Select a client for this audit request (optional)'
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Add status field for auditors
        self.fields['status'] = forms.ChoiceField(
            choices=ClientAuditRequest.STATUS_CHOICES,
            initial='approved',
            widget=forms.Select(attrs={'class': 'form-control'}),
            help_text='Set the status of this audit request'
        )

        # Add additional help text for auditors
        self.fields['company_name'].help_text = 'Enter the name of the institution to be audited'
        self.fields['phone_number'].help_text = 'Enter the phone number of the contact person'
        self.fields['email'].help_text = 'Enter the email address of the contact person'

    def save(self, commit=True):
        """Override save to handle client selection."""
        instance = super().save(commit=False)

        # If a client was selected, update the request with client info
        client_profile = self.cleaned_data.get('client_profile')
        if client_profile and client_profile.client:
            client = client_profile.client

            # Update with client information if fields are empty
            if not instance.institution_name:
                client_name = f"{client.first_name} {client.last_name}".strip()
                instance.institution_name = client_name

            if not instance.email and client.email:
                instance.email = client.email

            if not instance.phone_number and client.phone:
                instance.phone_number = client.phone

            if not instance.address and client.address:
                instance.address = client.address

        if commit:
            instance.save()
            self.save_m2m()

        return instance
