{% extends 'equipment/base.html' %}
{% block title %}Profile{% endblock %}
{% load crispy_forms_tags %}
{% load static %}

{% block stylesheets %}
<style>
    .profile-image-container {
        position: relative;
        display: inline-block;
        border-radius: 50%;
        overflow: hidden;
        cursor: pointer;
        margin-bottom: 20px;
    }

    .profile-image {
        width: 150px;
        height: 150px;
        object-fit: cover;
        transition: filter 0.3s;
    }

    .profile-image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: white;
        opacity: 0;
        transition: opacity 0.3s;
    }

    .profile-image-container:hover .profile-image {
        filter: blur(2px);
    }

    .profile-image-container:hover .profile-image-overlay {
        opacity: 1;
    }

    .profile-image-overlay i {
        font-size: 24px;
        margin-bottom: 5px;
    }

    .custom-file-upload {
        display: none;
    }

    /* Hide the file input but make it accessible */
    input[type="file"] {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        border: 0;
    }
</style>
{% endblock %}
{% block content %}
<div class="container my-4">
        <div class="box-main row">
          <div class="col-md-3 col-lg-3"></div>
          <div class="col-md-6 col-lg-6">
          <form method="POST" enctype="multipart/form-data">
              {% csrf_token %}
              <h1 id="header" class="text-success">Edit Your Profile</h1>

              <div class="text-center mb-4">
                <div class="profile-image-container">
                  <img src="{{ request.user.profile.image_url|default:'/static/assets/img/team/profile-picture-3.jpg' }}" class="profile-image" alt="Profile Picture">
                  <div class="profile-image-overlay">
                    <i class="fas fa-camera"></i>
                    <span>Change</span>
                  </div>
                </div>
                <div class="custom-file-upload">
                  {{ p_form.profile_picture }}
                </div>
              </div>
              <div class="float-label">
                <label> username </label>
                <span class="text-danger">{{ u_form.username.errors }}</span>
                {{ u_form.username }}
              </div>
              <br>
              <div class="float-label">
                <label> email </label>
                <span class="text-danger">{{ u_form.email.errors }}</span>
                {{ u_form.email }}
              </div>
              <br>
              <div class="float-label">
                <label>secondary email </label>
                <span class="text-danger">{{ p_form.email.errors }}</span>
                {{ p_form.email }}
              </div>
              <br>
              <div class="float-label">
                <label> first name </label>
                <span class="text-danger">{{ p_form.first_name.errors }}</span>
                {{ p_form.first_name}}
              </div>
              <br>
              <div class="float-label">
                <label> last name </label>
                <span class="text-danger">{{ p_form.last_name.errors }}</span>
                {{ p_form.last_name}}
              </div>
              <br>
              <div class="float-label">
                <label> mobile number </label>
                <span class="text-danger">{{ p_form.telephone.errors }}</span>
                {{ p_form.telephone}}
              </div>
              <br>

              <button type="submit" class="btn btn-success">update</button>
            </form>
              </div>
            </div>
          </div>
          <div class="col-md-3 col-lg-3"></div>
          </div>

{% endblock %}

{% block javascripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle profile picture upload
        const profileImageContainer = document.querySelector('.profile-image-container');
        const fileInput = document.querySelector('input[type="file"]');

        if (profileImageContainer && fileInput) {
            // Click on profile image to trigger file input
            profileImageContainer.addEventListener('click', function() {
                fileInput.click();
            });

            // Preview image when selected
            fileInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        document.querySelector('.profile-image').src = e.target.result;
                    };
                    reader.readAsDataURL(this.files[0]);
                }
            });
        }
    });
</script>
{% endblock %}