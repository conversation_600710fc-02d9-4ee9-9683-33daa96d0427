{% extends 'equipment/base.html' %}
{% load static %}

{% block content %}
<!-- Header Section -->
<div class="container my-4">
    <div class="card shadow-sm rounded p-3">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h4 class="display-6 mb-0 text-success">Auditors</h4>
            </div>
            <div class="col-md-6 d-flex justify-content-end gap-2">
                <a class="btn btn-success btn-sm rounded-pill shadow-sm" href="{% url 'auditor-create' %}">
                    <i class="fa-solid fa-plus"></i> Add Auditor
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container px-3">
    <style>
        .table th, .table td {
            text-align: center;
        }
    </style>
    <table class="table table-bordered table-striped">
        <thead class="thead-light">
            <tr>
                <th>Name</th>
                <th>Phone Number</th>
                <th>Address</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for auditor in auditors %}
            <tr>
                <td>{{ auditor.name }}</td>
                <td>{{ auditor.phone_number }}</td>
                <td>{{ auditor.address }}</td>
                <td>
                    <a href="{% url 'auditor-update' auditor.pk %}" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-edit me-2"></i>Edit
                    </a>
                    <a href="{% url 'auditor-delete' auditor.pk %}" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-trash-alt me-2"></i>Delete
                    </a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <!-- Pagination controls -->
    <nav aria-label="Page navigation example">
        <ul class="pagination justify-content-center">
            <!-- First Page Link -->
            {% if auditors.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page=1" aria-label="First">
                    <span aria-hidden="true">&laquo;&laquo;</span>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link" aria-label="First">
                    <span aria-hidden="true">&laquo;&laquo;</span>
                </span>
            </li>
            {% endif %}

            <!-- Previous Page Link -->
            {% if auditors.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page={{ auditors.previous_page_number }}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </span>
            </li>
            {% endif %}

            <!-- Page Number Links -->
            {% for num in auditors.paginator.page_range %}
                {% if auditors.number == num %}
                <li class="page-item active">
                    <span class="page-link">{{ num }}</span>
                </li>
                {% else %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                </li>
                {% endif %}
            {% endfor %}

            <!-- Next Page Link -->
            {% if auditors.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ auditors.next_page_number }}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </span>
            </li>
            {% endif %}

            <!-- Last Page Link -->
            {% if auditors.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ auditors.paginator.num_pages }}" aria-label="Last">
                    <span aria-hidden="true">&raquo;&raquo;</span>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link" aria-label="Last">
                    <span aria-hidden="true">&raquo;&raquo;</span>
                </span>
            </li>
            {% endif %}
        </ul>
    </nav>

</div>
{% endblock %}
