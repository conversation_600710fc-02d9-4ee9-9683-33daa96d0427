from django.contrib import admin
from .models import AuditReport, ScriptGeneratedReport

@admin.register(AuditReport)
class AuditReportAdmin(admin.ModelAdmin):
    """
    Admin interface configuration for the AuditReport model.
    """
    fields = (
        'client_name', 'contact_number', 'equipment',
        'audit_result', 'description', 'findings',
        'fee', 'additional_fees'
    )
    list_display = (
        'date', 'client_name', 'contact_number', 'equipment',
        'audit_result', 'fee', 'additional_fees', 'total_fee'
    )


@admin.register(ScriptGeneratedReport)
class ScriptGeneratedReportAdmin(admin.ModelAdmin):
    """
    Admin interface configuration for the ScriptGeneratedReport model.
    """
    fields = (
        'title', 'date_created', 'ongoing_audit', 'equipment', 'category',
        'report_file', 'description', 'findings', 'recommendations',
        'compliance_score', 'is_compliant', 'status',
        'script_name', 'script_version'
    )
    list_display = (
        'title', 'date_created', 'equipment', 'category',
        'compliance_score', 'is_compliant', 'status'
    )
    list_filter = ('status', 'is_compliant', 'category', 'date_created')
    search_fields = ('title', 'description', 'findings', 'recommendations')
    readonly_fields = ('date_uploaded',)
