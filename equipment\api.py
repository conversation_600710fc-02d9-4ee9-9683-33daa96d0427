from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.db.models import Count, Sum

from accounts.models import Profile, Auditor
from audit_process.models import AuditRequest, OngoingAudit
from .models import Category, Item, SolutionTechnologique

@login_required
@require_http_methods(["GET"])
def dashboard_stats(request):
    """
    API endpoint to get dashboard statistics.
    """
    # Get counts
    total_audits = OngoingAudit.objects.count()
    pending_audits = OngoingAudit.objects.filter(status='PE').count()
    completed_audits = OngoingAudit.objects.filter(status='CO').count()
    client_count = Profile.objects.filter(role='CL').count()
    
    # Return JSON response
    return JsonResponse({
        'totalAudits': total_audits,
        'pendingAudits': pending_audits,
        'completedAudits': completed_audits,
        'clientCount': client_count
    })

@login_required
@require_http_methods(["GET"])
def notifications(request):
    """
    API endpoint to get user notifications.
    """
    # In a real app, you would fetch actual notifications from a model
    # For now, we'll return dummy data
    notifications = [
        {
            'id': 1,
            'message': 'New audit request received',
            'isRead': False,
            'timestamp': '2025-04-25T10:30:00Z'
        },
        {
            'id': 2,
            'message': 'Audit report #123 is ready for review',
            'isRead': True,
            'timestamp': '2025-04-24T15:45:00Z'
        },
        {
            'id': 3,
            'message': 'Client updated their profile information',
            'isRead': False,
            'timestamp': '2025-04-23T09:15:00Z'
        }
    ]
    
    return JsonResponse(notifications, safe=False)

@login_required
@require_http_methods(["POST"])
def mark_notification_read(request, notification_id):
    """
    API endpoint to mark a notification as read.
    """
    # In a real app, you would update the notification in the database
    # For now, we'll just return a success response
    return JsonResponse({'success': True, 'message': f'Notification {notification_id} marked as read'})
