# Git
.git
.gitignore

# Docker
.docker
docker-compose.yml
docker-compose.override.yml
Dockerfile
.dockerignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/

# IDE
.idea
.vscode
*.swp
*.swo

# Local development
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Media and static files in development
media/
staticfiles/

# Node modules
node_modules/
