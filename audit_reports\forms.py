from django import forms
from .models import ScriptGeneratedReport, ComplianceData, AuditReport
from client_portal.models import ClientProfile
from equipment.models import Item
import csv
import io


class ScriptGeneratedReportForm(forms.ModelForm):
    """
    Form for creating and updating ScriptGeneratedReport instances.
    """

    class Meta:
        model = ScriptGeneratedReport
        fields = [
            'title', 'date_created', 'ongoing_audit', 'equipment', 'category',
            'report_file', 'description', 'findings', 'recommendations',
            'compliance_score', 'is_compliant', 'status',
            'script_name', 'script_version', 'client'
        ]
        # Auditor field is automatically set to the current user
        widgets = {
            'date_created': forms.DateTimeInput(
                attrs={
                    'class': 'form-control',
                    'type': 'datetime-local'
                }
            ),
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'ongoing_audit': forms.Select(attrs={'class': 'form-control'}),
            'equipment': forms.Select(attrs={'class': 'form-control'}),
            'category': forms.Select(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'findings': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'recommendations': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'compliance_score': forms.NumberInput(attrs={'class': 'form-control', 'min': 0, 'max': 100, 'step': 0.1}),
            'is_compliant': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
            'script_name': forms.TextInput(attrs={'class': 'form-control'}),
            'script_version': forms.TextInput(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Set help text for compliance score
        self.fields['compliance_score'].help_text = "Enter a value between 0 and 100"

        # Set help text for is_compliant
        self.fields['is_compliant'].help_text = "Check if the equipment is compliant with standards"

        # Set help text for client
        self.fields['client'].help_text = "Select the client who will receive this report"
        self.fields['client'].required = False  # Make client optional

        # Get all client profiles
        from client_portal.models import ClientProfile
        self.fields['client'].queryset = ClientProfile.objects.all().order_by('user__username')

        # Make equipment field dependent on ongoing_audit
        if 'ongoing_audit' in self.data:
            try:
                ongoing_audit_id = int(self.data.get('ongoing_audit'))
                from audit_process.models import OngoingAudit
                ongoing_audit = OngoingAudit.objects.get(id=ongoing_audit_id)
                self.fields['equipment'].initial = ongoing_audit.item

                # Try to set client from audit request
                if hasattr(ongoing_audit, 'client_audit_request') and ongoing_audit.client_audit_request:
                    audit_request = ongoing_audit.client_audit_request
                    client_profile = ClientProfile.objects.filter(
                        user__email=audit_request.email
                    ).first()

                    if client_profile:
                        self.fields['client'].initial = client_profile.id
            except (ValueError, OngoingAudit.DoesNotExist):
                pass


class CSVProcessingForm(forms.Form):
    """
    Form for uploading and processing CSV files to extract compliance data.
    """
    report = forms.CharField(
        widget=forms.HiddenInput(),
        required=False,
        help_text="Select the report this CSV data is associated with"
    )

    csv_file = forms.FileField(
        widget=forms.FileInput(attrs={'class': 'form-control'}),
        help_text="Upload a CSV file with compliance data (must include 'Result' column)"
    )

    process_immediately = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        help_text="Process the CSV file immediately and update compliance data"
    )

    def __init__(self, *args, auditor=None, **kwargs):
        super().__init__(*args, **kwargs)

        # The report field is now a hidden input, and the actual selection
        # is handled by the custom select element in the template


class AuditReportForm(forms.ModelForm):
    """
    Form for creating and updating audit reports with client selection.
    """
    client = forms.ModelChoiceField(
        queryset=ClientProfile.objects.all(),
        widget=forms.Select(attrs={'class': 'form-control', 'id': 'client-select'}),
        help_text="Select the client for this audit report"
    )

    # Use a ModelChoiceField for equipment selection from ongoing audits
    equipment = forms.ModelChoiceField(
        queryset=Item.objects.none(),  # Will be populated in __init__
        widget=forms.Select(attrs={'class': 'form-control', 'id': 'equipment-select'}),
        help_text="Select equipment from ongoing audits"
    )

    class Meta:
        model = AuditReport
        fields = [
            'client', 'contact_number', 'equipment',
            'audit_result', 'description', 'findings'
        ]
        widgets = {
            'contact_number': forms.TextInput(attrs={'class': 'form-control'}),
            'equipment': forms.Select(attrs={'class': 'form-control', 'id': 'equipment-select'}),
            'audit_result': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'findings': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Add client information to client choices
        self.fields['client'].label_from_instance = self.label_client_from_instance

        # Load all equipment from ongoing audits
        from audit_process.models import OngoingAudit
        ongoing_audits = OngoingAudit.objects.all()
        equipment_ids = ongoing_audits.values_list('item_id', flat=True).distinct()

        # Set the equipment queryset to all equipment from ongoing audits
        self.fields['equipment'].queryset = Item.objects.filter(id__in=equipment_ids)

        # Add equipment information to equipment choices
        self.fields['equipment'].label_from_instance = self.label_equipment_from_instance

        # If a client is selected, try to set client name and contact number
        if 'client' in self.data:
            try:
                client_id = int(self.data.get('client'))
                client = ClientProfile.objects.get(id=client_id)

                # Set client name and contact number
                if not self.instance.pk:  # Only for new instances
                    self.initial['client_name'] = client.user.get_full_name()
                    if client.phone:
                        self.initial['contact_number'] = client.phone
            except (ValueError, ClientProfile.DoesNotExist):
                pass

        # If this is an existing instance, try to find the client
        elif self.instance.pk:
            try:
                client = ClientProfile.objects.filter(
                    user__first_name__icontains=self.instance.client_name.split()[0]
                ).first()

                if client:
                    self.initial['client'] = client.id
            except Exception:
                pass

    @staticmethod
    def label_client_from_instance(obj):
        """Custom label for client dropdown."""
        return f"{obj.user.get_full_name()} ({obj.user.email})"

    @staticmethod
    def label_equipment_from_instance(obj):
        """Custom label for equipment dropdown."""
        # Try to get the ongoing audit for this equipment
        from audit_process.models import OngoingAudit
        ongoing_audits = OngoingAudit.objects.filter(item=obj)

        if ongoing_audits.exists():
            audit = ongoing_audits.first()
            return f"{obj.name} - Auditor: {audit.auditor.name if audit.auditor else 'Unknown'}"

        return obj.name

    def clean(self):
        """Custom validation to set client_name from client."""
        cleaned_data = super().clean()
        client = cleaned_data.get('client')
        equipment = cleaned_data.get('equipment')

        if client:
            # Set client_name from selected client
            cleaned_data['client_name'] = client.user.get_full_name()

            # Set contact_number if not provided
            if not cleaned_data.get('contact_number') and client.phone:
                cleaned_data['contact_number'] = client.phone

        # Validate that equipment is selected
        if not equipment:
            self.add_error('equipment', 'Please select an equipment to audit.')

        return cleaned_data

    def save(self, commit=True):
        """Override save to set client_name from client."""
        instance = super().save(commit=False)

        # Set client_name from client
        client = self.cleaned_data.get('client')
        if client:
            instance.client_name = client.user.get_full_name()

        if commit:
            instance.save()

        return instance


class WindowsAuditCSVForm(forms.Form):
    """
    Form for uploading and processing Windows audit CSV files.
    """
    report = forms.CharField(
        widget=forms.HiddenInput(),
        required=False,
        help_text="Select the report this CSV data is associated with"
    )

    csv_file = forms.FileField(
        widget=forms.FileInput(attrs={'class': 'form-control'}),
        help_text="Upload a Windows audit CSV file (with idPoint, descriptionPoint, commandExecuted, expectedOutput, actualOutput, statut columns)"
    )

    def __init__(self, *args, auditor=None, **kwargs):
        super().__init__(*args, **kwargs)

    def clean_csv_file(self):
        """Validate that the uploaded file is a valid Windows audit CSV."""
        csv_file = self.cleaned_data.get('csv_file')

        if not csv_file:
            return None

        # Check file extension
        if not csv_file.name.lower().endswith('.csv'):
            raise forms.ValidationError("File must be a CSV file.")

        # Try to parse the CSV file
        try:
            # Read the file
            csv_data = csv_file.read().decode('utf-8')
            csv_file.seek(0)  # Reset file pointer

            # Parse CSV
            csv_reader = csv.reader(io.StringIO(csv_data))

            # Check header
            header = next(csv_reader, None)
            required_columns = ["idPoint", "descriptionPoint", "commandExecuted", "expectedOutput", "actualOutput", "statut"]

            # Convert header to lowercase for case-insensitive comparison
            header_lower = [h.lower().strip('"') for h in header] if header else []
            required_columns_lower = [col.lower() for col in required_columns]

            # Check if all required columns are present (case-insensitive)
            missing_columns = [col for col in required_columns_lower if col not in header_lower]

            if missing_columns:
                raise forms.ValidationError(
                    f"CSV file must contain the following columns: {', '.join(required_columns)}. "
                    f"Missing: {', '.join(missing_columns)}. "
                    f"Found: {', '.join(header)}"
                )

            # Check if there's at least one data row
            first_row = next(csv_reader, None)
            if not first_row:
                raise forms.ValidationError("CSV file must contain at least one data row.")

            # Find the index of the status column
            status_index = -1
            for i, h in enumerate(header_lower):
                if 'statut' in h:
                    status_index = i
                    break

            if status_index == -1 or status_index >= len(first_row):
                # If we can't find the status column, just accept the file
                return csv_file

            # Check if the status column contains valid values
            valid_statuses = ["conforme", "non conforme", "non-conforme"]
            status_value = first_row[status_index].strip().lower()

            # Remove quotes and newlines if present
            if status_value.startswith('"') and status_value.endswith('"'):
                status_value = status_value[1:-1]
            status_value = status_value.replace('\n', '').replace('\r', '').strip()

            # More flexible check for status values
            if not any(s in status_value for s in valid_statuses):
                # Just warn but don't reject the file
                print(f"Warning: Status column contains unexpected value: {first_row[status_index]}")

        except Exception as e:
            raise forms.ValidationError(f"Error parsing CSV file: {str(e)}")

        return csv_file
