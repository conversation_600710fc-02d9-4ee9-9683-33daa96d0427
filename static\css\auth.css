/**
 * Authentication Pages - Lo<PERSON>, Register, Password Reset
 */

:root {
    --bs-primary: #023047;
    --bs-secondary: #219ebc;
    --bs-tertiary: #8ecae6;
    --bs-quaternary: #ffb703;
    --bs-light: #f0f5fa;
    --bs-dark: #1a1f2b;
}

body {
    font-family: 'Nunito', 'Inter', sans-serif;
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.auth-wrapper {
    width: 100%;
    max-width: 1000px;
    display: flex;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.3);
    border-radius: 1rem;
    overflow: hidden;
    position: relative;
}

.auth-sidebar {
    width: 40%;
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
    padding: 3rem;
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.auth-sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('/static/assets/img/pattern.svg');
    background-size: cover;
    opacity: 0.1;
    z-index: 0;
}

.auth-sidebar-content {
    position: relative;
    z-index: 1;
}

.auth-sidebar h1 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    color: white;
}

.auth-sidebar p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.auth-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.auth-features li {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.auth-features li i {
    margin-right: 0.75rem;
    width: 24px;
    height: 24px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
}

.auth-content {
    width: 60%;
    background-color: white;
    padding: 3rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.auth-header {
    margin-bottom: 2rem;
    text-align: center;
}

.auth-header h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--bs-primary);
    margin-bottom: 0.5rem;
}

.auth-header p {
    color: #6c757d;
    font-size: 1rem;
}

.auth-form .form-group {
    margin-bottom: 1.5rem;
}

.auth-form label {
    font-weight: 600;
    color: var(--bs-dark);
    margin-bottom: 0.5rem;
    display: block;
}

.auth-form .form-control {
    height: 50px;
    padding: 0.75rem 1.25rem;
    border-radius: 0.5rem;
    border: 1px solid #ced4da;
    font-size: 1rem;
    transition: all 0.2s ease;
}

.auth-form .form-control:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.25rem rgba(2, 48, 71, 0.25);
}

.auth-form .btn {
    height: 50px;
    font-weight: 700;
    font-size: 1rem;
    border-radius: 0.5rem;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.auth-form .btn-primary {
    background-color: var(--bs-primary);
    border: none;
}

.auth-form .btn-primary:hover {
    background-color: #01253a;
    transform: translateY(-3px);
    box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.2);
}

.auth-footer {
    margin-top: 2rem;
    text-align: center;
}

.auth-footer p {
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.auth-footer a {
    color: var(--bs-primary);
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s ease;
}

.auth-footer a:hover {
    color: var(--bs-secondary);
}

.auth-divider {
    display: flex;
    align-items: center;
    margin: 2rem 0;
}

.auth-divider::before,
.auth-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background-color: #e9ecef;
}

.auth-divider span {
    padding: 0 1rem;
    color: #6c757d;
    font-size: 0.875rem;
    font-weight: 600;
}

.social-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.social-button {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    color: #6c757d;
    font-size: 1.25rem;
    transition: all 0.2s ease;
    border: none;
}

.social-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
}

.social-button.facebook:hover {
    background-color: #3b5998;
    color: white;
}

.social-button.google:hover {
    background-color: #db4437;
    color: white;
}

.social-button.twitter:hover {
    background-color: #1da1f2;
    color: white;
}

/* Responsive */
@media (max-width: 991.98px) {
    .auth-wrapper {
        flex-direction: column;
        max-width: 500px;
    }
    
    .auth-sidebar,
    .auth-content {
        width: 100%;
    }
    
    .auth-sidebar {
        padding: 2rem;
        text-align: center;
    }
    
    .auth-features li {
        justify-content: center;
    }
    
    .auth-content {
        padding: 2rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-wrapper {
    animation: fadeIn 0.5s ease-out;
}

/* Error messages */
.errorlist {
    list-style: none;
    padding: 0;
    margin: 0.5rem 0;
    color: #dc3545;
    font-size: 0.875rem;
    font-weight: 600;
}

.alert {
    border-radius: 0.5rem;
    padding: 1rem 1.25rem;
    margin-bottom: 1.5rem;
    border: none;
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

/* Password strength meter */
.password-strength {
    height: 5px;
    margin-top: 0.5rem;
    border-radius: 5px;
    background-color: #e9ecef;
    overflow: hidden;
}

.password-strength-meter {
    height: 100%;
    border-radius: 5px;
    transition: width 0.3s ease, background-color 0.3s ease;
}

.password-strength-text {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    font-weight: 600;
}

.strength-weak .password-strength-meter {
    width: 25%;
    background-color: #dc3545;
}

.strength-fair .password-strength-meter {
    width: 50%;
    background-color: #ffc107;
}

.strength-good .password-strength-meter {
    width: 75%;
    background-color: #17a2b8;
}

.strength-strong .password-strength-meter {
    width: 100%;
    background-color: #28a745;
}

/* Custom checkbox */
.custom-checkbox {
    position: relative;
    padding-left: 30px;
    cursor: pointer;
    user-select: none;
    display: inline-block;
}

.custom-checkbox input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.custom-checkbox:hover input ~ .checkmark {
    background-color: #e9ecef;
}

.custom-checkbox input:checked ~ .checkmark {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.custom-checkbox input:checked ~ .checkmark:after {
    display: block;
}

.custom-checkbox .checkmark:after {
    left: 7px;
    top: 3px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Form help text */
.form-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* Logo */
.auth-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
}

.auth-logo img {
    height: 60px;
    width: auto;
}

/* Background pattern */
.bg-pattern {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    z-index: -1;
}
