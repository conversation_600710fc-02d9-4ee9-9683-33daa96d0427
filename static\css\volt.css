/**
 * Volt Dashboard - Bootstrap 5 Admin Dashboard
 *
 * This is a custom CSS file based on the Volt Dashboard theme.
 * For a complete implementation, consider purchasing the full theme from:
 * https://themesberg.com/product/admin-dashboard/volt-bootstrap-5-dashboard
 */

:root {
    --bs-blue: #0d6efd;
    --bs-indigo: #6610f2;
    --bs-purple: #6f42c1;
    --bs-pink: #d63384;
    --bs-red: #dc3545;
    --bs-orange: #fd7e14;
    --bs-yellow: #ffc107;
    --bs-green: #198754;
    --bs-teal: #20c997;
    --bs-cyan: #0dcaf0;
    --bs-white: #fff;
    --bs-gray: #6c757d;
    --bs-gray-dark: #343a40;
    --bs-gray-100: #f8f9fa;
    --bs-gray-200: #e9ecef;
    --bs-gray-300: #dee2e6;
    --bs-gray-400: #ced4da;
    --bs-gray-500: #adb5bd;
    --bs-gray-600: #6c757d;
    --bs-gray-700: #495057;
    --bs-gray-800: #343a40;
    --bs-gray-900: #212529;
    --bs-primary: #0d6efd;
    --bs-secondary: #6c757d;
    --bs-success: #198754;
    --bs-info: #0dcaf0;
    --bs-warning: #ffc107;
    --bs-danger: #dc3545;
    --bs-light: #f8f9fa;
    --bs-dark: #212529;

    /* Volt specific colors - Bold version */
    --bs-volt-primary: #023047;
    --bs-volt-secondary: #219ebc;
    --bs-volt-tertiary: #8ecae6;
    --bs-volt-quaternary: #ffb703;
    --bs-volt-light: #f0f5fa;
    --bs-volt-dark: #1a1f2b;
}

body {
    font-family: 'Nunito', 'Inter', sans-serif;
    background-color: var(--bs-volt-light);
    color: var(--bs-volt-dark);
    overflow-x: hidden;
    line-height: 1.6;
}

/* Typography */
h1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6 {
    font-weight: 700;
    color: var(--bs-volt-dark);
    margin-bottom: 1rem;
    line-height: 1.3;
}

h1, .h1 {
    font-size: 2.5rem;
}

h2, .h2 {
    font-size: 2rem;
}

h3, .h3 {
    font-size: 1.75rem;
}

h4, .h4 {
    font-size: 1.5rem;
}

h5, .h5 {
    font-size: 1.25rem;
}

h6, .h6 {
    font-size: 1rem;
}

.fw-extrabold {
    font-weight: 800 !important;
}

.text-gray-400 {
    color: var(--bs-gray-400) !important;
}

.text-gray-500 {
    color: var(--bs-gray-500) !important;
}

.text-gray-600 {
    color: var(--bs-gray-600) !important;
}

.text-gray-700 {
    color: var(--bs-gray-700) !important;
}

.text-gray-800 {
    color: var(--bs-gray-800) !important;
}

.text-gray-900 {
    color: var(--bs-gray-900) !important;
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    width: 280px;
    background: var(--bs-volt-primary);
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease-in-out;
    box-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.15);
    overflow-y: auto;
    overflow-x: hidden;
}

@media (max-width: 991.98px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }
}

.sidebar-inner {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.9);
    padding: 1rem 1.5rem;
    font-weight: 700;
    border-radius: 0.75rem;
    margin: 0.35rem 0.75rem;
    transition: all 0.3s ease;
    font-size: 1rem;
    position: relative;
    border-left: 4px solid transparent;
    letter-spacing: 0.025em;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1);
}

.sidebar .nav-link:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.15);
    border-left: 4px solid rgba(255, 255, 255, 0.7);
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.2);
    border-left: 4px solid #fff;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateX(5px);
}

.sidebar .nav-link i,
.sidebar .nav-link svg {
    margin-right: 0.5rem;
}

.sidebar-text {
    font-size: 0.875rem;
}

.sidebar-icon {
    width: 1.25rem;
    height: 1.25rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.dropdown-divider {
    border-color: rgba(255, 255, 255, 0.15);
}

/* Content */
.content {
    margin-left: 280px;
    min-height: 100vh;
    padding-top: 0;
    transition: all 0.3s ease-in-out;
    background-color: var(--bs-volt-light);
    position: relative;
    z-index: 1;
}

@media (max-width: 991.98px) {
    .content {
        margin-left: 0;
    }
}

/* Navbar */
.navbar-dashboard {
    background-color: #fff;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    padding: 1rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
    letter-spacing: -0.025em;
}

.navbar-brand .text-warning {
    color: var(--bs-volt-tertiary) !important;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.5rem 2rem rgba(33, 40, 50, 0.15);
    margin-bottom: 2rem;
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
    border-top: 4px solid var(--bs-volt-primary);
}

.card::before {
    content: '';
    position: absolute;
    top: -4px;
    left: 0;
    right: 0;
    height: 4px;
    background-color: var(--bs-volt-primary);
    z-index: 2;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: 0 1rem 3rem rgba(33, 40, 50, 0.2);
}

.card:hover::before {
    background-color: var(--bs-volt-secondary);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid var(--bs-gray-200);
    padding: 1.5rem 1.75rem;
    border-radius: 1rem 1rem 0 0 !important;
    display: flex;
    align-items: center;
    position: relative;
}

.card-header h2, .card-header h3, .card-header h4, .card-header h5, .card-header h6 {
    margin-bottom: 0;
    font-weight: 700;
    color: var(--bs-volt-dark);
}

.card-body {
    padding: 1.75rem;
    background-color: #fff;
    position: relative;
}

.card-footer {
    background-color: #fff;
    border-top: 1px solid var(--bs-gray-200);
    padding: 1.5rem 1.75rem;
    border-radius: 0 0 1rem 1rem !important;
    position: relative;
}

/* Buttons */
.btn {
    font-weight: 600;
    padding: 0.625rem 1.25rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    letter-spacing: 0.025em;
    text-transform: uppercase;
    font-size: 0.85rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background-color: var(--bs-volt-primary);
    border: none;
    color: #fff;
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.btn-primary:hover {
    background-color: #01253a;
    color: #fff;
}

.btn-secondary {
    background-color: var(--bs-volt-secondary);
    border: none;
    color: #fff;
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.btn-secondary:hover {
    background-color: #1a7d94;
    color: #fff;
}

.btn-tertiary {
    background-color: var(--bs-volt-tertiary);
    border: none;
    color: #023047;
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.btn-tertiary:hover {
    background-color: #6fb8d8;
    color: #023047;
}

.btn-gray-800 {
    background-color: var(--bs-gray-800);
    border-color: var(--bs-gray-800);
    color: #fff;
}

.btn-gray-800:hover {
    background-color: var(--bs-gray-900);
    border-color: var(--bs-gray-900);
    color: #fff;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
}

/* Icons */
.icon-shape {
    width: 48px;
    height: 48px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.75rem;
    color: #fff;
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.icon-shape::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    z-index: -1;
    transform: scale(0);
    transition: transform 0.3s ease;
    border-radius: 50%;
}

.icon-shape:hover {
    transform: translateY(-5px);
}

.icon-shape:hover::before {
    transform: scale(1.5);
}

.icon-shape-primary {
    background-color: var(--bs-volt-primary);
}

.icon-shape-secondary {
    background-color: var(--bs-volt-secondary);
}

.icon-shape-tertiary {
    background-color: var(--bs-volt-tertiary);
    color: var(--bs-volt-primary);
}

.icon-shape-quaternary {
    background-color: var(--bs-volt-quaternary);
    color: var(--bs-volt-primary);
}

.icon-shape-success {
    background-color: #198754;
}

.icon-shape-danger {
    background-color: #dc3545;
}

.icon-shape-warning {
    background-color: var(--bs-volt-quaternary);
    color: #212529;
}

.icon-shape-info {
    background-color: var(--bs-volt-secondary);
}

.icon-shape-sm {
    width: 32px;
    height: 32px;
}

.icon {
    width: 1.25rem;
    height: 1.25rem;
}

.icon-xxs {
    width: 0.875rem;
    height: 0.875rem;
}

/* Tables */
.table-centered th,
.table-centered td {
    vertical-align: middle;
}

.table-nowrap th,
.table-nowrap td {
    white-space: nowrap;
}

.table {
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 1.5rem;
}

.table thead th {
    padding: 1rem 1.25rem;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    font-weight: 700;
    border-top: 0;
    border-bottom: 1px solid var(--bs-gray-200);
    color: var(--bs-volt-dark);
    background-color: var(--bs-volt-light);
}

.table tbody td {
    font-size: 0.875rem;
    font-weight: 400;
    padding: 1rem 1.25rem;
    color: var(--bs-gray-700);
    border-top: 1px solid var(--bs-gray-200);
    transition: all 0.2s ease;
}

.table tbody tr:hover td {
    background-color: rgba(0, 97, 242, 0.05);
}

.table-rounded {
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 0.25rem 1.5rem rgba(33, 40, 50, 0.1);
}

.table-rounded thead th:first-child {
    border-top-left-radius: 0.75rem;
}

.table-rounded thead th:last-child {
    border-top-right-radius: 0.75rem;
}

.table-rounded tbody tr:last-child td:first-child {
    border-bottom-left-radius: 0.75rem;
}

.table-rounded tbody tr:last-child td:last-child {
    border-bottom-right-radius: 0.75rem;
}

.thead-light th {
    background-color: var(--bs-volt-light);
    color: var(--bs-volt-dark);
    font-weight: 700;
}

/* Badges */
.badge {
    padding: 0.5em 0.85em;
    font-weight: 800;
    font-size: 0.75em;
    border-radius: 0.5rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
    letter-spacing: 0.05em;
    text-transform: uppercase;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.2);
}

.badge.bg-primary {
    background-color: var(--bs-volt-primary) !important;
    border-color: rgba(255, 255, 255, 0.2);
}

.badge.bg-secondary {
    background-color: var(--bs-volt-secondary) !important;
    border-color: rgba(255, 255, 255, 0.2);
}

.badge.bg-success {
    background-color: #198754 !important;
    border-color: rgba(255, 255, 255, 0.2);
}

.badge.bg-info {
    background-color: var(--bs-volt-tertiary) !important;
    border-color: rgba(255, 255, 255, 0.2);
    color: var(--bs-volt-primary);
}

.badge.bg-warning {
    background-color: var(--bs-volt-quaternary) !important;
    color: #212529;
    border-color: rgba(0, 0, 0, 0.1);
}

.badge.bg-danger {
    background-color: #dc3545 !important;
    border-color: rgba(255, 255, 255, 0.2);
}

/* Notification bell */
.notification-bell {
    position: relative;
}

.notification-bell.unread::after {
    content: '';
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    width: 0.5rem;
    height: 0.5rem;
    background-color: var(--bs-danger);
    border-radius: 50%;
}

/* Avatar */
.avatar {
    width: 40px;
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: var(--bs-gray-200);
    color: var(--bs-gray-700);
    font-weight: 700;
    font-size: 0.875rem;
    box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    border: 2px solid #fff;
}

.avatar:hover {
    transform: scale(1.05);
}

.avatar-lg {
    width: 64px;
    height: 64px;
    font-size: 1.25rem;
}

.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 0.75rem;
}

.avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

/* List groups */
.list-group-item {
    border-color: var(--bs-gray-200);
    padding: 1.25rem 1.5rem;
    transition: all 0.2s ease;
}

.list-group-item-action {
    color: var(--bs-volt-dark);
}

.list-group-item-action:hover {
    background-color: var(--bs-volt-light);
    transform: translateX(3px);
}

.list-group-flush .list-group-item {
    border-right: 0;
    border-left: 0;
    border-radius: 0;
}

.list-group-flush .list-group-item:last-child {
    border-bottom-width: 0;
}

/* Dropdowns */
.dropdown-menu {
    padding: 0.75rem 0;
    border: 0;
    box-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.15);
    border-radius: 0.75rem;
    animation: dropdown-animation 0.2s ease forwards;
    transform-origin: top;
}

@keyframes dropdown-animation {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--bs-volt-dark);
    transition: all 0.2s ease;
}

.dropdown-item:hover,
.dropdown-item:focus {
    background-color: var(--bs-volt-light);
    color: var(--bs-volt-primary);
    transform: translateX(5px);
}

.dropdown-item.active,
.dropdown-item:active {
    background-color: var(--bs-volt-primary);
    color: #fff;
}

.dropdown-header {
    padding: 0.75rem 1.5rem;
    font-size: 0.75rem;
    text-transform: uppercase;
    font-weight: 700;
    color: var(--bs-gray-600);
    letter-spacing: 0.025em;
}

.dropdown-divider {
    margin: 0.75rem 0;
    border-color: var(--bs-gray-200);
}

/* Footer */
footer {
    background-color: #fff;
    border-radius: 0.75rem;
    box-shadow: 0 0.25rem 1.5rem rgba(33, 40, 50, 0.1);
    padding: 1.75rem;
    margin-top: 3rem;
    margin-bottom: 2rem;
    position: relative;
    z-index: 1;
    transition: all 0.2s ease;
}

footer:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 2rem rgba(33, 40, 50, 0.15);
}

/* Media queries */
@media (max-width: 991.98px) {
    .d-lg-block {
        display: none !important;
    }

    .d-md-none {
        display: block !important;
    }
}

@media (min-width: 992px) {
    .d-lg-block {
        display: block !important;
    }

    .d-md-none {
        display: none !important;
    }
}

/* Utilities */
.border-light {
    border-color: var(--bs-gray-200) !important;
}

.border-bottom {
    border-bottom: 1px solid var(--bs-gray-200) !important;
}

.rounded {
    border-radius: 0.5rem !important;
}

.rounded-circle {
    border-radius: 50% !important;
}

.shadow {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* Dashboard specific styles */
.user-card {
    padding: 1.75rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.1) 100%);
    border-radius: 1rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.user-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.user-card:hover {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0.15) 100%);
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.2);
}

.user-card:hover::before {
    opacity: 1;
}

.dashboard-dropdown {
    min-width: 14rem;
}

/* Fix for profile picture */
.card-img-top {
    width: 100%;
    height: auto;
    object-fit: cover;
    transition: all 0.3s ease;
}

.card-img-top:hover {
    transform: scale(1.02);
}

/* Chart container */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Custom animations */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes gradientAnimation {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Custom text colors */
.text-volt-primary {
    color: var(--bs-volt-primary) !important;
}

.text-volt-secondary {
    color: var(--bs-volt-secondary) !important;
}

.text-volt-tertiary {
    color: var(--bs-volt-tertiary) !important;
}

.text-volt-quaternary {
    color: var(--bs-volt-quaternary) !important;
}

/* Custom background colors */
.bg-volt-primary {
    background-color: var(--bs-volt-primary) !important;
}

.bg-volt-secondary {
    background-color: var(--bs-volt-secondary) !important;
}

.bg-volt-tertiary {
    background-color: var(--bs-volt-tertiary) !important;
}

.bg-volt-quaternary {
    background-color: var(--bs-volt-quaternary) !important;
}

.bg-volt-light {
    background-color: var(--bs-volt-light) !important;
}

.bg-volt-dark {
    background-color: var(--bs-volt-dark) !important;
}

/* Custom gradients */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--bs-volt-primary) 0%, #01253a 100%) !important;
}

.bg-gradient-secondary {
    background: linear-gradient(135deg, var(--bs-volt-secondary) 0%, #1a7d94 100%) !important;
}

.bg-gradient-tertiary {
    background: linear-gradient(135deg, var(--bs-volt-tertiary) 0%, #6fb8d8 100%) !important;
}

.bg-gradient-quaternary {
    background: linear-gradient(135deg, var(--bs-volt-quaternary) 0%, #e69500 100%) !important;
}
