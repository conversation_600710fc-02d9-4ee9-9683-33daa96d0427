{% extends "equipment/base.html" %}
{% load static %}

{% block title %}Delete Compliance Point{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>Delete Compliance Point</h2>
                <div>
                    <a href="{% url 'manual_compliance_list' audit_id=audit.id %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Compliance Points
                    </a>
                </div>
            </div>
            <p class="text-muted">
                Audit: <strong>{{ audit.item.name }}</strong> | 
                Status: <strong>{{ audit.get_audit_status_display }}</strong> |
                Auditor: <strong>{{ audit.auditor.name }}</strong>
            </p>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">Confirm Deletion</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h5 class="alert-heading">Warning!</h5>
                        <p>You are about to delete the compliance point <strong>{{ compliance_point.point_id }}</strong>. This action cannot be undone.</p>
                        <p>All evidence files associated with this compliance point will also be deleted.</p>
                    </div>
                    
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Compliance Point Details</h6>
                        </div>
                        <div class="card-body">
                            <dl class="row mb-0">
                                <dt class="col-sm-3">ID:</dt>
                                <dd class="col-sm-9">{{ compliance_point.point_id }}</dd>
                                
                                <dt class="col-sm-3">Description:</dt>
                                <dd class="col-sm-9">{{ compliance_point.description }}</dd>
                                
                                <dt class="col-sm-3">Command:</dt>
                                <dd class="col-sm-9">
                                    {% if compliance_point.command %}
                                    <code>{{ compliance_point.command }}</code>
                                    {% else %}
                                    <span class="text-muted">N/A</span>
                                    {% endif %}
                                </dd>
                                
                                <dt class="col-sm-3">Status:</dt>
                                <dd class="col-sm-9">
                                    <span class="badge {% if compliance_point.status == 'conforme' %}bg-success{% elif compliance_point.status == 'non_conforme' %}bg-danger{% else %}bg-secondary{% endif %}">
                                        {% if compliance_point.status == 'conforme' %}
                                            Compliant
                                        {% elif compliance_point.status == 'non_conforme' %}
                                            Non-Compliant
                                        {% else %}
                                            Not Checked
                                        {% endif %}
                                    </span>
                                </dd>
                                
                                <dt class="col-sm-3">Evidence Files:</dt>
                                <dd class="col-sm-9">
                                    {{ compliance_point.evidence_files.count }} file(s)
                                </dd>
                            </dl>
                        </div>
                    </div>
                    
                    <form method="POST">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'manual_compliance_list' audit_id=audit.id %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i> Delete Compliance Point
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
