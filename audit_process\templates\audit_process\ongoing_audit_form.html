{% extends "equipment/base.html" %}
{% load static %}
{% block title %}
    {% if form.instance.pk %}Edit Ongoing Audit{% else %}New Ongoing Audit{% endif %}
{% endblock title %}
{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        {% if form.instance.pk %}Edit Ongoing Audit{% else %}New Ongoing Audit{% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}

                        <!-- Audit Request Selection -->
                        <div class="mb-4">
                            <label for="{{ form.client_audit_request.id_for_label }}" class="form-label">{{ form.client_audit_request.label }}</label>
                            {{ form.client_audit_request }}
                            {% if form.client_audit_request.errors %}
                            <div class="text-danger small mt-1">{{ form.client_audit_request.errors }}</div>
                            {% endif %}
                            <div class="form-text">{{ form.client_audit_request.help_text }}</div>
                        </div>

                        <hr class="my-4">

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label for="{{ form.item.id_for_label }}" class="form-label">Equipment</label>
                                {{ form.item }}
                                {% if form.item.errors %}
                                <div class="text-danger small mt-1">{{ form.item.errors }}</div>
                                {% endif %}
                            </div>
                        </div>



                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.completion_date.id_for_label }}" class="form-label">Completion Date</label>
                                {{ form.completion_date }}
                                {% if form.completion_date.errors %}
                                <div class="text-danger small mt-1">{{ form.completion_date.errors }}</div>
                                {% endif %}
                                <div class="form-text">Leave blank if not completed yet.</div>
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.audit_status.id_for_label }}" class="form-label">Audit Status</label>
                                {{ form.audit_status }}
                                {% if form.audit_status.errors %}
                                <div class="text-danger small mt-1">{{ form.audit_status.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label for="{{ form.audit_type.id_for_label }}" class="form-label">{{ form.audit_type.label }}</label>
                                {{ form.audit_type }}
                                {% if form.audit_type.errors %}
                                <div class="text-danger small mt-1">{{ form.audit_type.errors }}</div>
                                {% endif %}
                                <div class="form-text">{{ form.audit_type.help_text }}</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                            {{ form.description }}
                            {% if form.description.errors %}
                            <div class="text-danger small mt-1">{{ form.description.errors }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3" id="script-section">
                            <label for="{{ form.script.id_for_label }}" class="form-label">{{ form.script.label }}</label>
                            {{ form.script }}
                            {% if form.script.errors %}
                            <div class="text-danger small mt-1">{{ form.script.errors }}</div>
                            {% endif %}
                            <div class="form-text">{{ form.script.help_text }}</div>
                        </div>

                        <div id="manual-audit-info" class="alert alert-info mb-4" style="display: none;">
                            <h5><i class="fas fa-info-circle me-2"></i> Manual Audit Selected</h5>
                            <p>For manual audits, you'll be able to add compliance points after creating the audit.</p>
                            <p>After saving, the system will automatically search for control points related to the selected equipment.</p>
                            <p>Each compliance point can have:</p>
                            <ul>
                                <li>A unique ID</li>
                                <li>Description of what's being checked</li>
                                <li>Commands used for verification</li>
                                <li>Expected and actual results</li>
                                <li>Evidence files (screenshots, logs, etc.)</li>
                            </ul>
                        </div>

                        <!-- Equipment info display -->
                        <div id="equipment-info" class="mb-4 mt-2" style="display: none;">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">Selected Equipment</h6>
                                </div>
                                <div class="card-body" id="equipment-list">
                                    <!-- Equipment items will be displayed here -->
                                </div>
                            </div>
                        </div>

                        <!-- JavaScript to handle audit request selection and audit type -->
                        <script>
                            document.addEventListener('DOMContentLoaded', function() {
                                const auditRequestSelect = document.getElementById('{{ form.client_audit_request.id_for_label }}');
                                const auditTypeSelect = document.getElementById('{{ form.audit_type.id_for_label }}');
                                const itemSelect = document.getElementById('{{ form.item.id_for_label }}');
                                const scriptSection = document.getElementById('script-section');
                                const manualAuditInfo = document.getElementById('manual-audit-info');
                                const equipmentInfo = document.getElementById('equipment-info');
                                const equipmentList = document.getElementById('equipment-list');
                                const scriptSelect = document.getElementById('{{ form.script.id_for_label }}');

                                // No control points elements needed anymore

                                // Function to extract equipment from description
                                function extractEquipment(description) {
                                    if (!description || !description.includes('Selected Equipment:')) {
                                        return [];
                                    }

                                    const equipmentSection = description.split('Selected Equipment:')[1].split('Category:')[0];
                                    return equipmentSection.split(',')
                                        .map(item => item.trim())
                                        .filter(item => item.length > 0);
                                }

                                // Function to display equipment
                                function displayEquipment(equipment) {
                                    if (equipment.length === 0) {
                                        equipmentInfo.style.display = 'none';
                                        return;
                                    }

                                    equipmentList.innerHTML = '';
                                    equipment.forEach(item => {
                                        const badge = document.createElement('span');
                                        badge.className = 'badge bg-success me-2 mb-2';
                                        badge.textContent = item;
                                        equipmentList.appendChild(badge);
                                    });

                                    equipmentInfo.style.display = 'block';
                                }

                                // Function to fetch control points is no longer needed
                                // Control points will be loaded after saving the audit

                                // Function to toggle sections based on audit type
                                function toggleSections() {
                                    const auditType = auditTypeSelect.value;

                                    if (auditType === 'manual') {
                                        scriptSection.style.display = 'none';
                                        manualAuditInfo.style.display = 'block';
                                        // Make script not required for manual audits
                                        scriptSelect.required = false;
                                    } else {
                                        scriptSection.style.display = 'block';
                                        manualAuditInfo.style.display = 'none';
                                        // Make script required for automated audits
                                        scriptSelect.required = true;
                                    }
                                }

                                // Handle audit type selection change
                                auditTypeSelect.addEventListener('change', toggleSections);

                                // No need to handle equipment selection change for control points

                                // Handle audit request selection change
                                auditRequestSelect.addEventListener('change', function() {
                                    const selectedOption = auditRequestSelect.options[auditRequestSelect.selectedIndex];

                                    if (selectedOption.value) {
                                        // Fetch the audit request details via AJAX
                                        fetch(`/audit-requests/api/get-request/${selectedOption.value}/`)
                                            .then(response => response.json())
                                            .then(data => {
                                                if (data.description) {
                                                    const equipment = extractEquipment(data.description);
                                                    displayEquipment(equipment);

                                                    // If description is empty, use the one from the audit request
                                                    const descriptionField = document.getElementById('{{ form.description.id_for_label }}');
                                                    if (!descriptionField.value) {
                                                        descriptionField.value = data.description;
                                                    }
                                                }
                                            })
                                            .catch(error => {
                                                console.error('Error fetching audit request details:', error);
                                                equipmentInfo.style.display = 'none';
                                            });
                                    } else {
                                        equipmentInfo.style.display = 'none';
                                    }
                                });

                                // Initialize sections based on current audit type
                                toggleSections();

                                // Check if an audit request is already selected on page load
                                if (auditRequestSelect.value) {
                                    auditRequestSelect.dispatchEvent(new Event('change'));
                                }

                                // No need to check for equipment on page load for control points
                            });
                        </script>

                        <div class="d-flex justify-content-end mt-4">
                            <a href="{% url 'ongoing_audit_list' %}" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-1"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-1"></i> Save
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}
