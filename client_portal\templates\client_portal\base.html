<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        {% load static %}
        <!-- Bootstrap CSS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" type="text/css" href="https://unpkg.com/@webpixels/css@1.1.5/dist/index.css">
        <link rel="stylesheet" href="{% static 'css/style.css' %}" type="text/css">
        <!-- Font Awesome for icons -->
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
        {% block stylesheets %}{% endblock stylesheets %}
        <title>CLIENT PORTAL: {% block title %}{% endblock title %}</title>
    </head>
<body>
    <style>
        .paginator {
            display: flex;
            justify-content: center;
        }
        .sidebar {
            width: 250px;
            background-color: #343a40;
            color: #fff;
            position: fixed;
            top: 0;
            bottom: 0; /* Extend to the bottom of the viewport */
            display: flex;
            flex-direction: column; /* Stack children vertically */
        }

        .sidebar-header {
            background-color: #212529;
        }

        .nav-container {
            flex: 1; /* Take up remaining space */
            overflow-y: auto; /* Enable vertical scrolling */
            padding: 5px; /* Add some padding */
        }

        .nav-link {
            color: #adb5bd;
        }

        .nav-link.active {
            background-color: #495057;
            color: #fff;
        }

        .dropdown-menu {
            background-color: #495057;
        }

        .dropdown-item {
            color: #adb5bd;
        }

        .dropdown-item.active, .dropdown-item:hover {
            background-color: #6c757d;
            color: #fff;
        }

        .sidebar-footer {
            background-color: #212529;
            text-align: center;
            padding: 10px;
        }
    </style>

    <!-- Client Sidebar -->
    <aside class="sidebar position-fixed top-0 left-0 overflow-auto h-100 bg-dark text-light" id="show-side-navigation1">
        <!-- Close Button -->
        <button class="btn btn-dark d-md-none d-lg-none position-absolute top-0 end-0 mt-2 me-2 rounded-circle" data-close="show-side-navigation1" aria-label="Close">
            <i class="fa fa-times text-white"></i>
        </button>

        <!-- Sidebar Header -->
        <div class="sidebar-header d-flex align-items-center px-3 py-4 border-bottom border-secondary">
            <a href="{% url 'client_profile' %}" class="d-flex align-items-center text-decoration-none text-light">
                <img class="rounded-circle img-fluid" id="sidebar-img" width="45" src="https://via.placeholder.com/45" alt="Profile Picture" />
                <div class="ms-3">
                    <h5 class="fs-6 mb-0">
                        {{ user.get_full_name|default:user.username }}
                    </h5>
                    <span class="badge bg-primary text-light">Client</span>
                </div>
            </a>
        </div>

        <!-- Navigation Container -->
        <div class="nav-container">
            <!-- Navigation Links -->
            <ul class="nav flex-column mt-3">
                <li class="nav-item mb-2">
                    <a class="nav-link text-light {% if request.resolver_match.url_name == 'client_dashboard' %}active{% endif %}" href="{% url 'client_dashboard' %}">
                        <i class="fa fa-tachometer-alt fa-fw me-2"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item mb-2">
                    <a class="nav-link text-light {% if 'audit_request' in request.resolver_match.url_name %}active{% endif %}" href="{% url 'client_audit_request_list' %}">
                        <i class="fa-solid fa-clipboard-list me-2"></i> Audit Requests
                    </a>
                </li>
                <li class="nav-item mb-2">
                    <a class="nav-link text-light {% if 'audit_report' in request.resolver_match.url_name %}active{% endif %}" href="{% url 'client_audit_report_list' %}">
                        <i class="fa-solid fa-file-alt me-2"></i> Audit Reports
                    </a>
                </li>
                <li class="nav-item mb-2">
                    <a class="nav-link text-light {% if request.resolver_match.url_name == 'client_notifications' %}active{% endif %}" href="{% url 'client_notifications' %}">
                        <i class="fa-solid fa-bell me-2"></i> Notifications
                        {% if unread_notifications_count > 0 %}
                        <span class="badge bg-danger">{{ unread_notifications_count }}</span>
                        {% endif %}
                    </a>
                </li>
                <li class="nav-item mb-2">
                    <a class="nav-link text-light {% if request.resolver_match.url_name == 'client_profile' %}active{% endif %}" href="{% url 'client_profile' %}">
                        <i class="fa-solid fa-user me-2"></i> Profile
                    </a>
                </li>
            </ul>
        </div>

        <!-- Sidebar Footer -->
        <div class="sidebar-footer position-absolute bottom-0 w-100 text-center py-3 bg-dark border-top border-secondary">
            <form method="post" action="{% url 'user-logout' %}">
                {% csrf_token %}
                <button type="submit" class="btn btn-danger btn-sm w-75">
                    <i class="fa fa-sign-out-alt me-2"></i> Logout
                </button>
            </form>
        </div>
    </aside>

    <section id="wrapper">
        <nav class="navbar navbar-expand-md bg-primary">
            <div class="container-fluid mx-2">
                <div class="navbar-header">
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#toggle-navbar" aria-controls="toggle-navbar" aria-expanded="false" aria-label="Toggle navigation">
                        <i class="fa fa-bars text-white"></i>
                    </button>
                    <a class="navbar-brand text-white" style="font-size: 20px;" href="{% url 'client_dashboard' %}">Client<span class="text-warning">Portal</span></a>
                </div>
                <div class="collapse navbar-collapse" id="toggle-navbar">
                    <ul class="navbar-nav ms-auto">
                        {% if unread_notifications_count > 0 %}
                        <li class="nav-item">
                            <a class="nav-link text-white" href="{% url 'client_notifications' %}">
                                <i class="fa fa-bell"></i>
                                <span class="badge bg-danger">{{ unread_notifications_count }}</span>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Messages -->
        <div class="container mt-3">
            {% if messages %}
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                {% endfor %}
            {% endif %}
        </div>

        <!-- Content -->
        {% block content %}{% endblock content %}
    </section>

    <!-- Bootstrap core JavaScript-->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Core plugin JavaScript-->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

    <script>
        // Explicitly initializing Bootstrap dropdowns if needed
        document.querySelectorAll('.dropdown-toggle').forEach(function(element) {
            new bootstrap.Dropdown(element);
        });
    </script>

    {% block javascripts %}{% endblock javascripts %}
</body>
</html>
