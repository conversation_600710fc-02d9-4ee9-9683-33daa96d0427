import django_tables2 as tables
from .models import AuditReport

class AuditReportTable(tables.Table):
    """
    Table representation for the AuditReport model.
    """

    class Meta:
        model = AuditReport
        template_name = "django_tables2/semantic.html"
        fields = (
            'date', 'client_name', 'contact_number', 'equipment',
            'audit_result', 'description', 'findings'
        )
        order_by = 'date'
