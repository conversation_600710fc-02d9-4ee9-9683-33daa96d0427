{% extends "equipment/base.html" %}
{% load static %}
<!-- Page title  -->
{% block title %}Create sale{% endblock title %}

<!-- Specific Page CSS goes HERE  -->
{% block stylesheets %}
<!--Select2 CSS-->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@1.5.2/dist/select2-bootstrap4.min.css">
{% endblock stylesheets %}

<!-- Page Heading -->
<h2>Add sale</h2>

<!-- Page content  -->
{% block content %}
<div class="container py-5">
    <!-- Go back -->
    <div class="mb-4">
        <a href="{% url 'saleslist' %}" class="btn btn-outline-success">
            <i class="fas fa-arrow-left me-2"></i>
            Go back
        </a>
    </div>

    <!-- Sale items and details -->
    <form id="form_sale" action="{% url 'sale-create' %}" class="saleForm" method="post">
        <div class="row">
            <!-- Left column -->
            <div class="col-lg-8 mb-4">
                <div class="card border-light shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">Sale Items</h5>
                    </div>
                    <div class="card-body">
                        <!-- Search item -->
                        <div class="mb-4">
                            <label for="searchbox_items" class="form-label">Search Item:</label>
                            <select class="form-select select2" name="searchbox_items" id="searchbox_items" aria-label="Search items"></select>
                        </div>

                        <!-- Delete all items from sale -->
                        <button type="button" class="btn btn-danger btn-sm mb-4 deleteAll">
                            <i class="fas fa-trash-alt me-2"></i> Delete All Items
                        </button>

                        <!-- Items Table -->
                        <div class="table-responsive my-3">
                            <table class="table table-bordered table-striped" id="table_items">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Name</th>
                                        <th>Price</th>
                                        <th>Quantity</th>
                                        <th>Total</th>
                                        <th class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Left Column -->

            <!-- Right Column -->
            <div class="col-lg-4 mb-4">
                <div class="card border-light shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">Sale Details</h5>
                    </div>
                    <div class="card-body">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="customer" class="form-label">Client</label>
                            <select name="customer" class="form-select" id="customer" aria-label="Client" required>
                                <option value="" selected disabled hidden>Select the client</option>
                                {% for customer in customers %}
                                <option value="{{ customer.value }}">{{ customer.label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="sub_total" class="form-label">Subtotal</label>
                            <input name="sub_total" type="number" class="form-control" id="sub_total" aria-label="Subtotal" required>
                        </div>
                        <div class="mb-3">
                            <label for="tax_percentage" class="form-label">Tax Inclusive (%)</label>
                            <input name="tax_percentage" type="number" class="form-control" id="tax_percentage" aria-label="Tax Inclusive" value="0" required>
                        </div>
                        <div class="mb-3">
                            <label for="tax_amount" class="form-label">Tax Amount</label>
                            <input name="tax_amount" type="number" class="form-control" id="tax_amount" aria-label="Tax Amount" required>
                        </div>
                        <div class="mb-3">
                            <label for="grand_total" class="form-label">Grand Total</label>
                            <input name="grand_total" type="number" class="form-control" id="grand_total" aria-label="Grand Total" required>
                        </div>
                        <div class="mb-3">
                            <label for="amount_paid" class="form-label">Amount Payed</label>
                            <input name="amount_paid" type="number" class="form-control" id="amount_paid" aria-label="Amount Paid" required>
                        </div>
                        <button type="submit" class="btn btn-success w-100">Create Sale</button>
                    </div>
                </div>
            </div>
            <!-- End Right Column -->
        </div>
    </form>
</div>
{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
<!-- Datatables -->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js" defer></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js" defer></script>

<!-- Select2 -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js" defer></script>

<!-- Bootstrap Touchspin -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-touchspin/3.1.0/jquery.bootstrap-touchspin.min.js" defer></script>

<!-- Sweet Alert -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.6.15/dist/sweetalert2.all.min.js" defer></script>

<script>
    // Source: https://stackoverflow.com/a/32605063
    function roundTo(n, digits) {
        if (digits === undefined) {
            digits = 0;
        }

        var multiplicator = Math.pow(10, digits);
        n = parseFloat((n * multiplicator).toFixed(11));
        return Math.round(n) / multiplicator;
    }

    // Variable for item number in table
    var number = 1;

    // Variable to store sale details and products
    var sale = {
        products: {
            client: 0,  // This was previously 'customer'
            sub_total: 0.00,
            grand_total: 0.00,
            tax_amount: 0.00,
            tax_percentage: 0.00,
            amount_payed: 0.00,
            amount_change: 0.00,
            items: []
        },
        calculate_sale: function () {
            // Subtotal of all items added
            var sub_total = 0.00;

            var tax_percentage = $('input[name="tax_percentage"]').val();

            // Calculates the total for each item
            $.each(this.products.items, function (pos, dict) {
                dict.pos = pos;
                dict.total_item = roundTo(dict.quantity * dict.price, 2);
                // Add the item total to the sale subtotal
                sub_total += roundTo(dict.total_item, 2);
            });

            // Update the sale subtotal, grand total, and tax amount
            this.products.sub_total = roundTo(sub_total, 2);
            this.products.grand_total = roundTo(this.products.sub_total, 2);
            this.products.tax_amount = roundTo(this.products.sub_total * (tax_percentage / 100), 2);

            $('input[name="sub_total"]').val(this.products.sub_total);
            $('input[name="tax_amount"]').val(this.products.tax_amount);
            $('input[name="grand_total"]').val(this.products.grand_total);
        },
        // Adds an item to the sale object
        add_item: function (item) {
            this.products.items.push(item);
            this.list_item();
        },
        // Shows the selected item in the table
        list_item: function () {
            // Calculate the sale
            this.calculate_sale();

            tblItems = $("#table_items").DataTable({
                destroy: true,
                data: this.products.items,
                columns: [
                    {"data": "number"},
                    {"data": "name"},
                    {"data": "price"},
                    {"data": "quantity"},
                    {"data": "total_item"},
                    {"data": "id"},
                ],
                columnDefs: [
                    {
                        // Quantity
                        class: 'text-center',
                        targets: [3],
                        render: function (data, type, row) {
                            return '<input name="quantity" type="text" class="form-control form-control-xs text-center input-sm" autocomplete="off" value="' + row.quantity + '">';
                        },
                    },
                    {
                        // Item price and total
                        class: 'text-right',
                        targets: [2, 4],
                        render: function (data, type, row) {
                            return parseFloat(data).toFixed(2) + ' $';
                        },
                    },
                    {
                        // Delete button
                        class: 'text-center',
                        targets: [-1],
                        orderable: false,
                        render: function (data, type, row) {
                            return '<a rel="delete" type="button" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="Delete item"> <i class="fas fa-trash-alt fa-xs"></i> </a>';
                        },
                    },
                ],
                rowCallback(row, data, displayNun, displayIndex, dataIndex) {
                    $(row).find("input[name='quantity']").TouchSpin({
                        min: 1,
                        max: 100, // Máximo de acuerdo al stock de cada itemo
                        step: 1,
                        decimals: 0,
                        boostat: 1,
                        maxboostedstep: 3,
                        postfix: ''
                    });
                },
            });
        },
    };

    $(document).ready(function () {
        // Tax percentage touchspin
        $("input[name='tax_percentage']").TouchSpin({
            min: 0,
            max: 100,
            step: 1,
            decimals: 2,
            boostat: 5,
            maxboostedstep: 10,
            postfix: '%'
        }).on('change', function () {
            sale.calculate_sale();
        });

        // Select2 clients
        $('#searchbox_customers').select2({
            delay: 250,
            placeholder: "Select a client",
            allowClear: true,
            minimumInputLength: 1,
            ajax: {
                url: "{% url 'get_clients' %}",
                type: 'POST',
                data: function (params) {
                    return {
                        term: params.term,
                        csrfmiddlewaretoken: $('input[name="csrfmiddlewaretoken"]').val() // Include CSRF token
                    };
                },
                processResults: function (data) {
                    return {
                        results: data
                    };
                }
            }
        }).on('select2:select', function (e) {
            // When a client is selected
            var data = e.params.data;
            sale.products.client = data.id;  // This was previously 'customer'
        });

        // Select2 items searchbox
        $('#searchbox_items').select2({
            delay: 250,
            placeholder: 'Search an item',
            minimumInputLength: 1,
            allowClear: true,
            templateResult: template_item_searchbox,
            ajax: {
                url: "{% url 'get_items' %}",
                type: 'POST',
                data: function (params) {
                    return {
                        term: params.term,
                        csrfmiddlewaretoken: $('input[name="csrfmiddlewaretoken"]').val()
                    };
                },
                processResults: function (data) {
                    return {
                        results: data
                    };
                }
            }
        }).on('select2:select', function (e) {
            // When an item is selected from the searchbox
            var data = e.params.data;
            // Add number, subtotal, and quantity of the item to the dictionary
            data.number = number;
            number++; // Increase the item number in the table
            // Add the item to the sale object
            sale.add_item(data);
            // Clear the searchbox after the item is selected
            $(this).val('').trigger('change.select2');
        });

        // Tables Events
        $('#table_items tbody').on('click', 'a[rel="delete"]', function () {
            // When an item is deleted
            var tr = tblItems.cell($(this).closest('td, li')).index();
            item_name = (tblItems.row(tr.row).data().name);

            Swal.fire({
                customClass: {
                    confirmButton: 'ml-3 btn btn-danger',
                    cancelButton: 'btn btn-success'
                },
                buttonsStyling: false,
                title: "Are you sure you want to delete this item from the sale?",
                text: item_name,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Delete',
                cancelButtonText: 'Cancel',
                reverseButtons: true,
            }).then((result) => {
                // If confirmed
                if (result.isConfirmed) {
                    // Delete the item
                    sale.products.items.splice(tr.row, 1);
                    // List the table again
                    sale.list_item();
                    Swal.fire('The item was eliminated!', '', 'success');
                }
            });
        }).on('change keyup', 'input[name="quantity"]', function () {
            // When an item changes its quantity
            var quantity = parseInt($(this).val());
            var tr = tblItems.cell($(this).closest('td, li')).index();
            // Update the item quantity in the sale object
            sale.products.items[tr.row].quantity = quantity;
            // Calculate the sale with the new quantity
            sale.calculate_sale();
            // Find the row to update the item total
            $('td:eq(4)', tblItems.row(tr.row).node()).html(sale.products.items[tr.row].total_item + ' $');
        });

        // Delete all items
        $('.deleteAll').on('click', function () {
            // If there are no items, don't do anything
            if (sale.products.items.length === 0) return false;
            // Alert the user
            Swal.fire({
                customClass: {
                    confirmButton: 'ml-3 btn btn-danger',
                    cancelButton: 'btn btn-success'
                },
                buttonsStyling: false,
                title: "Are you sure you want to delete all items from the sale?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Delete all',
                cancelButtonText: 'Cancel',
                reverseButtons: true,
            }).then((result) => {
                // If confirmed
                if (result.isConfirmed) {
                    // Remove all items from the sale object
                    sale.products.items = [];
                    // Recalculate the sale
                    sale.list_item();
                    Swal.fire('All items were eliminated!', '', 'success');
                }
            });
        });

        // Items datatable
        tblItems = $('#table_items').DataTable({
            columnDefs: [
                {
                    targets: [-1],
                    orderable: false,
                }
            ],
        });

        // Item searchbox templateResult
        function template_item_searchbox(repo) {
            return $(`
                <div class="card mb-3">
                    <div class="card-body">
                        <small class="card-title">${repo.name}</small>
                    </div>
                </div>
            `);
        }

        // Helper functions
        function getAjax(type, url, data, successCallback, errorCallback) {
            $.ajax({
                type: type,
                url: url,
                data: data,
                success: successCallback,
                error: errorCallback
            });
        }

        // Function to round numbers
        function roundTo(value, decimals) {
            const factor = Math.pow(10, decimals);
            return Math.round(value * factor) / factor;
        }

        // On form submit
        $('form#form_sale').on('submit', function (event) {
            event.preventDefault();

            // Gather sale details
            var amountPaid = parseFloat($('input[name="amount_paid"]').val());
            var grandTotal = parseFloat($('input[name="grand_total"]').val());
            var amountChange = roundTo(amountPaid - grandTotal, 2);

            // Gather sale details
            var formData = {
                customer: $('select[name="customer"]').val(),  // This field name is still 'customer' in the backend
                sub_total: $('input[name="sub_total"]').val(),
                grand_total: grandTotal,
                amount_paid: amountPaid,
                amount_change: amountChange,
                items: sale.products.items
            };

            // Check if amount_change is present
            if (isNaN(amountChange)) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Amount change is required!'
                });
                return;
            }

            // Get CSRF token
            var csrftoken = $('input[name="csrfmiddlewaretoken"]').val();

            // Submit the form data via AJAX
            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                contentType: 'application/json',
                headers: {
                    'X-CSRFToken': csrftoken
                },
                data: JSON.stringify(formData),
                success: function (response) {
                    // Handle success response
                    sale.products.items = [];
                    sale.list_item();
                    $('form#form_sale').trigger('reset');
                    Swal.fire({
                        icon: 'success',
                        title: 'Success',
                        text: 'Sale has been completed successfully!'
                    });
                },
                error: function (xhr) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: xhr.responseJSON.message || 'An error occurred while processing the sale!'
                    });
                }
            });
        });
    });
</script>

{% endblock javascripts %}