{% extends "equipment/base.html" %}
{% load static %}{% load crispy_forms_tags %}
{% block title %}Update Ongoing Audit{% endblock %}
{% block content %}
<div class="container p-5">
    <h2>Create or Update Ongoing Audit</h2>
    <form method="post">
        {% csrf_token %}
        {{ form.non_field_errors }}
        <div class="row mt-5">
            <div class="form-group col-md-4">
                {{ form.item.label_tag }}
                {{ form.item }}
                {{ form.item.errors }}
            </div>
            <div class="form-group col-md-4">
                {{ form.auditor.label_tag }}
                {{ form.auditor }}
                {{ form.auditor.errors }}
            </div>
            <div class="form-group col-md-4">
                {{ form.description.label_tag }}
                {{ form.description }}
                {{ form.description.errors }}
            </div>
        </div>
        <div class="row">
            <div class="form-group col-md-6">
                {{ form.completion_date.label_tag }}
                {{ form.completion_date }}
                {{ form.completion_date.errors }}
            </div>
            <div class="form-group col-md-6">
                {{ form.quantity.label_tag }}
                {{ form.quantity }}
                {{ form.quantity.errors }}
            </div>
        </div>
        <div class="row">
            <div class="form-group col-md-4">
                {{ form.audit_status.label_tag }}
                {{ form.audit_status }}
                {{ form.audit_status.errors }}
            </div>
            <div class="form-group col-md-4">
                {{ form.price.label_tag }}
                {{ form.price }}
                {{ form.price.errors }}
            </div>
        </div>
        <button type="submit" class="mt-3 btn btn-primary">
            <i class="fas fa-save"></i> Save
        </button>
    </form>
</div>
{% endblock %}
