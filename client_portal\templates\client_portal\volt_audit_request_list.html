{% extends 'client_portal/volt_base.html' %}

{% block title %}My Audit Requests{% endblock %}
{% block page_title %}My Audit Requests{% endblock %}

{% block content %}
<div class="py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow">
                <div class="card-header d-flex flex-row align-items-center flex-0 border-bottom">
                    <div class="d-block">
                        <h2 class="h5 mb-0">Audit Requests</h2>
                    </div>
                    <div class="d-block ms-auto">
                        <a href="{% url 'client_create_audit_request' %}" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus me-2"></i> Create New Audit Request
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if audit_requests %}
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap mb-0 rounded">
                            <thead class="thead-light">
                                <tr>
                                    <th class="border-0 rounded-start">ID</th>
                                    <th class="border-0">Date</th>
                                    <th class="border-0">Items</th>
                                    <th class="border-0">Status</th>
                                    <th class="border-0 rounded-end">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for request in audit_requests %}
                                <tr>
                                    <td>{{ request.id }}</td>
                                    <td>{{ request.date|date:"M d, Y" }}</td>
                                    <td>
                                        {% if "Selected Equipment:" in request.description %}
                                            <span class="badge bg-info">Equipment selected</span>
                                        {% else %}
                                            <span class="text-muted">No equipment specified</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if request.status == 'pending' %}
                                        <span class="badge bg-warning">Pending Review</span>
                                        {% elif request.status == 'approved' %}
                                        <span class="badge bg-success">Accepted - Audit in Progress</span>
                                        {% elif request.status == 'rejected' %}
                                        <span class="badge bg-danger">Rejected</span>
                                        {% elif request.status == 'completed' %}
                                        <span class="badge bg-info">Completed</span>
                                        {% else %}
                                        <span class="badge bg-secondary">Unknown</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'client_audit_request_detail' request.id %}" class="btn btn-sm btn-gray-800">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                    <div class="card-footer px-3 border-0 d-flex align-items-center justify-content-center">
                        <nav aria-label="Page navigation">
                            <ul class="pagination mb-0">
                                {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
                                </li>
                                {% endif %}

                                {% for i in paginator.page_range %}
                                {% if page_obj.number == i %}
                                <li class="page-item active">
                                    <a class="page-link" href="#">{{ i }}</a>
                                </li>
                                {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ i }}">{{ i }}</a>
                                </li>
                                {% endif %}
                                {% endfor %}

                                {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Next</a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-4">
                        <div class="icon icon-shape icon-shape-primary rounded-circle mb-4">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <h3 class="fw-extrabold">No Audit Requests Yet</h3>
                        <p class="text-gray-500 mb-4">You haven't made any audit requests yet. Create your first one to get started.</p>
                        <a href="{% url 'client_create_audit_request' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i> Create Your First Audit Request
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow">
                <div class="card-header border-bottom d-flex align-items-center justify-content-between">
                    <h2 class="fs-5 fw-bold mb-0">Understanding Audit Request Status</h2>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 col-md-6 col-lg-3 mb-4 mb-lg-0">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="icon-shape icon-shape-warning rounded-circle mb-3">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <h5 class="fw-bold">Pending Review</h5>
                                    <p class="text-gray-500">Your audit request has been submitted and is waiting for review by an auditor.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 col-lg-3 mb-4 mb-lg-0">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="icon-shape icon-shape-success rounded-circle mb-3">
                                        <i class="fas fa-check"></i>
                                    </div>
                                    <h5 class="fw-bold">Accepted</h5>
                                    <p class="text-gray-500">Your request has been accepted and the audit is currently in progress.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 col-lg-3 mb-4 mb-md-0">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="icon-shape icon-shape-danger rounded-circle mb-3">
                                        <i class="fas fa-times"></i>
                                    </div>
                                    <h5 class="fw-bold">Rejected</h5>
                                    <p class="text-gray-500">Your request has been rejected. Check the details for more information.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 col-lg-3">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="icon-shape icon-shape-info rounded-circle mb-3">
                                        <i class="fas fa-flag-checkered"></i>
                                    </div>
                                    <h5 class="fw-bold">Completed</h5>
                                    <p class="text-gray-500">The audit has been completed and a report is available for review.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
