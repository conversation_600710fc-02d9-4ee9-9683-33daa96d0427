from django.db.models.signals import post_save
from django.dispatch import receiver

from .models import OngoingAudit


# Commented out as Item model doesn't have a quantity field
# @receiver(post_save, sender=OngoingAudit)
# def update_item_quantity(sender, instance, created, **kwargs):
#     """
#     Signal to update item quantity when an ongoing audit is created.
#     """
#     if created:
#         # Item model doesn't have a quantity field
#         # instance.item.quantity += instance.quantity
#         # instance.item.save()
#         pass
