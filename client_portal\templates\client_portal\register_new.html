{% load static %}
{% load crispy_forms_tags %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Client Registration - Audit System</title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/auth.css' %}">
</head>
<body>
    <div class="bg-pattern"></div>

    <div class="auth-wrapper">
        <div class="auth-sidebar">
            <div class="auth-sidebar-content">
                <div class="auth-logo">
                    <img src="{% static 'assets/img/brand/light.svg' %}" alt="Logo">
                </div>
                <h1>Join Us Today!</h1>
                <p>Create a client account to start requesting audits with our comprehensive audit management system.</p>

                <ul class="auth-features">
                    <li><i class="fas fa-check"></i> Easy audit request creation</li>
                    <li><i class="fas fa-check"></i> Track audit progress in real-time</li>
                    <li><i class="fas fa-check"></i> View detailed audit reports</li>
                    <li><i class="fas fa-check"></i> Secure data management</li>
                </ul>
            </div>
        </div>

        <div class="auth-content">
            <div class="auth-header">
                <h2>Create Client Account</h2>
                <p>Fill in the form below to create your client account</p>
            </div>

            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            <form method="POST" class="auth-form">
                {% csrf_token %}

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="id_username">Username</label>
                            <input type="text" name="username" class="form-control" id="id_username" placeholder="Choose a username" required>
                            {% if form.username.errors %}
                                <div class="errorlist">
                                    {% for error in form.username.errors %}
                                        <p>{{ error }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="id_email">Email</label>
                            <input type="email" name="email" class="form-control" id="id_email" placeholder="Enter your email" required>
                            {% if form.email.errors %}
                                <div class="errorlist">
                                    {% for error in form.email.errors %}
                                        <p>{{ error }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="id_first_name">First Name</label>
                            <input type="text" name="first_name" class="form-control" id="id_first_name" placeholder="Enter your first name" required>
                            {% if form.first_name.errors %}
                                <div class="errorlist">
                                    {% for error in form.first_name.errors %}
                                        <p>{{ error }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="id_last_name">Last Name</label>
                            <input type="text" name="last_name" class="form-control" id="id_last_name" placeholder="Enter your last name" required>
                            {% if form.last_name.errors %}
                                <div class="errorlist">
                                    {% for error in form.last_name.errors %}
                                        <p>{{ error }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="id_company_name">Company Name</label>
                            <input type="text" name="company_name" class="form-control" id="id_company_name" placeholder="Enter your company name" required>
                            {% if form.company_name.errors %}
                                <div class="errorlist">
                                    {% for error in form.company_name.errors %}
                                        <p>{{ error }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="id_phone">Phone</label>
                            <input type="tel" name="phone" class="form-control" id="id_phone" placeholder="Enter your phone number" required>
                            {% if form.phone.errors %}
                                <div class="errorlist">
                                    {% for error in form.phone.errors %}
                                        <p>{{ error }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="id_address">Address</label>
                    <textarea name="address" class="form-control" id="id_address" placeholder="Enter your address" rows="3" required></textarea>
                    {% if form.address.errors %}
                        <div class="errorlist">
                            {% for error in form.address.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="id_password1">Password</label>
                            <input type="password" name="password1" class="form-control" id="id_password1" placeholder="Create a password" required>
                            {% if form.password1.errors %}
                                <div class="errorlist">
                                    {% for error in form.password1.errors %}
                                        <p>{{ error }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="password-strength">
                                <div class="password-strength-meter"></div>
                            </div>
                            <div class="password-strength-text"></div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="id_password2">Confirm Password</label>
                            <input type="password" name="password2" class="form-control" id="id_password2" placeholder="Confirm your password" required>
                            {% if form.password2.errors %}
                                <div class="errorlist">
                                    {% for error in form.password2.errors %}
                                        <p>{{ error }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <label class="custom-checkbox">
                        I agree to the <a href="#" class="text-decoration-none">Terms of Service</a> and <a href="#" class="text-decoration-none">Privacy Policy</a>
                        <input type="checkbox" name="terms" required>
                        <span class="checkmark"></span>
                    </label>
                </div>

                <button type="submit" class="btn btn-primary w-100">Create Account</button>

                <div class="auth-footer">
                    <p>Already have an account? <a href="{% url 'user-login' %}">Sign In</a></p>
                </div>
            </form>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Password Strength Meter -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const passwordInput = document.getElementById('id_password1');
            const strengthMeter = document.querySelector('.password-strength-meter');
            const strengthText = document.querySelector('.password-strength-text');
            const passwordStrength = document.querySelector('.password-strength');

            passwordInput.addEventListener('input', function() {
                const password = passwordInput.value;
                let strength = 0;

                if (password.length >= 8) strength += 1;
                if (password.match(/[a-z]/) && password.match(/[A-Z]/)) strength += 1;
                if (password.match(/\d/)) strength += 1;
                if (password.match(/[^a-zA-Z\d]/)) strength += 1;

                passwordStrength.className = 'password-strength';

                switch (strength) {
                    case 0:
                        strengthMeter.style.width = '0%';
                        strengthText.textContent = '';
                        break;
                    case 1:
                        passwordStrength.classList.add('strength-weak');
                        strengthMeter.style.width = '25%';
                        strengthText.textContent = 'Weak';
                        strengthText.style.color = '#dc3545';
                        break;
                    case 2:
                        passwordStrength.classList.add('strength-fair');
                        strengthMeter.style.width = '50%';
                        strengthText.textContent = 'Fair';
                        strengthText.style.color = '#ffc107';
                        break;
                    case 3:
                        passwordStrength.classList.add('strength-good');
                        strengthMeter.style.width = '75%';
                        strengthText.textContent = 'Good';
                        strengthText.style.color = '#17a2b8';
                        break;
                    case 4:
                        passwordStrength.classList.add('strength-strong');
                        strengthMeter.style.width = '100%';
                        strengthText.textContent = 'Strong';
                        strengthText.style.color = '#28a745';
                        break;
                }
            });
        });
    </script>
</body>
</html>
