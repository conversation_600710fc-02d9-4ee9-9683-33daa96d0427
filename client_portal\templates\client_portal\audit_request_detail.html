{% extends 'client_portal/base.html' %}

{% block title %}Audit Request #{{ audit_request.id }}{% endblock %}
{% block page_title %}Audit Request #{{ audit_request.id }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>Audit Request #{{ audit_request.id }}</h2>
                <a href="{% url 'client_audit_request_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to List
                </a>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Request Information</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Request ID:</span>
                            <span class="fw-bold">{{ audit_request.id }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Date:</span>
                            <span>{{ audit_request.date|date:"F d, Y" }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Status:</span>
                            <span>
                                {% if audit_request.status == 'P' %}
                                <span class="badge bg-warning">Pending</span>
                                {% elif audit_request.status == 'A' %}
                                <span class="badge bg-success">Accepted</span>
                                {% elif audit_request.status == 'R' %}
                                <span class="badge bg-danger">Rejected</span>
                                {% else %}
                                <span class="badge bg-secondary">Unknown</span>
                                {% endif %}
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Total Items:</span>
                            <span>{{ audit_request.details.count }}</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Items to Audit</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Item</th>
                                    <th>Quantity</th>
                                    <th>Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for detail in audit_request.details.all %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ detail.item.name }}</td>
                                    <td>{{ detail.quantity }}</td>
                                    <td>${{ detail.price|floatformat:2 }}</td>
                                    <td>${{ detail.total|floatformat:2 }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="4" class="text-end">Subtotal:</th>
                                    <th>${{ audit_request.sub_total|floatformat:2 }}</th>
                                </tr>
                                <tr>
                                    <th colspan="4" class="text-end">Tax ({{ audit_request.tax_percentage }}%):</th>
                                    <th>${{ audit_request.tax_amount|floatformat:2 }}</th>
                                </tr>
                                <tr>
                                    <th colspan="4" class="text-end">Grand Total:</th>
                                    <th>${{ audit_request.grand_total|floatformat:2 }}</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    {% if audit_request.status == 'A' %}
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Audit Reports</h5>
                </div>
                <div class="card-body">
                    {% if related_audit_reports %}
                    <div class="list-group">
                        {% for report in related_audit_reports %}
                        <a href="{% url 'client_audit_report_detail' report.slug %}" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">{{ report.report_name }}</h6>
                                <small>{{ report.date|date:"M d, Y" }}</small>
                            </div>
                            <p class="mb-1">
                                Result: 
                                {% if report.result %}
                                <span class="badge bg-success">Pass</span>
                                {% else %}
                                <span class="badge bg-danger">Fail</span>
                                {% endif %}
                            </p>
                        </a>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted">No audit reports have been generated for this request yet.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
