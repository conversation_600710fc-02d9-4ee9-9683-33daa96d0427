/* Dashboard Styles */

/* Consistent container padding */
.dashboard-container {
    padding: 2rem;
}

/* Card styling */
.dashboard-card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
    height: 100%;
    transition: transform 0.2s, box-shadow 0.2s;
}

.dashboard-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dashboard-card .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 1rem;
}

.dashboard-card .card-body {
    padding: 1.5rem;
}

/* Table styling */
.dashboard-table {
    width: 100%;
}

.dashboard-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.dashboard-table th, 
.dashboard-table td {
    padding: 0.75rem;
    vertical-align: middle;
    text-align: center;
}

/* Scrollable tables */
.table-container {
    max-height: 400px;
    overflow-y: auto;
    scrollbar-width: thin;
}

.table-container::-webkit-scrollbar {
    width: 6px;
}

.table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.table-container::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Chart containers */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Status badges */
.status-badge {
    padding: 0.5rem 0.75rem;
    border-radius: 0.25rem;
    font-weight: 500;
    display: inline-block;
}

/* Progress bars */
.compliance-progress {
    height: 20px;
    border-radius: 0.25rem;
    margin-bottom: 0;
}

/* Summary cards */
.summary-card {
    text-align: center;
    padding: 1.5rem;
}

.summary-card .card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.summary-card .display-4 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.summary-card .text-muted {
    font-size: 0.875rem;
}

/* Action buttons */
.action-btn {
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.2s;
}

.action-btn:hover {
    transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 1rem;
    }
    
    .summary-card .display-4 {
        font-size: 2rem;
    }
    
    .chart-container {
        height: 250px;
    }
}
