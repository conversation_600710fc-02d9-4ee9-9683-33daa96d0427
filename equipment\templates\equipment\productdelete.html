{% extends "equipment/base.html" %}
{% load static %}
{% block title %}Delete Equipment{% endblock title %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6>Delete Equipment</h6>
                        <a href="{% url 'product-list' %}" class="btn btn-secondary btn-sm">Back to List</a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <p>Are you sure you want to delete "{{ object.name }}"?</p>
                        <button type="submit" class="btn btn-danger">Confirm Delete</button>
                        <a href="{% url 'product-list' %}" class="btn btn-secondary">Cancel</a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}
