from django.db import models
from django.contrib.auth.models import User
from accounts.models import Client
from imagekit.models import ProcessedImageField
from imagekit.processors import ResizeToFill

class ClientProfile(models.Model):
    """
    Extended profile for client users.
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='client_profile')
    client = models.OneToOneField(Client, on_delete=models.CASCADE, related_name='client_profile', null=True, blank=True)
    company_name = models.CharField(max_length=100, blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    phone = models.CharField(max_length=20, blank=True, null=True)
    profile_picture = ProcessedImageField(
        default='profile_pics/default.jpg',
        upload_to='profile_pics/clients',
        format='JPEG',
        processors=[ResizeToFill(150, 150)],
        options={'quality': 100},
        verbose_name='Profile Picture',
        null=True,
        blank=True
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @property
    def image_url(self):
        """
        Returns the URL of the profile picture.
        Returns an empty string if the image is not available.
        """
        try:
            return self.profile_picture.url
        except (AttributeError, ValueError):
            return '/static/assets/img/team/profile-picture-3.jpg'

    def __str__(self):
        return f"{self.user.username}'s Client Profile"

class ClientNotification(models.Model):
    """
    Notifications for client users.
    """
    NOTIFICATION_TYPES = (
        ('audit_request', 'Audit Request'),
        ('audit_report', 'Audit Report'),
        ('system', 'System Notification'),
    )

    client = models.ForeignKey(ClientProfile, on_delete=models.CASCADE, related_name='notifications')
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    title = models.CharField(max_length=100)
    message = models.TextField()
    # Fields for related objects
    # We'll add proper foreign key references later when needed
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.title} - {self.client.user.username}"

    class Meta:
        ordering = ['-created_at']
