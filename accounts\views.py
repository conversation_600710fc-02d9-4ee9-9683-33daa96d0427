# Django core imports
from django.shortcuts import render, redirect
from django.http import JsonResponse
from django.urls import reverse_lazy, reverse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.contrib.auth import authenticate, login
from django.contrib import messages

# Authentication and permissions
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin

# Class-based views
from django.views.generic import (
    ListView,
    CreateView,
    UpdateView,
    DeleteView
)

# Third-party packages
from django_tables2 import SingleTableView
from django_tables2.export.views import ExportMixin

# Local app imports
from .models import Profile, Client, Auditor
from .forms import (
    CreateUserForm, UserUpdateForm,
    ProfileUpdateForm, ClientForm,
    AuditorForm
)
from .tables import ProfileTable
from client_portal.models import ClientP<PERSON><PERSON>le


def custom_login(request):
    """
    Custom login view that redirects users based on their role.
    """
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        user = authenticate(request, username=username, password=password)

        if user is not None:
            login(request, user)

            # Check if user is a client
            try:
                client_profile = ClientProfile.objects.get(user=user)
                return redirect('client_dashboard')  # Redirect to client dashboard
            except ClientProfile.DoesNotExist:
                # Not a client, check if they have a staff profile
                try:
                    profile = Profile.objects.get(user=user)
                    return redirect('dashboard')  # Redirect to staff dashboard
                except Profile.DoesNotExist:
                    # User has no profile, create a default staff profile
                    Profile.objects.create(user=user, role='staff', status='active')
                    return redirect('dashboard')
        else:
            messages.error(request, 'Invalid username or password')

    # Create a form context for rendering the login template
    from django.contrib.auth.forms import AuthenticationForm
    form = AuthenticationForm()
    return render(request, 'accounts/login.html', {'form': form})


@login_required
def jwt_login_view(request):
    """
    View for JWT login test page.
    Only accessible to superusers for testing purposes.
    """
    if not request.user.is_superuser:
        messages.error(request, 'Access denied. This page is only accessible to administrators.')
        return redirect('dashboard')

    return render(request, 'accounts/jwt_login.html')

def register(request):
    """
    Handle user registration.
    If the request is POST, process the form data to create a new user.
    Redirect to the login page on successful registration.
    For GET requests, render the registration form.
    """
    if request.method == 'POST':
        form = CreateUserForm(request.POST)
        if form.is_valid():
            form.save()
            return redirect('user-login')
    else:
        form = CreateUserForm()

    return render(request, 'accounts/register.html', {'form': form})


@login_required
def profile(request):
    """
    Render the user profile page.
    Requires user to be logged in.
    """
    return render(request, 'accounts/profile.html')


@login_required
def profile_update(request):
    """
    Handle profile update.
    If the request is POST, process the form data
    to update user information and profile.
    Redirect to the profile page on success.
    For GET requests, render the update forms.
    """
    if request.method == 'POST':
        u_form = UserUpdateForm(request.POST, instance=request.user)
        p_form = ProfileUpdateForm(
            request.POST,
            request.FILES,
            instance=request.user.profile
        )
        if u_form.is_valid() and p_form.is_valid():
            u_form.save()
            p_form.save()
            messages.success(request, 'Your profile has been updated successfully!')
            # Redirect to profile list if user is admin, otherwise to user profile
            if request.user.profile.role == 'AD':
                return redirect('profile_list')
            else:
                return redirect('user-profile')
    else:
        u_form = UserUpdateForm(instance=request.user)
        p_form = ProfileUpdateForm(instance=request.user.profile)

    return render(
        request,
        'accounts/profile_update.html',
        {'u_form': u_form, 'p_form': p_form}
    )


class ProfileListView(LoginRequiredMixin, ExportMixin, SingleTableView):
    """
    Display a list of profiles in a table format.
    Requires user to be logged in
    and supports exporting the table data.
    Pagination is applied with 10 profiles per page.
    """
    model = Profile
    template_name = 'accounts/stafflist_debug.html'
    context_object_name = 'profiles'
    table_class = ProfileTable
    paginate_by = 10
    table_pagination = False


class ProfileCreateView(LoginRequiredMixin, CreateView):
    """
    Create a new profile.
    Requires user to be logged in and have superuser status.
    Redirects to the profile list upon successful creation.
    """
    model = Profile
    template_name = 'accounts/staffcreate.html'
    fields = ['user', 'role', 'status']

    def get_success_url(self):
        """
        Return the URL to redirect to after successfully creating a profile.
        """
        return reverse('profile_list')

    def test_func(self):
        """
        Check if the user is a superuser.
        """
        return self.request.user.is_superuser


class ProfileUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    """
    Update an existing profile.
    Requires user to be logged in and have superuser status.
    Redirects to the profile list upon successful update.
    """
    model = Profile
    template_name = 'accounts/staffupdate.html'
    fields = ['user', 'role', 'status']

    def get_success_url(self):
        """
        Return the URL to redirect to after successfully updating a profile.
        If the user is a superuser, redirect to the profile list.
        Otherwise, redirect to the user's profile page.
        """
        if self.request.user.is_superuser:
            return reverse('profile_list')
        else:
            return reverse('user-profile')

    def test_func(self):
        """
        Check if the user is a superuser or the owner of the profile.
        """
        profile = self.get_object()
        return self.request.user.is_superuser or profile.user == self.request.user


class ProfileDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    """
    Delete an existing profile.
    Requires user to be logged in and have superuser status.
    Redirects to the profile list upon successful deletion.
    """
    model = Profile
    template_name = 'accounts/staffdelete.html'

    def get_success_url(self):
        """
        Return the URL to redirect to after successfully deleting a profile.
        If the user is a superuser, redirect to the profile list.
        Otherwise, redirect to the login page.
        """
        if self.request.user.is_superuser:
            return reverse('profile_list')
        else:
            return reverse('user-login')

    def test_func(self):
        """
        Check if the user is a superuser or the owner of the profile.
        """
        profile = self.get_object()
        return self.request.user.is_superuser or profile.user == self.request.user


class ClientListView(LoginRequiredMixin, ListView):
    """
    View for listing all clients.

    Requires the user to be logged in. Displays a list of all Client objects.
    """
    model = Client
    template_name = 'accounts/client_list.html'
    context_object_name = 'clients'


class ClientCreateView(LoginRequiredMixin, CreateView):
    """
    View for creating a new client.

    Requires the user to be logged in.
    Provides a form for creating a new Client object.
    On successful form submission, redirects to the client list.
    """
    model = Client
    template_name = 'accounts/client_form.html'
    form_class = ClientForm
    success_url = reverse_lazy('client_list')


class ClientUpdateView(LoginRequiredMixin, UpdateView):
    """
    View for updating an existing client.

    Requires the user to be logged in.
    Provides a form for editing an existing Client object.
    On successful form submission, redirects to the client list.
    """
    model = Client
    template_name = 'accounts/client_form.html'
    form_class = ClientForm
    success_url = reverse_lazy('client_list')


class ClientDeleteView(LoginRequiredMixin, DeleteView):
    """
    View for deleting a client.

    Requires the user to be logged in.
    Displays a confirmation page for deleting an existing Client object.
    On confirmation, deletes the object and redirects to the client list.
    """
    model = Client
    template_name = 'accounts/client_confirm_delete.html'
    success_url = reverse_lazy('client_list')


def is_ajax(request):
    return request.META.get('HTTP_X_REQUESTED_WITH') == 'XMLHttpRequest'


@csrf_exempt
@require_POST
@login_required
def get_clients(request):
    if is_ajax(request) and request.method == 'POST':
        term = request.POST.get('term', '')
        clients = Client.objects.filter(
            name__icontains=term
        ).values('id', 'name')
        client_list = list(clients)
        return JsonResponse(client_list, safe=False)
    return JsonResponse({'error': 'Invalid request method'}, status=400)


class AuditorListView(LoginRequiredMixin, ListView):
    model = Auditor
    template_name = 'accounts/auditor_list.html'
    context_object_name = 'auditors'
    paginate_by = 10


class AuditorCreateView(LoginRequiredMixin, CreateView):
    model = Auditor
    form_class = AuditorForm
    template_name = 'accounts/auditor_form.html'
    success_url = reverse_lazy('auditor-list')


class AuditorUpdateView(LoginRequiredMixin, UpdateView):
    model = Auditor
    form_class = AuditorForm
    template_name = 'accounts/auditor_form.html'
    success_url = reverse_lazy('auditor-list')


class AuditorDeleteView(LoginRequiredMixin, DeleteView):
    model = Auditor
    template_name = 'accounts/auditor_confirm_delete.html'
    success_url = reverse_lazy('auditor-list')


@login_required
def debug_roles(request):
    """
    Debug view to check profile roles.
    """
    profiles = Profile.objects.all()
    return render(request, 'accounts/debug_roles.html', {'profiles': profiles})
