const path = require('path');

module.exports = {
  entry: {
    main: './static/js/react/index.js',
    notifications: './static/js/react/notifications.js',
    dashboard: './static/js/react/dashboard.js',
    categorySelector: './static/js/react/categorySelector.js',
    auth: './static/js/react/auth.js'
  },
  output: {
    path: path.resolve(__dirname, './static/js/dist'),
    filename: '[name].bundle.js',
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env', '@babel/preset-react']
          }
        }
      }
    ]
  }
};
