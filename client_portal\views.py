import json
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib import messages
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.urls import reverse, reverse_lazy
from django.db import transaction, IntegrityError
from django.http import JsonResponse
from django.contrib.auth import authenticate, login, update_session_auth_hash
from django.contrib.auth.models import User
from django.contrib.auth.forms import PasswordChangeForm

from .models import ClientProfile, ClientNotification
from .forms import ClientRegistrationForm, ClientProfileUpdateForm
# Temporarily commented out to avoid import errors during migration
# from .forms import AuditRequestForm, AuditRequestDetailForm
# from audit_process.models import AuditRequest, AuditRequestDetail, OngoingAudit
# from audit_reports.models import AuditReport
from accounts.models import Client, Profile, AuditorNotification

# Client login redirect view
def client_login_redirect(request):
    """
    Redirect to the main login page with a message indicating this is for clients.
    """
    messages.info(request, 'Please log in with your client account credentials.')
    return redirect('user-login')

# Client registration view
def client_register(request):
    """
    View for client registration.
    """
    if request.method == 'POST':
        form = ClientRegistrationForm(request.POST)
        if form.is_valid():
            try:
                # Save the user and create client profile
                user = form.save()

                # Create a Client instance for this user
                client = Client.objects.create(
                    first_name=form.cleaned_data.get('first_name'),
                    last_name=form.cleaned_data.get('last_name'),
                    email=form.cleaned_data.get('email'),
                    phone=form.cleaned_data.get('phone'),
                    address=form.cleaned_data.get('address')
                )

                # Update the ClientProfile to link to the Client instance
                client_profile = ClientProfile.objects.get(user=user)
                client_profile.client = client
                client_profile.save()

                # Log the user in after registration
                login(request, user)
                messages.success(request, 'Your client account has been created! You are now logged in.')
                return redirect('client_dashboard')
            except Exception as e:
                # If any error occurs, delete the user if it was created
                if 'user' in locals():
                    User.objects.filter(pk=user.pk).delete()
                messages.error(request, f'An error occurred during registration. Please try again.')
    else:
        form = ClientRegistrationForm()

    return render(request, 'client_portal/register_new.html', {'form': form})

# Client dashboard view
@login_required
def client_dashboard(request):
    """
    Dashboard view for clients.
    """
    try:
        client_profile = request.user.client_profile
    except ClientProfile.DoesNotExist:
        messages.error(request, 'You do not have a client profile. Please contact the administrator.')
        return redirect('user-login')

    # Get audit requests - using the same logic as ClientAuditRequestListView
    try:
        from audit_requests.models import AuditRequest as ClientAuditRequest  # Renamed to avoid confusion

        # Start with an empty queryset
        queryset = ClientAuditRequest.objects.none()

        # Find audit requests by email
        if request.user.email:
            email_requests = ClientAuditRequest.objects.filter(email=request.user.email)
            queryset = email_requests

        # Find audit requests by client name
        if client_profile.client:
            client_name = f"{client_profile.client.first_name} {client_profile.client.last_name}".strip()
            name_requests = ClientAuditRequest.objects.filter(institution_name=client_name)

            # Combine the querysets
            queryset = queryset | name_requests

            # Also try to find requests where the institution name contains parts of the client name
            first_name = client_profile.client.first_name.strip().lower()
            last_name = client_profile.client.last_name.strip().lower()

            if first_name:
                first_name_requests = ClientAuditRequest.objects.filter(
                    institution_name__icontains=first_name
                )
                queryset = queryset | first_name_requests

            if last_name:
                last_name_requests = ClientAuditRequest.objects.filter(
                    institution_name__icontains=last_name
                )
                queryset = queryset | last_name_requests

        # Get the final queryset, removing duplicates
        all_audit_requests = queryset.distinct().order_by('-date')

        # Count total and pending requests
        audit_requests_count = all_audit_requests.count()
        pending_requests_count = all_audit_requests.filter(status='pending').count()

        # Get the 5 most recent requests for display
        audit_requests = all_audit_requests[:5]
    except (ImportError, AttributeError) as e:
        print(f"Error getting audit requests: {str(e)}")
        audit_requests = []
        audit_requests_count = 0
        pending_requests_count = 0

    # Get audit reports - both standard and script-generated
    try:
        # Get standard audit reports
        from audit_reports.models import AuditReport
        standard_reports = []
        standard_reports_count = 0
        passed_standard_reports = 0
        failed_standard_reports = 0

        if client_profile.client:
            client_name = f"{client_profile.client.first_name} {client_profile.client.last_name}".strip()
            # Get all standard audit reports for this client
            all_standard_reports = AuditReport.objects.filter(client_name=client_name).order_by('-date')
            # Count total, passed, and failed reports
            standard_reports_count = all_standard_reports.count()
            passed_standard_reports = all_standard_reports.filter(audit_result=True).count()
            failed_standard_reports = all_standard_reports.filter(audit_result=False).count()
            # Get the 5 most recent reports for display
            standard_reports = all_standard_reports[:5]

        # Get script-generated reports
        from audit_reports.models import ScriptGeneratedReport, ComplianceData
        script_reports = []
        script_reports_count = 0
        compliant_script_reports = 0
        non_compliant_script_reports = 0

        if client_profile.client:
            # Get all script reports for this client
            all_script_reports = ScriptGeneratedReport.objects.filter(client=client_profile).order_by('-date_created')
            # Count total, compliant, and non-compliant reports
            script_reports_count = all_script_reports.count()
            compliant_script_reports = all_script_reports.filter(is_compliant=True).count()
            non_compliant_script_reports = all_script_reports.filter(is_compliant=False).count()
            # Get the 5 most recent reports for display
            script_reports = all_script_reports[:5]

            # Also try to find reports by email
            if request.user.email:
                email_script_reports = ScriptGeneratedReport.objects.filter(
                    client__user__email=request.user.email
                ).exclude(id__in=[r.id for r in all_script_reports]).order_by('-date_created')

                # Update counts
                script_reports_count += email_script_reports.count()
                compliant_script_reports += email_script_reports.filter(is_compliant=True).count()
                non_compliant_script_reports += email_script_reports.filter(is_compliant=False).count()

                # Add to the list of reports to display
                script_reports = list(script_reports) + list(email_script_reports[:5])
                script_reports = sorted(script_reports, key=lambda x: x.date_created, reverse=True)[:5]

        # Combine counts
        audit_reports_count = standard_reports_count + script_reports_count
        passed_reports_count = passed_standard_reports + compliant_script_reports
        failed_reports_count = failed_standard_reports + non_compliant_script_reports

        # Combine reports for display
        # We'll prioritize script reports as they're more detailed
        audit_reports = list(script_reports)
        remaining_slots = 5 - len(audit_reports)
        if remaining_slots > 0:
            audit_reports.extend(standard_reports[:remaining_slots])

        # Add script reports to context for separate display
        script_generated_reports = script_reports

        # Get compliance data for the client's equipment
        compliance_data = []
        compliance_categories = []
        compliance_scores = []

        # Get all compliance data for this client's equipment
        if client_profile.client:
            # Get all equipment IDs from the client's audit reports
            equipment_ids = set()
            for report in all_script_reports:
                equipment_ids.add(report.equipment.id)

            # Get the latest compliance data for each equipment
            from django.db.models import Max
            from equipment.models import Item

            for equipment_id in equipment_ids:
                # Get the latest compliance data for this equipment
                latest_data = ComplianceData.objects.filter(
                    equipment_id=equipment_id
                ).order_by('-date_checked').first()

                if latest_data:
                    compliance_data.append(latest_data)

            # Sort by compliance score (descending)
            compliance_data = sorted(compliance_data, key=lambda x: x.compliance_score, reverse=True)

            # Prepare data for the radar chart
            if compliance_data:
                # Get categories from the first compliance data item
                if compliance_data[0].categories:
                    categories = list(compliance_data[0].categories.keys())
                    compliance_categories = json.dumps(categories)

                    # Get scores for each category
                    scores = [compliance_data[0].categories[cat]['score'] for cat in categories]
                    compliance_scores = json.dumps(scores)

    except (ImportError, AttributeError) as e:
        print(f"Error getting audit reports: {str(e)}")
        audit_reports = []
        audit_reports_count = 0
        passed_reports_count = 0
        failed_reports_count = 0
        script_generated_reports = []

    # Get notifications (handle missing fields in the database)
    try:
        notifications = ClientNotification.objects.filter(client=client_profile, is_read=False).order_by('-created_at')[:5]
        unread_notifications_count = notifications.count()
    except Exception as e:
        # If there's an error with the notifications, just use empty values
        notifications = []
        unread_notifications_count = 0

    # Generate dates for the last 7 days
    from datetime import datetime, timedelta

    # Generate dates for the last 7 days
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    date_range = [(start_date + timedelta(days=i)).strftime('%Y-%m-%d') for i in range(8)]

    # Simplified chart data
    audit_dates = json.dumps(date_range)
    audit_requests_data = json.dumps([0] * 8)  # Placeholder data
    audit_reports_data = json.dumps([0] * 8)   # Placeholder data

    context = {
        'client_profile': client_profile,
        'audit_requests': audit_requests,
        'audit_requests_count': audit_requests_count,
        'pending_requests_count': pending_requests_count,
        'audit_reports': audit_reports,
        'audit_reports_count': audit_reports_count,
        'passed_reports_count': passed_reports_count,
        'failed_reports_count': failed_reports_count,
        'script_generated_reports': script_generated_reports if 'script_generated_reports' in locals() else [],
        'notifications': notifications,
        'unread_notifications_count': unread_notifications_count,
        'audit_dates': audit_dates,
        'audit_requests_data': audit_requests_data,
        'audit_reports_data': audit_reports_data,
        'compliance_data': compliance_data if 'compliance_data' in locals() else [],
        'compliance_categories': compliance_categories if 'compliance_categories' in locals() else json.dumps([]),
        'compliance_scores': compliance_scores if 'compliance_scores' in locals() else json.dumps([])
    }

    return render(request, 'client_portal/volt_dashboard.html', context)

# Client profile view
@login_required
def client_profile(request):
    """
    View for client profile.
    """
    try:
        client_profile = request.user.client_profile
    except ClientProfile.DoesNotExist:
        messages.error(request, 'You do not have a client profile. Please contact the administrator.')
        return redirect('user-login')

    if request.method == 'POST':
        form = ClientProfileUpdateForm(request.POST, request.FILES, instance=client_profile)
        if form.is_valid():
            form.save()
            messages.success(request, 'Your profile has been updated!')
            return redirect('client_profile')
    else:
        form = ClientProfileUpdateForm(instance=client_profile)

    return render(request, 'client_portal/volt_profile.html', {'form': form})

# Audit request views
from django.db import transaction
from django.contrib import messages
from audit_requests.models import AuditRequest as ClientAuditRequest  # Renamed to avoid confusion
from audit_reports.models import AuditReport
from audit_requests.forms import AuditRequestForm

class ClientAuditRequestListView(LoginRequiredMixin, ListView):
    """
    View for listing client's audit requests.
    """
    model = ClientAuditRequest
    template_name = 'client_portal/volt_audit_request_list.html'
    context_object_name = 'audit_requests'
    paginate_by = 10

    def get_queryset(self):
        try:
            # Get client profile
            client_profile = self.request.user.client_profile

            # Start with an empty queryset
            queryset = ClientAuditRequest.objects.none()

            # Find audit requests by email
            if self.request.user.email:
                email_requests = ClientAuditRequest.objects.filter(email=self.request.user.email)
                queryset = email_requests

            # Find audit requests by client name
            if client_profile.client:
                client_name = f"{client_profile.client.first_name} {client_profile.client.last_name}".strip()
                name_requests = ClientAuditRequest.objects.filter(institution_name=client_name)

                # Combine the querysets
                queryset = queryset | name_requests

                # Also try to find requests where the institution name contains parts of the client name
                first_name = client_profile.client.first_name.strip().lower()
                last_name = client_profile.client.last_name.strip().lower()

                if first_name:
                    first_name_requests = ClientAuditRequest.objects.filter(
                        institution_name__icontains=first_name
                    )
                    queryset = queryset | first_name_requests

                if last_name:
                    last_name_requests = ClientAuditRequest.objects.filter(
                        institution_name__icontains=last_name
                    )
                    queryset = queryset | last_name_requests

            # Return the combined queryset, removing duplicates and ordering by date
            return queryset.distinct().order_by('-date')

        except ClientProfile.DoesNotExist:
            return ClientAuditRequest.objects.none()

class ClientAuditRequestDetailView(LoginRequiredMixin, UserPassesTestMixin, DetailView):
    """
    View for displaying client's audit request details.
    """
    model = ClientAuditRequest
    template_name = 'client_portal/volt_audit_request_detail.html'
    context_object_name = 'audit_request'

    def get_login_url(self):
        """Override to redirect to client login page."""
        messages.error(self.request, "You need to log in to view this audit request.")
        return reverse('client_login')

    def handle_no_permission(self):
        """Override to provide a better error message."""
        if self.request.user.is_authenticated:
            messages.error(self.request, "You don't have permission to view this audit request.")
            return redirect('client_dashboard')
        return super().handle_no_permission()

    def get_context_data(self, **kwargs):
        """Add related audit reports to the context."""
        context = super().get_context_data(**kwargs)

        # Get the audit request
        audit_request = self.get_object()

        # Add all fields of the audit request to the context for template debugging
        context['audit_request_fields'] = {field.name: getattr(audit_request, field.name, None) for field in audit_request._meta.fields}

        try:
            from audit_reports.models import ScriptGeneratedReport

            if audit_request.email:
                client_reports = ScriptGeneratedReport.objects.filter(
                    client__user__email=audit_request.email
                ).order_by('-date_created')
                if client_reports.exists():
                    context['related_script_reports'] = client_reports

            from audit_reports.models import AuditReport
            if audit_request.institution_name:
                audit_reports = AuditReport.objects.filter(
                    client_name=audit_request.institution_name
                ).order_by('-date')
                if audit_reports.exists():
                    context['related_audit_reports'] = audit_reports

        except (ImportError, Exception) as e:
            print(f"Error finding related reports: {str(e)}")

        return context

    def test_func(self):
        """
        Check if the current user has permission to view this audit request.
        A client can view an audit request if:
        1. They created it (email matches)
        2. The institution name matches their name
        """
        audit_request = self.get_object()

        # If user is staff or superuser, allow access
        if self.request.user.is_staff or self.request.user.is_superuser:
            return True

        try:
            # Get client profile
            client_profile = self.request.user.client_profile

            # If email matches, allow access
            if client_profile.user.email and audit_request.email:
                if client_profile.user.email.lower() == audit_request.email.lower():
                    return True

            # If client exists, check if institution name matches
            if client_profile.client:
                client_name = f"{client_profile.client.first_name} {client_profile.client.last_name}".strip().lower()
                institution_name = audit_request.institution_name.lower() if audit_request.institution_name else ""

                # Check if client name is part of institution name or vice versa
                if client_name in institution_name or institution_name in client_name:
                    return True

                # Check if they're similar enough (simple check)
                if client_name and institution_name and (
                    client_name.split()[0] in institution_name or
                    (len(client_name.split()) > 1 and client_name.split()[1] in institution_name)
                ):
                    return True

            # If we get here, the user doesn't have permission
            return False

        except (ClientProfile.DoesNotExist, AttributeError) as e:
            # Log the error for debugging
            print(f"Error in test_func: {str(e)}")
            return False

@login_required
def create_audit_request(request):
    """
    View for creating an audit request.
    """
    if request.method == 'POST':
        form = AuditRequestForm(request.POST, request.FILES, client=request.user)
        print(f"Form data: {request.POST}")
        print(f"Files: {request.FILES}")
        if form.is_valid():
            print("Form is valid!")
        else:
            print(f"Form errors: {form.errors}")
            messages.error(request, f"Form validation failed: {form.errors}")
        if form.is_valid():
            with transaction.atomic():
                # Get client profile
                client_profile = request.user.client_profile
                if not client_profile.client:
                    messages.error(request, 'Your client profile is not properly set up. Please contact the administrator.')
                    return redirect('client_dashboard')

                # Create audit request
                audit_request = form.save(commit=False)

                # Set other fields
                audit_request.status = 'pending'

                # Set contact information if not provided
                if not audit_request.phone_number and client_profile.client.phone:
                    audit_request.phone_number = client_profile.client.phone

                if not audit_request.email and client_profile.client.email:
                    audit_request.email = client_profile.client.email

                if not audit_request.address and client_profile.client.address:
                    audit_request.address = client_profile.client.address

                # Set description to include title and categories if provided
                title = form.cleaned_data.get('title', '')
                categories = form.cleaned_data.get('categories', [])

                if title:
                    if audit_request.description:
                        audit_request.description = f"Title: {title}\n\n{audit_request.description}"
                    else:
                        audit_request.description = f"Title: {title}"

                # Handle single category selection
                category_id = form.cleaned_data.get('categories')
                if category_id:
                    from equipment.models import Category
                    try:
                        category = Category.objects.get(id=category_id)
                        audit_request.description = f"{audit_request.description}\n\nCategory: {category.name}"
                    except Category.DoesNotExist:
                        pass

                # Handle equipment selection
                equipment_ids = form.cleaned_data.get('equipment', [])
                print(f"Equipment IDs: {equipment_ids}")

                from equipment.models import Item
                equipment_names = []

                # Handle both list and single value cases
                if isinstance(equipment_ids, list):
                    for eq_id in equipment_ids:
                        try:
                            item = Item.objects.get(id=eq_id)
                            equipment_names.append(f"{item.name} ({item.category.name})")
                        except Item.DoesNotExist:
                            pass
                elif equipment_ids:  # Handle single value
                    try:
                        item = Item.objects.get(id=equipment_ids)
                        equipment_names.append(f"{item.name} ({item.category.name})")
                    except Item.DoesNotExist:
                        pass

                if equipment_names:
                    equipment_str = "\n- " + "\n- ".join(equipment_names)
                    audit_request.description = f"{audit_request.description}\n\nSelected Equipment:{equipment_str}"

                # Handle script selection
                script_id = form.cleaned_data.get('script')
                if script_id:
                    from audit_process.models import AuditScript
                    try:
                        script = AuditScript.objects.get(id=script_id)
                        audit_request.description = f"{audit_request.description}\n\nSelected Script: {script.name} {f'(v{script.version})' if script.version else ''}"
                    except AuditScript.DoesNotExist:
                        pass

                # Handle config file if uploaded
                config_file = form.cleaned_data.get('config_file')
                if config_file:
                    audit_request.description = f"{audit_request.description}\n\nConfig file: {config_file.name}"

                audit_request.save()

                # Save many-to-many relationships
                form.save_m2m()

                # Set a default total of 0 since we're not tracking items anymore
                audit_request.total = 0
                audit_request.save()

                # Create notifications for all auditors
                auditor_profiles = Profile.objects.filter(role='AU')
                for auditor_profile in auditor_profiles:
                    # Create detailed notification message
                    notification_message = f"New audit request from {audit_request.institution_name}\n"
                    notification_message += f"Contact: {audit_request.phone_number}, {audit_request.email}\n"

                    # Add equipment details if available
                    if equipment_names:
                        notification_message += f"Equipment: {', '.join(equipment_names)}\n"

                    # Add category if available
                    if category_id:
                        notification_message += f"Category: {category.name}\n"

                    # Add script if available
                    if script_id:
                        try:
                            script = AuditScript.objects.get(id=script_id)
                            notification_message += f"Script: {script.name} {f'(v{script.version})' if script.version else ''}\n"
                        except AuditScript.DoesNotExist:
                            pass

                    # Create the notification
                    AuditorNotification.objects.create(
                        auditor=auditor_profile,
                        notification_type='audit_request',
                        title=f"New Audit Request: {title}",
                        message=notification_message,
                        related_id=audit_request.id,
                        is_read=False
                    )

                messages.success(request, 'Your audit request has been created successfully!')
                try:
                    # Try to redirect to the detail page
                    return redirect('client_audit_request_detail', pk=audit_request.pk)
                except Exception as e:
                    # If there's an error, log it and redirect to the list page
                    print(f"Error redirecting to detail page: {str(e)}")
                    messages.info(request, 'Your request was created, but there was an issue viewing the details. You can see it in the list below.')
                    return redirect('client_audit_request_list')
    else:
        form = AuditRequestForm(client=request.user)

    return render(request, 'client_portal/volt_audit_request_form.html', {'form': form})

# Audit report views
class ClientAuditReportListView(LoginRequiredMixin, ListView):
    """
    View for listing client's audit reports.
    """
    model = AuditReport
    template_name = 'client_portal/volt_audit_report_list.html'
    context_object_name = 'audit_reports'
    paginate_by = 10

    def get_queryset(self):
        """
        Get both standard audit reports and script-generated reports.
        We'll handle the combined display in the template.
        """
        try:
            # Get client profile
            client_profile = self.request.user.client_profile

            # Start with standard audit reports
            standard_reports = []
            if client_profile.client:
                client_name = f"{client_profile.client.first_name} {client_profile.client.last_name}".strip()
                standard_reports = list(AuditReport.objects.filter(client_name=client_name).order_by('-date'))

            # Get script-generated reports
            from audit_reports.models import ScriptGeneratedReport
            script_reports = []

            if client_profile:
                # Get reports directly linked to this client
                client_reports = list(ScriptGeneratedReport.objects.filter(client=client_profile).order_by('-date_created'))
                script_reports.extend(client_reports)

                # Also get reports by email
                if self.request.user.email:
                    email_reports = list(ScriptGeneratedReport.objects.filter(
                        client__user__email=self.request.user.email
                    ).exclude(id__in=[r.id for r in client_reports]).order_by('-date_created'))
                    script_reports.extend(email_reports)

            # Combine the reports
            # We'll return both types and handle the display in the template
            return script_reports + standard_reports

        except (ClientProfile.DoesNotExist, ImportError) as e:
            print(f"Error getting audit reports: {str(e)}")
            return []

    def get_context_data(self, **kwargs):
        """Add additional context for the template."""
        context = super().get_context_data(**kwargs)

        # Separate the reports by type
        from audit_reports.models import ScriptGeneratedReport

        script_reports = [r for r in context['audit_reports'] if isinstance(r, ScriptGeneratedReport)]
        standard_reports = [r for r in context['audit_reports'] if not isinstance(r, ScriptGeneratedReport)]

        context['script_reports'] = script_reports
        context['standard_reports'] = standard_reports

        return context

class ClientAuditReportDetailView(LoginRequiredMixin, UserPassesTestMixin, DetailView):
    """
    View for displaying client's audit report details.
    """
    model = AuditReport
    template_name = 'client_portal/volt_audit_report_detail.html'
    context_object_name = 'audit_report'

    def test_func(self):
        audit_report = self.get_object()
        try:
            client_profile = self.request.user.client_profile
            if client_profile.client:
                client_name = f"{client_profile.client.first_name} {client_profile.client.last_name}".strip()
                return audit_report.client_name == client_name
            return False
        except ClientProfile.DoesNotExist:
            return False


class ClientScriptReportDetailView(LoginRequiredMixin, UserPassesTestMixin, DetailView):
    """
    View for displaying client's script-generated report details.
    """
    from audit_reports.models import ScriptGeneratedReport, ComplianceData
    model = ScriptGeneratedReport
    template_name = 'client_portal/volt_script_report_detail_simplified.html'
    context_object_name = 'report'

    def test_func(self):
        """
        Check if the current user has permission to view this report.
        A client can view a report if:
        1. They are the client associated with the report
        2. Their email matches the client's email
        """
        report = self.get_object()

        try:
            client_profile = self.request.user.client_profile

            # If this report is directly linked to this client
            if report.client and report.client == client_profile:
                return True

            # If client email matches
            if client_profile.user.email and report.client and report.client.user.email:
                if client_profile.user.email.lower() == report.client.user.email.lower():
                    return True

            # If client name matches
            if client_profile.client and report.client:
                client_name = f"{client_profile.client.first_name} {client_profile.client.last_name}".strip().lower()
                report_client_name = f"{report.client.user.first_name} {report.client.user.last_name}".strip().lower()

                if client_name == report_client_name:
                    return True

            return False

        except (ClientProfile.DoesNotExist, AttributeError) as e:
            print(f"Error in test_func: {str(e)}")
            return False

    def get_context_data(self, **kwargs):
        """Add compliance data to the context."""
        context = super().get_context_data(**kwargs)

        # Get the report
        report = self.get_object()

        # Add the report file URL to the context
        if report.report_file:
            context['csv_file_url'] = report.report_file.url

        # Get compliance data
        try:
            from audit_reports.models import ComplianceData
            import csv
            import io

            # Try to parse the CSV file directly
            if report.report_file:
                try:
                    # Reset the file pointer to the beginning
                    report.report_file.seek(0)
                    csv_data = report.report_file.read().decode('utf-8')

                    # Debug output
                    print(f"Client view - CSV file path: {report.report_file.path}")
                    print(f"Client view - CSV data first 200 chars: {csv_data[:200]}")

                    # Parse the CSV data
                    csv_reader = csv.reader(io.StringIO(csv_data))

                    # Skip header
                    header = next(csv_reader)

                    # Process rows
                    rows = list(csv_reader)
                    total_checks = len(rows)

                    # Count passed and failed checks based on the CSV format
                    passed_checks = 0
                    failed_checks = 0

                    # Simple approach to count passed and failed checks
                    # We'll look for "conforme" and "non conforme" in any column
                    for row in rows:
                        row_str = ' '.join(row).lower()
                        if 'conforme' in row_str and 'non' not in row_str:
                            passed_checks += 1
                        elif 'non conforme' in row_str or 'non-conforme' in row_str:
                            failed_checks += 1

                    # If no checks were counted, try a more direct approach
                    if passed_checks == 0 and failed_checks == 0 and total_checks > 0:
                        # Try direct string search in the CSV data
                        passed_checks = csv_data.lower().count('"conforme"') + csv_data.lower().count(',conforme,')
                        failed_checks = csv_data.lower().count('"non conforme"') + csv_data.lower().count('"non-conforme"') + csv_data.lower().count(',non conforme,') + csv_data.lower().count(',non-conforme,')

                    # Create a temporary compliance data object
                    class TempComplianceData:
                        def __init__(self, total, passed, failed):
                            self.total_checks = total
                            self.passed_checks = passed
                            self.failed_checks = failed
                            self.compliance_score = (passed / total) * 100 if total > 0 else 0

                    context['compliance_data'] = TempComplianceData(
                        total=total_checks,
                        passed=passed_checks,
                        failed=failed_checks
                    )

                    # Update the actual compliance data in the database
                    compliance_data = ComplianceData.objects.filter(report=report).order_by('-date_checked').first()
                    if compliance_data:
                        compliance_data.total_checks = total_checks
                        compliance_data.passed_checks = passed_checks
                        compliance_data.failed_checks = failed_checks
                        compliance_data.calculate_compliance_score()
                        compliance_data.save()

                        # Update the report's compliance score
                        report.compliance_score = compliance_data.compliance_score
                        report.is_compliant = compliance_data.compliance_score >= 70
                        report.save(update_fields=['compliance_score', 'is_compliant'])
                except Exception as e:
                    print(f"Error parsing CSV file: {str(e)}")

                    # If there was an error parsing the CSV, fall back to the database
                    compliance_data = ComplianceData.objects.filter(report=report).order_by('-date_checked').first()
                    if compliance_data:
                        context['compliance_data'] = compliance_data
            else:
                # If there's no CSV file, use the compliance data from the database
                compliance_data = ComplianceData.objects.filter(report=report).order_by('-date_checked').first()
                if compliance_data:
                    context['compliance_data'] = compliance_data
        except Exception as e:
            print(f"Error getting compliance data: {str(e)}")

        return context

# Notification views
@login_required
def mark_notification_as_read(request, pk):
    """
    View for marking a notification as read.
    """
    try:
        notification = get_object_or_404(ClientNotification, pk=pk, client=request.user.client_profile)
        notification.is_read = True
        notification.save()
    except Exception as e:
        # If there's an error with the notification, just ignore it
        pass

    # Redirect back to the dashboard
    return redirect('client_dashboard')

class ClientNotificationListView(LoginRequiredMixin, ListView):
    """
    View for listing client's notifications.
    """
    model = ClientNotification
    template_name = 'client_portal/volt_notification_list.html'
    context_object_name = 'notifications'
    paginate_by = 10

    def get_queryset(self):
        try:
            return ClientNotification.objects.filter(client=self.request.user.client_profile).order_by('-created_at')
        except Exception as e:
            # If there's an error with the notifications, just return an empty queryset
            return ClientNotification.objects.none()


@login_required
def client_password_change(request):
    """
    View for changing client's password.
    """
    if request.method == 'POST':
        form = PasswordChangeForm(request.user, request.POST)
        if form.is_valid():
            user = form.save()
            # Update the session to prevent the user from being logged out
            update_session_auth_hash(request, user)
            messages.success(request, 'Your password has been changed successfully!')
            return redirect('client_profile')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = PasswordChangeForm(request.user)

    return render(request, 'client_portal/volt_password_change.html', {'form': form})
