{% extends "equipment/base.html" %}
{% load static %}
{% block title %}Ongoing Audit Detail{% endblock title %}
{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">Ongoing Audit Details</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted">Basic Information</h6>
                            <hr>
                            <p><strong>ID:</strong> {{ object.id }}</p>
                            <p><strong>Equipment:</strong> {{ object.item.name }}</p>
                            <p><strong>Description:</strong> {{ object.description|default:"Not provided" }}</p>
                            <p><strong>Auditor:</strong> {{ object.auditor.name }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Audit <PERSON>ails</h6>
                            <hr>
                            <p><strong>Start Date:</strong> {{ object.start_date|date:"Y-m-d H:i" }}</p>
                            <p><strong>Completion Date:</strong> {{ object.completion_date|date:"Y-m-d H:i"|default:"Not completed" }}</p>
                            <p><strong>Audit Status:</strong> {{ object.get_audit_status_display }}</p>
                            <p><strong>Audit Type:</strong> {{ object.get_audit_type_display }}</p>

                            {% if object.audit_type == 'auto' %}
                            <p><strong>Audit Script:</strong>
                                {% if object.script %}
                                    <a href="{{ object.script.script_file.url }}" class="btn btn-sm btn-primary" download>
                                        <i class="fa-solid fa-download me-1"></i> {{ object.script.name }}{% if object.script.version %} (v{{ object.script.version }}){% endif %}
                                    </a>
                                    {% if object.script.description %}
                                        <div class="mt-2 small text-muted">{{ object.script.description }}</div>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">No script selected</span>
                                {% endif %}
                            </p>
                            {% else %}
                            <p><strong>Manual Compliance:</strong>
                                <a href="{% url 'manual_audit_compliance_list' %}" class="btn btn-sm btn-primary">
                                    <i class="fa-solid fa-clipboard-check me-1"></i> View Compliance Points
                                </a>
                                <div class="mt-2 small text-muted">Manage manual compliance points and evidence files</div>
                            </p>
                            {% endif %}
                        </div>
                    </div>
                    <div class="d-flex justify-content-end mt-4">
                        <a href="{% url 'ongoing_audit_list' %}" class="btn btn-secondary me-2">
                            <i class="fas fa-arrow-left me-1"></i> Back to List
                        </a>
                        <a href="{% url 'ongoing_audit_update' object.pk %}" class="btn btn-warning me-2">
                            <i class="fas fa-edit me-1"></i> Edit
                        </a>
                        <a href="{% url 'ongoing_audit_delete' object.pk %}" class="btn btn-danger">
                            <i class="fas fa-trash-alt me-1"></i> Delete
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}
