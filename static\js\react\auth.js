import React, { useState, useEffect } from 'react';
import ReactD<PERSON> from 'react-dom';
import axios from 'axios';

// Set up axios defaults
axios.defaults.baseURL = window.location.origin;

// Auth service for JWT
const authService = {
  setTokens: (access, refresh) => {
    localStorage.setItem('access_token', access);
    localStorage.setItem('refresh_token', refresh);
    axios.defaults.headers.common['Authorization'] = `Bearer ${access}`;
  },
  
  getAccessToken: () => {
    return localStorage.getItem('access_token');
  },
  
  getRefreshToken: () => {
    return localStorage.getItem('refresh_token');
  },
  
  clearTokens: () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    delete axios.defaults.headers.common['Authorization'];
  },
  
  isAuthenticated: () => {
    return !!localStorage.getItem('access_token');
  },
  
  login: async (username, password) => {
    try {
      const response = await axios.post('/api/auth/token/', { username, password });
      const { access, refresh } = response.data;
      authService.setTokens(access, refresh);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  
  logout: async () => {
    try {
      const refresh = authService.getRefreshToken();
      if (refresh) {
        await axios.post('/api/auth/logout/', { refresh });
      }
      authService.clearTokens();
    } catch (error) {
      console.error('Logout error:', error);
      authService.clearTokens();
    }
  },
  
  refreshToken: async () => {
    try {
      const refresh = authService.getRefreshToken();
      if (!refresh) throw new Error('No refresh token available');
      
      const response = await axios.post('/api/auth/token/refresh/', { refresh });
      const { access } = response.data;
      
      localStorage.setItem('access_token', access);
      axios.defaults.headers.common['Authorization'] = `Bearer ${access}`;
      
      return access;
    } catch (error) {
      authService.clearTokens();
      throw error;
    }
  },
  
  getUserData: async () => {
    try {
      const response = await axios.get('/api/auth/user/data/');
      return response.data;
    } catch (error) {
      throw error;
    }
  }
};

// Set up axios interceptor for token refresh
axios.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    // If error is 401 and we haven't tried to refresh the token yet
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        // Try to refresh the token
        await authService.refreshToken();
        
        // Retry the original request with the new token
        return axios(originalRequest);
      } catch (refreshError) {
        // If refresh fails, redirect to login
        authService.clearTokens();
        window.location.href = '/accounts/login/';
        return Promise.reject(refreshError);
      }
    }
    
    return Promise.reject(error);
  }
);

// Login component
const LoginForm = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(authService.isAuthenticated());
  const [userData, setUserData] = useState(null);
  
  useEffect(() => {
    // Check if user is already logged in
    if (authService.isAuthenticated()) {
      fetchUserData();
    }
  }, []);
  
  const fetchUserData = async () => {
    try {
      const data = await authService.getUserData();
      setUserData(data);
    } catch (error) {
      console.error('Error fetching user data:', error);
      if (error.response?.status === 401) {
        setIsLoggedIn(false);
      }
    }
  };
  
  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    
    try {
      await authService.login(username, password);
      setIsLoggedIn(true);
      await fetchUserData();
    } catch (error) {
      console.error('Login error:', error);
      setError(error.response?.data?.detail || 'Login failed. Please check your credentials.');
    } finally {
      setLoading(false);
    }
  };
  
  const handleLogout = async () => {
    setLoading(true);
    try {
      await authService.logout();
      setIsLoggedIn(false);
      setUserData(null);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setLoading(false);
    }
  };
  
  if (isLoggedIn && userData) {
    return (
      <div className="card">
        <div className="card-body">
          <h5 className="card-title">Welcome, {userData.username}!</h5>
          <p className="card-text">You are logged in with JWT authentication.</p>
          <p><strong>Email:</strong> {userData.email}</p>
          <p><strong>Role:</strong> {userData.role || 'Not assigned'}</p>
          <button 
            className="btn btn-danger" 
            onClick={handleLogout}
            disabled={loading}
          >
            {loading ? 'Logging out...' : 'Logout'}
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="card">
      <div className="card-body">
        <h5 className="card-title">JWT Login</h5>
        {error && <div className="alert alert-danger">{error}</div>}
        <form onSubmit={handleLogin}>
          <div className="mb-3">
            <label htmlFor="username" className="form-label">Username</label>
            <input
              type="text"
              className="form-control"
              id="username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              required
            />
          </div>
          <div className="mb-3">
            <label htmlFor="password" className="form-label">Password</label>
            <input
              type="password"
              className="form-control"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>
          <button 
            type="submit" 
            className="btn btn-primary"
            disabled={loading}
          >
            {loading ? 'Logging in...' : 'Login'}
          </button>
        </form>
      </div>
    </div>
  );
};

// Initialize the component when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  const jwtLoginRoot = document.getElementById('jwt-login-root');
  if (jwtLoginRoot) {
    ReactDOM.render(<LoginForm />, jwtLoginRoot);
  }
});

export default LoginForm;
