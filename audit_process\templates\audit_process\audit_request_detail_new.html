{% extends "equipment/base.html" %} {% load static %}{% block title %}Audit Request Detail{% endblock %}
{% block content %}
<style>
    .badge-pending {
        background-color: #ffc107;
        color: #212529;
    }
    .badge-approved {
        background-color: #28a745;
        color: white;
    }
    .badge-rejected {
        background-color: #dc3545;
        color: white;
    }
    .badge-completed {
        background-color: #17a2b8;
        color: white;
    }
</style>

<div class="container mb-4">
    <div class="row">
        <div class="col-md-8">
            <h2>{{ audit_request.title }}</h2>
            <div class="badge badge-{{ audit_request.status }} mb-3">{{ audit_request.status|title }}</div>
            <p class="text-muted">Requested on {{ audit_request.date_added|date:"F d, Y" }} at {{ audit_request.date_added|time:"H:i" }}</p>

            {% if audit_request.description %}
            <div class="card mb-3">
                <div class="card-header" style="background-color: #023047; color: white;">
                    <h5 class="mb-0">Description</h5>
                </div>
                <div class="card-body">
                    <p>{{ audit_request.description }}</p>
                </div>
            </div>
            {% endif %}

            {% if audit_request.categories.all %}
            <div class="card mb-3">
                <div class="card-header" style="background-color: #023047; color: white;">
                    <h5 class="mb-0">Audit Categories</h5>
                </div>
                <div class="card-body">
                    {% for category in audit_request.categories.all %}
                    <span class="badge bg-info me-2">{{ category.name }}</span>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            {% if audit_request.config_file %}
            <div class="card mb-3">
                <div class="card-header" style="background-color: #023047; color: white;">
                    <h5 class="mb-0">Configuration File</h5>
                </div>
                <div class="card-body">
                    <a href="{{ audit_request.config_file.url }}" class="btn btn-sm btn-outline-primary" download>
                        <i class="fas fa-download me-2"></i> Download Configuration File
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
        <div class="col-md-4 text-end">
            <div class="d-flex justify-content-end mb-3">
                <a href="{% url 'audit_request_list' %}" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-left me-2"></i> Back
                </a>
                <a href="#" class="btn btn-outline-primary">
                    <i class="fa fa-print me-2"></i> Print
                </a>
            </div>

            {% if audit_request.status == 'pending' and not request.user.client_profile %}
            <div class="card border-light shadow-sm mb-4">
                <div class="card-header" style="background-color: #023047; color: white;">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">This audit request is waiting for your response.</p>
                    <a href="{% url 'launch_audit' audit_request.id %}" class="btn btn-success w-100 mb-2">
                        <i class="fa fa-check me-2"></i> Accept and Launch Audit
                    </a>
                    <a href="{% url 'reject_audit' audit_request.id %}" class="btn btn-danger w-100">
                        <i class="fa fa-times me-2"></i> Reject Request
                    </a>
                </div>
            </div>
            {% elif audit_request.status == 'pending' and request.user.client_profile %}
            <div class="card border-light shadow-sm mb-4">
                <div class="card-header" style="background-color: #023047; color: white;">
                    <h5 class="mb-0">Status</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Your audit request is pending review by our auditors.</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-clock me-2"></i> Waiting for auditor response
                    </div>
                </div>
            </div>
            {% endif %}

            <div class="card border-light shadow-sm">
                <div class="card-header" style="background-color: #023047; color: white;">
                    <h5 class="mb-0">Client Information</h5>
                </div>
                <div class="card-body">
                    <p><strong>Name:</strong> {{ audit_request.customer.first_name }} {{ audit_request.customer.last_name }}</p>
                    <p><strong>Email:</strong> {{ audit_request.customer.email }}</p>
                    <p><strong>Phone:</strong> {{ audit_request.customer.phone }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container mb-5">
    <div class="card border-light shadow-sm">
        <div class="card-header" style="background-color: #023047; color: white;">
            <h5 class="mb-0">Items to Audit</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Item</th>
                            <th>Description</th>
                            <th class="text-center">Quantity</th>
                            <th class="text-end">Price</th>
                            <th class="text-end">Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for detail in audit_request.auditrequestdetail_set.all %}
                        <tr>
                            <td>{{ detail.item.name }}</td>
                            <td>{{ detail.item.description|truncatechars:50 }}</td>
                            <td class="text-center">{{ detail.quantity }}</td>
                            <td class="text-end">${{ detail.price|floatformat:2 }}</td>
                            <td class="text-end">${{ detail.total_detail|floatformat:2 }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="text-center">No items found</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="4" class="text-end"><strong>Total:</strong></td>
                            <td class="text-end"><strong>${{ audit_request.total|floatformat:2 }}</strong></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>

{% if audit_request.ongoing_audits.all %}
<div class="container mb-5">
    <div class="card border-light shadow-sm">
        <div class="card-header" style="background-color: #023047; color: white;">
            <h5 class="mb-0">Ongoing Audits</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Auditor</th>
                            <th>Start Date</th>
                            <th>Status</th>
                            <th class="text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for audit in audit_request.ongoing_audits.all %}
                        <tr>
                            <td>{{ audit.id }}</td>
                            <td>{{ audit.auditor.name }}</td>
                            <td>{{ audit.start_date|date:"F d, Y" }}</td>
                            <td>
                                {% if audit.audit_status == 'P' %}
                                <span class="badge bg-warning">In Progress</span>
                                {% elif audit.audit_status == 'S' %}
                                <span class="badge bg-success">Completed</span>
                                {% elif audit.audit_status == 'R' %}
                                <span class="badge bg-danger">Rejected</span>
                                {% endif %}
                            </td>
                            <td class="text-end">
                                <a href="{% url 'ongoing_audit_detail' audit.id %}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-eye me-1"></i> View
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="text-center">No ongoing audits found</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}
