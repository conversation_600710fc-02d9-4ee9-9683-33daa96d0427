# Generated by Django 5.1 on 2025-04-25 13:08

import django.db.models.deletion
import django_extensions.db.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("accounts", "0004_alter_profile_telephone"),
        ("equipment", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="AuditCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True, null=True)),
            ],
            options={
                "verbose_name": "Audit Category",
                "verbose_name_plural": "Audit Categories",
                "db_table": "audit_categories",
            },
        ),
        migrations.CreateModel(
            name="AuditRequest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "date_added",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Request Date"
                    ),
                ),
                ("title", models.CharField(default="Audit Request", max_length=200)),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "config_file",
                    models.FileField(blank=True, null=True, upload_to="audit_configs/"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("completed", "Completed"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                (
                    "total",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                (
                    "categories",
                    models.ManyToManyField(
                        blank=True,
                        related_name="audit_requests",
                        to="audit_process.auditcategory",
                    ),
                ),
                (
                    "customer",
                    models.ForeignKey(
                        db_column="customer",
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="accounts.client",
                    ),
                ),
            ],
            options={
                "verbose_name": "Audit Request",
                "verbose_name_plural": "Audit Requests",
                "db_table": "audit_requests",
            },
        ),
        migrations.CreateModel(
            name="AuditRequestDetail",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("price", models.DecimalField(decimal_places=2, max_digits=10)),
                ("quantity", models.PositiveIntegerField()),
                (
                    "total_detail",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                (
                    "audit_request",
                    models.ForeignKey(
                        db_column="audit_request",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="auditrequestdetail_set",
                        to="audit_process.auditrequest",
                    ),
                ),
                (
                    "item",
                    models.ForeignKey(
                        db_column="item",
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="equipment.item",
                    ),
                ),
            ],
            options={
                "verbose_name": "Audit Request Detail",
                "verbose_name_plural": "Audit Request Details",
                "db_table": "audit_request_details",
            },
        ),
        migrations.CreateModel(
            name="OngoingAudit",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "slug",
                    django_extensions.db.fields.AutoSlugField(
                        blank=True, editable=False, populate_from="vendor", unique=True
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, max_length=300, null=True),
                ),
                ("start_date", models.DateTimeField(auto_now_add=True)),
                (
                    "completion_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Completion Date"
                    ),
                ),
                ("quantity", models.PositiveIntegerField(default=0)),
                (
                    "audit_status",
                    models.CharField(
                        choices=[
                            ("P", "In Progress"),
                            ("S", "Completed"),
                            ("R", "Rejected"),
                        ],
                        default="P",
                        max_length=1,
                        verbose_name="Audit Status",
                    ),
                ),
                (
                    "price",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="Audit Fee (Ksh)",
                    ),
                ),
                ("total_value", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "notes",
                    models.TextField(
                        blank=True, null=True, verbose_name="Auditor Notes"
                    ),
                ),
                (
                    "findings",
                    models.TextField(
                        blank=True, null=True, verbose_name="Audit Findings"
                    ),
                ),
                (
                    "audit_request",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ongoing_audits",
                        to="audit_process.auditrequest",
                    ),
                ),
                (
                    "item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="equipment.item"
                    ),
                ),
                (
                    "vendor",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ongoing_audits",
                        to="accounts.auditor",
                    ),
                ),
            ],
            options={
                "verbose_name": "Ongoing Audit",
                "verbose_name_plural": "Ongoing Audits",
                "db_table": "ongoing_audits",
                "ordering": ["start_date"],
            },
        ),
    ]
