<div class="row">
    <div class="card shadow border-0 mb-7 col-md-6 col-lg-6">
        <div class="card-body">
            <h5 class="card-title">Category Distribution</h5>
            <div class="chart-container">
                <canvas id="pieChart"></canvas>
            </div>
        </div>
    </div>
    <div class="card shadow border-0 mb-7 col-md-6 col-lg-6">
        <div class="card-body">
            <h5 class="card-title">Audits Performed Over Time</h5>
            <div class="chart-container">
                <canvas id="lineChart"></canvas>
            </div>
        </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    // Pie Chart
    var ctxPie = document.getElementById('pieChart').getContext('2d');
    var pieChart = new Chart(ctxPie, {
        type: 'doughnut',
        data: {
            labels: {{ categories|safe }},
            datasets: [{
                data: {{ category_counts|safe }},
                backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#E7E9ED', '#8E5EA2'],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 10
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(tooltipItem) {
                            return tooltipItem.label + ': ' + tooltipItem.raw;
                        }
                    }
                },
                title: {
                    display: true,
                    text: 'Category Distribution',
                    font: {
                        size: 16
                    }
                }
            },
            cutout: '60%',
            maintainAspectRatio: false
        }
    });

    // Line Chart
    var ctxLine = document.getElementById('lineChart').getContext('2d');
    var lineChart = new Chart(ctxLine, {
        type: 'line',
        data: {
            labels: {{ audit_request_dates_labels|safe }},
            datasets: [{
                label: 'Completed Audits',
                data: {{ audit_request_dates_values|safe }},
                fill: false,
                borderColor: '#4BC0C0',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    callbacks: {
                        label: function(tooltipItem) {
                            return 'Audits Completed: ' + tooltipItem.raw;
                        }
                    }
                },
                title: {
                    display: true,
                    text: 'Audits Performed Over Time',
                    font: {
                        size: 16
                    }
                }
            },
            maintainAspectRatio: false
        }
    });
  </script>

  <style>
  .chart-container {
    position: relative;
    width: 100%;
    height: 400px;
  }

  #pieChart, #lineChart {
    max-width: 100%;
    height: 100%;
  }
  </style>
