{% extends "equipment/base.html" %}
{% load render_table from django_tables2 %}
{% load querystring from django_tables2 %}
{% load static %}

{% block title %}Audit Reports{% endblock title %}

{% block content %}
<div class="container p-5">
  <style>
    .table th, .table td {
        text-align: center;
        vertical-align: middle;
        padding: 8px;
    }
    /* Make text wrap properly */
    .table td {
        white-space: normal;
        word-wrap: break-word;
    }
    /* Action buttons */
    .action-btn-group {
        display: flex;
        justify-content: center;
        gap: 5px;
    }
    @media (max-width: 992px) {
        .action-btn-group {
            flex-direction: column;
        }
    }
  </style>


    <div class="mb-3">
        <a class="btn btn-success btn-sm" href="{% url 'auditreport-create' %}">
            <i class="fa-solid fa-plus me-2"></i> Create Audit Report
        </a>
        <a class="float-end btn btn-success btn-sm" href="{% querystring '_export'='xlsx' %}">
            <i class="fa-solid fa-download me-2"></i> Export to Excel
        </a>
    </div>

    <div class="m-2">
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead class="thead-light">
                    <tr>
                        <th scope="col">ID</th>
                        <th scope="col">Client Name</th>
                        <th scope="col">Phone Number</th>
                        <th scope="col">Equipment</th>
                        <th scope="col">Audit Result</th>
                        <th scope="col">Description</th>
                        <th scope="col">Findings</th>
                        <th scope="col">Action</th>
                    </tr>
                </thead>
                <tbody>
                    {% for invoice in invoices %}
                    <tr>
                        <th scope="row">{{ invoice.id }}</th>
                        <td><a href="{% url 'auditreport-detail' invoice.slug %}">{{ invoice.client_name }}</a></td>
                        <td>{{ invoice.contact_number }}</td>
                        <td>{{ invoice.equipment.name }}</td>
                        <td>
                            {% if invoice.audit_result %}
                                <span class="badge bg-success">Pass</span>
                            {% else %}
                                <span class="badge bg-danger">Fail</span>
                            {% endif %}
                        </td>
                        <td>{{ invoice.description|truncatechars:50 }}</td>
                        <td>{{ invoice.findings|truncatechars:50 }}</td>
                        <td>
                            <div class="action-btn-group">
                                <a class="btn btn-sm btn-info" href="{% url 'auditreport-update' invoice.slug %}">
                                    <i class="fa-solid fa-pen"></i>
                                </a>
                                <a class="btn btn-sm btn-danger" href="{% url 'auditreport-delete' invoice.pk %}">
                                    <i class="fa-solid fa-trash"></i>
                                </a>
                                <a class="btn btn-sm btn-success" href="{% url 'convert_audit_report_to_script' invoice.pk %}" title="Convert to Script Report for CSV Upload">
                                    <i class="fa-solid fa-file-csv me-1"></i> Convert to Script Report
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    <div class="mt-4">
        {% if is_paginated %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </span>
                </li>
                {% endif %}
                {% for i in paginator.page_range %}
                {% if page_obj.number == i %}
                <li class="page-item active" aria-current="page">
                    <span class="page-link">{{ i }} <span class="visually-hidden">(current)</span></span>
                </li>
                {% else %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ i }}">{{ i }}</a>
                </li>
                {% endif %}
                {% endfor %}
                {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </span>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>
{% endblock content %}
