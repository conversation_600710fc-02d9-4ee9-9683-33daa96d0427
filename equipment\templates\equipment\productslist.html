{% extends "equipment/base.html" %}
{% load static %}
{% block title %}Equipment List{% endblock title %}

{% block content %}
<!-- Header Section -->
<div class="container my-4">
    <div class="card shadow-sm rounded p-3">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h4 class="display-6 mb-0 text-success">Equipment List</h4>
            </div>
            <div class="col-md-6 d-flex justify-content-end gap-2">
                <a class="btn btn-success btn-sm rounded-pill shadow-sm" href="{% url 'product-create' %}">
                    <i class="fa-solid fa-plus"></i> Add Equipment
                </a>
            </div>
        </div>
    </div>
</div>
<div class="container px-3">
    <style>
        .table th, .table td {
            text-align: center;
        }
    </style>
    <table class="table table-sm table-striped table-bordered">
        <thead class="thead-light">
            <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Category</th>
                <th>Version</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for item in items %}
            <tr>
                <td>{{ item.id }}</td>
                <td>{{ item.name }}</td>
                <td>{{ item.category.name }}</td>
                <td>{{ item.version }}</td>
                <td>
                    <a href="{% url 'product-detail' item.slug %}" class="btn btn-outline-primary btn-sm">
                        <i class="fa-solid fa-info-circle me-2"></i> View
                    </a>
                    <a href="{% url 'product-update' item.slug %}" class="btn btn-outline-warning btn-sm">
                        <i class="fa-solid fa-edit me-2"></i> Edit
                    </a>
                    <a href="{% url 'product-delete' item.slug %}" class="btn btn-outline-danger btn-sm">
                        <i class="fa-solid fa-trash me-2"></i> Delete
                    </a>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="text-center py-4">
                    <div class="alert alert-info mb-0">
                        <i class="fa-solid fa-info-circle me-2"></i> No equipment found. Please add some equipment.
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock content %}
