{% extends "equipment/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>{{ title }}</h2>
                <div>
                    <a href="{% url 'manual_compliance_list' audit_id=audit.id %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Compliance Points
                    </a>
                </div>
            </div>
            <p class="text-muted">
                Audit: <strong>{{ audit.item.name }}</strong> | 
                Status: <strong>{{ audit.get_audit_status_display }}</strong> |
                Auditor: <strong>{{ audit.auditor.name }}</strong>
            </p>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Compliance Point Details</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {% csrf_token %}
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                {{ form.point_id|as_crispy_field }}
                            </div>
                            <div class="col-md-8">
                                {{ form.status|as_crispy_field }}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.description|as_crispy_field }}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.command|as_crispy_field }}
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.expected_result|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.actual_result|as_crispy_field }}
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> {{ button_text }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Compliance Point Information</h5>
                </div>
                <div class="card-body">
                    <p class="mb-4">Compliance points represent individual checks performed during a manual audit. Here's what each field means:</p>
                    
                    <div class="d-flex mb-3">
                        <div class="icon-shape icon-shape-sm icon-shape-primary me-3">
                            <i class="fas fa-fingerprint"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">Point ID</h5>
                            <p class="text-muted small mb-0">A unique identifier for this compliance point (e.g., FW-001).</p>
                        </div>
                    </div>
                    
                    <div class="d-flex mb-3">
                        <div class="icon-shape icon-shape-sm icon-shape-secondary me-3">
                            <i class="fas fa-align-left"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">Description</h5>
                            <p class="text-muted small mb-0">What this compliance point is checking for.</p>
                        </div>
                    </div>
                    
                    <div class="d-flex mb-3">
                        <div class="icon-shape icon-shape-sm icon-shape-tertiary me-3">
                            <i class="fas fa-terminal"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">Command</h5>
                            <p class="text-muted small mb-0">The command used to check this compliance point.</p>
                        </div>
                    </div>
                    
                    <div class="d-flex mb-3">
                        <div class="icon-shape icon-shape-sm icon-shape-info me-3">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">Expected Result</h5>
                            <p class="text-muted small mb-0">What result is expected for compliance.</p>
                        </div>
                    </div>
                    
                    <div class="d-flex mb-3">
                        <div class="icon-shape icon-shape-sm icon-shape-warning me-3">
                            <i class="fas fa-clipboard-check"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">Actual Result</h5>
                            <p class="text-muted small mb-0">What was actually found during the audit.</p>
                        </div>
                    </div>
                    
                    <div class="d-flex">
                        <div class="icon-shape icon-shape-sm icon-shape-success me-3">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">Status</h5>
                            <p class="text-muted small mb-0">Whether this point is compliant, non-compliant, or not yet checked.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
