from django.db import models
from django.conf import settings
from django.utils import timezone
from django_extensions.db.fields import AutoSlugField

from equipment.models import Item, Category
from accounts.models import Auditor, Client

AUDIT_STATUS_CHOICES = [("P", "In Progress"), ("S", "Completed"), ("R", "Rejected")]


class AuditScript(models.Model):
    """
    Represents a script file used for auditing equipment.
    """
    name = models.CharField(max_length=100, verbose_name="Script Name")
    description = models.TextField(blank=True, null=True, verbose_name="Script Description")
    script_file = models.FileField(upload_to='audit_scripts/', verbose_name='Script File')
    version = models.CharField(max_length=20, blank=True, null=True, verbose_name="Version")
    category = models.ForeignKey(
        Category,
        on_delete=models.SET_NULL,
        related_name="audit_scripts",
        null=True,
        blank=True,
        verbose_name="Equipment Category"
    )
    date_added = models.DateTimeField(default=timezone.now, verbose_name="Date Added")
    is_active = models.BooleanField(default=True, verbose_name="Active")

    def __str__(self):
        """Return a string representation of the script."""
        if self.version:
            return f"{self.name} (v{self.version})"
        return self.name

    class Meta:
        verbose_name = "Audit Script"
        verbose_name_plural = "Audit Scripts"
        ordering = ["-date_added"]


class AuditCategory(models.Model):
    """
    Represents a category of audit that can be performed.
    """
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)

    class Meta:
        db_table = "audit_categories"
        verbose_name = "Audit Category"
        verbose_name_plural = "Audit Categories"

    def __str__(self):
        return self.name


class AuditRequest(models.Model):
    """
    Represents an audit request from a client.

    NOTE: There is another AuditRequest model in audit_requests/models.py which serves a different
    purpose but has the same name. In the future, these models should be consolidated or renamed
    to avoid confusion. This model is used primarily by the audit process system.
    """

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('completed', 'Completed')
    ]

    date_added = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Request Date"
    )
    customer = models.ForeignKey(
        Client,
        on_delete=models.DO_NOTHING,
        db_column="customer"
    )
    title = models.CharField(max_length=200, default="Audit Request")
    description = models.TextField(blank=True, null=True)
    categories = models.ManyToManyField(AuditCategory, related_name="audit_requests", blank=True)
    config_file = models.FileField(upload_to='audit_configs/', blank=True, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    total = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.0,
        blank=True,
        null=True
    )

    class Meta:
        db_table = "audit_requests"
        verbose_name = "Audit Request"
        verbose_name_plural = "Audit Requests"

    def __str__(self):
        """
        Returns a string representation of the AuditRequest instance.
        """
        return (
            f"Request ID: {self.id} | "
            f"Total: {self.total} | "
            f"Date: {self.date_added}"
        )

    def sum_products(self):
        """
        Returns the total quantity of products in the audit request.
        """
        return sum(detail.quantity for detail in self.auditrequestdetail_set.all())


class AuditRequestDetail(models.Model):
    """
    Represents details of a specific audit request, including item and quantity.
    """

    audit_request = models.ForeignKey(
        AuditRequest,
        on_delete=models.CASCADE,
        db_column="audit_request",
        related_name="auditrequestdetail_set"
    )
    item = models.ForeignKey(
        Item,
        on_delete=models.DO_NOTHING,
        db_column="item"
    )
    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True
    )
    quantity = models.PositiveIntegerField(default=1)
    total_detail = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.0,
        blank=True,
        null=True
    )

    class Meta:
        db_table = "audit_request_details"
        verbose_name = "Audit Request Detail"
        verbose_name_plural = "Audit Request Details"

    def __str__(self):
        """
        Returns a string representation of the AuditRequestDetail instance.
        """
        return (
            f"Detail ID: {self.id} | "
            f"Request ID: {self.audit_request.id} | "
            f"Quantity: {self.quantity}"
        )


class OngoingAudit(models.Model):
    """
    Represents an ongoing audit being conducted by an auditor,
    including status and completion details.
    """
    AUDIT_TYPE_CHOICES = [
        ('auto', 'Automated'),
        ('manual', 'Manual'),
    ]

    slug = AutoSlugField(unique=True, populate_from="auditor")
    audit_request = models.ForeignKey(AuditRequest, on_delete=models.CASCADE, related_name="ongoing_audits", null=True, blank=True)
    # Add a field for ClientAuditRequest (from audit_requests app)
    client_audit_request = models.ForeignKey('audit_requests.AuditRequest', on_delete=models.SET_NULL, related_name="client_ongoing_audits", null=True, blank=True, verbose_name="Client Audit Request")
    item = models.ForeignKey(Item, on_delete=models.CASCADE)
    description = models.TextField(max_length=300, blank=True, null=True)
    auditor = models.ForeignKey(
        Auditor, related_name="ongoing_audits", on_delete=models.CASCADE
    )
    start_date = models.DateTimeField(auto_now_add=True)
    completion_date = models.DateTimeField(
        blank=True, null=True, verbose_name="Completion Date"
    )
    quantity = models.PositiveIntegerField(default=0)
    audit_status = models.CharField(
        choices=AUDIT_STATUS_CHOICES,
        max_length=1,
        default="P",
        verbose_name="Audit Status",
    )
    audit_type = models.CharField(
        choices=AUDIT_TYPE_CHOICES,
        max_length=10,
        default="auto",
        verbose_name="Audit Type",
    )
    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.0,
        blank=True,
        null=True,
        verbose_name="Audit Fee (optional)",
    )
    total_value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True
    )
    notes = models.TextField(blank=True, null=True, verbose_name="Auditor Notes")
    findings = models.TextField(blank=True, null=True, verbose_name="Audit Findings")
    script = models.ForeignKey(
        AuditScript,
        on_delete=models.SET_NULL,
        related_name="ongoing_audits",
        null=True,
        blank=True,
        verbose_name="Audit Script"
    )

    def save(self, *args, **kwargs):
        """
        Calculates the total value before saving the OngoingAudit instance.
        """
        if self.price is not None and self.quantity is not None:
            self.total_value = self.price * self.quantity
        super().save(*args, **kwargs)
        # Item model doesn't have a quantity field, so we don't update it
        # self.item.quantity += self.quantity
        # self.item.save()

    def __str__(self):
        """
        Returns a string representation of the OngoingAudit instance.
        """
        return f"{self.item.name} - Auditor: {self.auditor.name}"

    class Meta:
        db_table = "ongoing_audits"
        verbose_name = "Ongoing Audit"
        verbose_name_plural = "Ongoing Audits"
        ordering = ["start_date"]


class ManualCompliancePoint(models.Model):
    """
    Represents a manual compliance point for an audit.
    Used for manual audits like firewall audits.
    """
    audit = models.ForeignKey(
        OngoingAudit,
        on_delete=models.CASCADE,
        related_name="compliance_points",
        verbose_name="Audit"
    )
    point_id = models.CharField(
        max_length=20,
        verbose_name="ID"
    )
    description = models.TextField(
        verbose_name="Description"
    )
    command = models.TextField(
        verbose_name="Audit Command",
        blank=True,
        null=True
    )
    expected_result = models.TextField(
        verbose_name="Expected Result",
        blank=True,
        null=True
    )
    actual_result = models.TextField(
        verbose_name="Actual Result",
        blank=True,
        null=True
    )
    status = models.CharField(
        max_length=20,
        choices=[
            ('conforme', 'Conforme'),
            ('non_conforme', 'Non Conforme'),
            ('not_checked', 'Not Checked')
        ],
        default='not_checked',
        verbose_name="Status"
    )
    date_added = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Date Added"
    )
    date_updated = models.DateTimeField(
        auto_now=True,
        verbose_name="Date Updated"
    )

    def __str__(self):
        return f"{self.point_id} - {self.description[:30]}"

    class Meta:
        db_table = "manual_compliance_points"
        verbose_name = "Manual Compliance Point"
        verbose_name_plural = "Manual Compliance Points"
        ordering = ["point_id"]


class ComplianceEvidence(models.Model):
    """
    Represents evidence files for manual compliance points.
    """
    compliance_point = models.ForeignKey(
        ManualCompliancePoint,
        on_delete=models.CASCADE,
        related_name="evidence_files",
        verbose_name="Compliance Point"
    )
    file = models.FileField(
        upload_to='compliance_evidence/',
        verbose_name="Evidence File"
    )
    description = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name="Description"
    )
    date_added = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Date Added"
    )

    def __str__(self):
        return f"Evidence for {self.compliance_point.point_id}"

    class Meta:
        db_table = "compliance_evidence"
        verbose_name = "Compliance Evidence"
        verbose_name_plural = "Compliance Evidence"
        ordering = ["-date_added"]
