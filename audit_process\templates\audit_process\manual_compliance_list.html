{% extends "equipment/base.html" %}
{% load static %}
{% load audit_process_filters %}

{% block title %}Manual Compliance Points - {{ audit.item.name }}{% endblock %}

{% block extra_css %}
<style>
    .compliance-status {
        font-weight: bold;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
    }
    .status-conforme {
        background-color: #d1e7dd;
        color: #0f5132;
    }
    .status-non_conforme {
        background-color: #f8d7da;
        color: #842029;
    }
    .status-not_checked {
        background-color: #e2e3e5;
        color: #41464b;
    }
    .evidence-file {
        display: inline-block;
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
    }
    .evidence-file a {
        display: flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
        color: #212529;
        text-decoration: none;
    }
    .evidence-file a:hover {
        background-color: #e9ecef;
    }
    .evidence-file i {
        margin-right: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>Manual Compliance Points</h2>
                <div>
                    <a href="{% url 'ongoing_audit_detail' pk=audit.id %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Audit
                    </a>
                </div>
            </div>
            <p class="text-muted">
                Audit: <strong>{{ audit.item.name }}</strong> |
                Status: <strong>{{ audit.get_audit_status_display }}</strong> |
                Auditor: <strong>{{ audit.auditor.name }}</strong>
            </p>
        </div>
    </div>

    <!-- Compliance Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Total Points</h5>
                    <p class="card-text display-4">{{ total_points }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">Compliant</h5>
                    <p class="card-text display-4">{{ compliant_points }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <h5 class="card-title">Non-Compliant</h5>
                    <p class="card-text display-4">{{ non_compliant_points }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Compliance Rate</h5>
                    <p class="card-text display-4">{{ compliance_percentage|floatformat:1 }}%</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex gap-2">
                <a href="{% url 'add_compliance_point' audit_id=audit.id %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i> Add Compliance Point
                </a>
                <a href="{% url 'bulk_import_compliance_points' audit_id=audit.id %}" class="btn btn-success">
                    <i class="fas fa-file-import me-2"></i> Import from CSV
                </a>
                <a href="{% url 'audit_db_os_list' %}" class="btn btn-info">
                    <i class="fas fa-database me-2"></i> Import from Database
                </a>
            </div>
        </div>
    </div>

    <!-- Compliance Points Table -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Compliance Points</h5>
                </div>
                <div class="card-body">
                    {% if compliance_points %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Description</th>
                                    <th>Command</th>
                                    <th>Expected Result</th>
                                    <th>Actual Result</th>
                                    <th>Status</th>
                                    <th>Evidence</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for point in compliance_points %}
                                <tr>
                                    <td><strong>{{ point.point_id }}</strong></td>
                                    <td>{{ point.description }}</td>
                                    <td>
                                        {% if point.command %}
                                        <code>{{ point.command }}</code>
                                        {% else %}
                                        <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ point.expected_result|default:"N/A" }}</td>
                                    <td>{{ point.actual_result|default:"N/A" }}</td>
                                    <td>
                                        <span class="compliance-status status-{{ point.status }}">
                                            {% if point.status == 'conforme' %}
                                                Compliant
                                            {% elif point.status == 'non_conforme' %}
                                                Non-Compliant
                                            {% else %}
                                                Not Checked
                                            {% endif %}
                                        </span>
                                    </td>
                                    <td>
                                        {% for evidence in point.evidence_files.all %}
                                        <div class="evidence-file mb-2">
                                            <div class="card">
                                                <div class="card-body p-2">
                                                    {% if evidence.file.url|lower|endswith:".jpg" or evidence.file.url|lower|endswith:".jpeg" or evidence.file.url|lower|endswith:".png" or evidence.file.url|lower|endswith:".gif" %}
                                                    <img src="{{ evidence.file.url }}" class="img-fluid img-thumbnail mb-2" style="max-height: 100px;" alt="Evidence Screenshot">
                                                    {% else %}
                                                    <i class="fas fa-file fa-2x text-muted mb-2"></i>
                                                    {% endif %}
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <a href="{{ evidence.file.url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-external-link-alt me-1"></i> View
                                                        </a>
                                                        <form method="POST" action="{% url 'delete_compliance_evidence' evidence_id=evidence.id %}" class="d-inline">
                                                            {% csrf_token %}
                                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this evidence?')">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {% empty %}
                                        <span class="text-muted">No evidence</span>
                                        {% endfor %}
                                        <div>
                                            <div class="btn-group mt-1">
                                                <a href="{% url 'add_compliance_evidence' point_id=point.id %}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-plus"></i> Add Local
                                                </a>
                                                <a href="{% url 'audit_db_add_preuve' audit_id=audit.id point_id=point.id %}" class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-database"></i> Add to DB
                                                </a>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'edit_compliance_point' point_id=point.id %}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'delete_compliance_point' point_id=point.id %}" class="btn btn-sm btn-danger">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <p class="mb-0">No compliance points have been added yet. Click "Add Compliance Point" to add one manually or "Import from CSV" to import from a CSV file.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
