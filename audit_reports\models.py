from django.db import models
from django.utils import timezone
from django.contrib.auth.models import User
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.urls import reverse
from django.core.mail import send_mail
from django.conf import settings
from django_extensions.db.fields import AutoSlugField
import json

from equipment.models import Item, Category
from audit_process.models import OngoingAudit
from accounts.models import Profile, AuditorNotification
from client_portal.models import ClientProfile, ClientNotification


class AuditReport(models.Model):
    """
    Represents an audit report for an audited equipment.

    Attributes:
        slug (str): Unique slug based on the date.
        date (datetime): Date of report creation.
        customer_name (str): Name of the client.
        contact_number (str): Client's contact number.
        item (ForeignKey): The audited equipment.
        price_per_item (float): Fee per equipment.
        quantity (float): Number of equipment audited.
        shipping (float): Additional fees.
        total (float): Total before additional fees.
        grand_total (float): Total including additional fees.
    """

    slug = AutoSlugField(unique=True, populate_from='date')
    date = models.DateTimeField(
        auto_now=True,
        verbose_name='Report Date'
    )
    client_name = models.CharField(max_length=30, verbose_name='Client Name')
    contact_number = models.CharField(max_length=13, verbose_name='Contact Number')
    equipment = models.ForeignKey(Item, on_delete=models.CASCADE, verbose_name='Audited Equipment')
    audit_result = models.BooleanField(default=False, verbose_name='Audit Result (Pass/Fail)')
    description = models.TextField(blank=True, null=True, verbose_name='Audit Description')
    findings = models.TextField(blank=True, null=True, verbose_name='Audit Findings')
    fee = models.FloatField(verbose_name='Audit Fee', default=0.00, blank=True, null=True)
    additional_fees = models.FloatField(verbose_name='Additional Fees', default=0.00, blank=True, null=True)
    total_fee = models.FloatField(
        verbose_name='Total Fee', editable=False, default=0.00, blank=True, null=True
    )

    def save(self, *args, **kwargs):
        """
        Update total_fee before saving.
        """
        if self.fee is not None and self.additional_fees is not None:
            self.total_fee = round(self.fee + self.additional_fees, 2)
        return super().save(*args, **kwargs)

    def __str__(self):
        """
        Return the audit report's identifier.
        """
        return f"Audit Report {self.slug} - {self.client_name}"

    class Meta:
        verbose_name = "Audit Report"
        verbose_name_plural = "Audit Reports"
        ordering = ["-date"]


class ScriptGeneratedReport(models.Model):
    """
    Represents a report generated by an audit script.

    This model stores reports that are generated by external audit scripts
    and uploaded by auditors for clients to view.
    """

    REPORT_STATUS_CHOICES = [
        ('pending', 'Pending Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('archived', 'Archived')
    ]

    slug = AutoSlugField(unique=True, populate_from='title')
    title = models.CharField(max_length=100, verbose_name='Report Title')
    date_created = models.DateTimeField(default=timezone.now, verbose_name='Date Created')
    date_uploaded = models.DateTimeField(auto_now_add=True, verbose_name='Date Uploaded')

    # Link to the ongoing audit this report is for
    ongoing_audit = models.ForeignKey(
        OngoingAudit,
        on_delete=models.CASCADE,
        related_name='script_reports',
        verbose_name='Related Audit'
    )

    # Client and auditor information
    client = models.ForeignKey(
        ClientProfile,
        on_delete=models.CASCADE,
        related_name='script_reports',
        verbose_name='Client',
        null=True,
        blank=True
    )

    auditor = models.ForeignKey(
        Profile,
        on_delete=models.CASCADE,
        related_name='script_reports',
        verbose_name='Auditor',
        null=True,
        blank=True
    )

    # Equipment and category information
    equipment = models.ForeignKey(
        Item,
        on_delete=models.CASCADE,
        related_name='script_reports',
        verbose_name='Audited Equipment'
    )
    category = models.ForeignKey(
        Category,
        on_delete=models.CASCADE,
        related_name='script_reports',
        verbose_name='Equipment Category'
    )

    # The actual report file
    report_file = models.FileField(
        upload_to='script_reports/',
        verbose_name='Report File (Excel/CSV)'
    )

    # Report details
    description = models.TextField(blank=True, null=True, verbose_name='Report Description')
    findings = models.TextField(blank=True, null=True, verbose_name='Key Findings')
    recommendations = models.TextField(blank=True, null=True, verbose_name='Recommendations')

    # Compliance information
    compliance_score = models.FloatField(
        default=0.0,
        verbose_name='Compliance Score (%)'
    )
    is_compliant = models.BooleanField(
        default=False,
        verbose_name='Is Compliant'
    )

    # Status of the report
    status = models.CharField(
        max_length=20,
        choices=REPORT_STATUS_CHOICES,
        default='pending',
        verbose_name='Report Status'
    )

    # Script information
    script_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name='Script Name'
    )
    script_version = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name='Script Version'
    )

    def __str__(self):
        """Return a string representation of the report."""
        return f"{self.title} - {self.equipment.name} ({self.date_created.strftime('%Y-%m-%d')})"

    def get_absolute_url(self):
        """Return the URL to access a detail record for this report."""
        return reverse('script_report_detail', kwargs={'slug': self.slug})

    def send_notifications(self):
        """Send notifications to the client and auditor."""
        # Create notification for the client if available
        if self.client:
            ClientNotification.objects.create(
                client=self.client,
                notification_type='audit_report',
                title=f"New Audit Report: {self.title}",
                message=f"A new audit report has been uploaded for your {self.equipment.name}. "
                        f"Compliance score: {self.compliance_score}%."
            )

            # Send email notification to client
            try:
                client_email = self.client.user.email
                if client_email:
                    send_mail(
                        subject=f"New Audit Report: {self.title}",
                        message=f"Dear {self.client.user.get_full_name()},\n\n"
                                f"A new audit report has been uploaded for your {self.equipment.name}.\n"
                                f"Compliance score: {self.compliance_score}%.\n\n"
                                f"You can view the report by logging into your account.\n\n"
                                f"Best regards,\nThe Audit Team",
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        recipient_list=[client_email],
                        fail_silently=True
                    )
            except Exception as e:
                print(f"Error sending email to client: {str(e)}")

        # Create notification for other auditors
        auditors = Profile.objects.exclude(id=self.auditor.id)
        for auditor in auditors:
            AuditorNotification.objects.create(
                auditor=auditor,
                notification_type='audit_report',
                title=f"New Audit Report: {self.title}",
                message=f"Auditor {self.auditor.user.get_full_name()} has uploaded a new audit report "
                        f"for {self.equipment.name}.",
                related_id=self.id
            )

    def update_status_notification(self, old_status):
        """Send notification when report status changes."""
        if old_status != self.status and self.client:
            status_message = {
                'pending': 'is pending review',
                'approved': 'has been approved',
                'rejected': 'has been rejected',
                'archived': 'has been archived'
            }

            # Create notification for the client
            ClientNotification.objects.create(
                client=self.client,
                notification_type='audit_report',
                title=f"Audit Report Status Update: {self.title}",
                message=f"Your audit report for {self.equipment.name} {status_message.get(self.status, 'has been updated')}."
            )

            # Send email notification to client
            try:
                client_email = self.client.user.email
                if client_email:
                    send_mail(
                        subject=f"Audit Report Status Update: {self.title}",
                        message=f"Dear {self.client.user.get_full_name()},\n\n"
                                f"Your audit report for {self.equipment.name} {status_message.get(self.status, 'has been updated')}.\n\n"
                                f"You can view the report by logging into your account.\n\n"
                                f"Best regards,\nThe Audit Team",
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        recipient_list=[client_email],
                        fail_silently=True
                    )
            except Exception as e:
                print(f"Error sending email to client: {str(e)}")

    class Meta:
        verbose_name = "Script Generated Report"
        verbose_name_plural = "Script Generated Reports"
        ordering = ["-date_created"]


class ComplianceData(models.Model):
    """
    Stores compliance data extracted from CSV files uploaded by auditors.
    This model is used to track compliance points for equipment over time.
    """
    # Link to the script-generated report
    report = models.ForeignKey(
        ScriptGeneratedReport,
        on_delete=models.CASCADE,
        related_name='compliance_data',
        verbose_name='Related Report'
    )

    # Link to the equipment
    equipment = models.ForeignKey(
        Item,
        on_delete=models.CASCADE,
        related_name='compliance_data',
        verbose_name='Equipment'
    )

    # Date of the compliance check
    date_checked = models.DateTimeField(
        default=timezone.now,
        verbose_name='Date Checked'
    )

    # Compliance data
    total_checks = models.IntegerField(
        default=0,
        verbose_name='Total Checks'
    )

    passed_checks = models.IntegerField(
        default=0,
        verbose_name='Passed Checks'
    )

    failed_checks = models.IntegerField(
        default=0,
        verbose_name='Failed Checks'
    )

    # Compliance score (percentage)
    compliance_score = models.FloatField(
        default=0.0,
        verbose_name='Compliance Score (%)'
    )

    # Detailed compliance data (stored as JSON)
    detailed_data = models.JSONField(
        blank=True,
        null=True,
        verbose_name='Detailed Compliance Data'
    )

    # Compliance categories
    categories = models.JSONField(
        blank=True,
        null=True,
        verbose_name='Compliance Categories'
    )

    def __str__(self):
        """Return a string representation of the compliance data."""
        return f"Compliance Data for {self.equipment.name} - {self.date_checked.strftime('%Y-%m-%d')}"

    def calculate_compliance_score(self):
        """Calculate the compliance score based on passed and total checks."""
        if self.total_checks > 0:
            self.compliance_score = (self.passed_checks / self.total_checks) * 100
        else:
            self.compliance_score = 0
        return self.compliance_score

    def save(self, *args, **kwargs):
        """Override save to calculate compliance score before saving."""
        self.calculate_compliance_score()
        super().save(*args, **kwargs)

        # Update the equipment's compliance points
        self.equipment.compliance_points = self.compliance_score
        self.equipment.save(update_fields=['compliance_points'])

        # Update the report's compliance score
        self.report.compliance_score = self.compliance_score
        self.report.is_compliant = self.compliance_score >= 70  # Consider compliant if score is 70% or higher
        self.report.save(update_fields=['compliance_score', 'is_compliant'])

    class Meta:
        verbose_name = "Compliance Data"
        verbose_name_plural = "Compliance Data"
        ordering = ["-date_checked"]


# Signal handlers for ScriptGeneratedReport
@receiver(post_save, sender=ScriptGeneratedReport)
def handle_script_report_save(sender, instance, created, **kwargs):
    """
    Signal handler to send notifications when a script-generated report is created or updated.
    """
    if created:
        # Send notifications when a new report is created
        instance.send_notifications()
    else:
        # Check if status has changed
        try:
            old_instance = ScriptGeneratedReport.objects.get(pk=instance.pk)
            if old_instance.status != instance.status:
                instance.update_status_notification(old_instance.status)
        except ScriptGeneratedReport.DoesNotExist:
            pass
