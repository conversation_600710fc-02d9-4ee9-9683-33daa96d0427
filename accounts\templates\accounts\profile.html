{% extends 'equipment/base.html' %}
{% block title %}Staff Page{% endblock %}

{% block content %}
<div class="container">
        <div class="card">
            <div class="row p-3">
                <div class="col-md-8">
                    <table class="table table-borderless">
                        <thead>
                            <span class="h4">Profile Information</span class="h4">
                            <a class="btn btn-outline-success btn-sm float-end" href="{% url 'user-profile-update' %}">Edit profile</a>
                            <hr>
                        </thead>
                        <tbody>
                            <tr>
                                <th scope="row">username</th>
                                <td>{{ user.username }}</td>
                            </tr>
                            <tr>
                                <th scope="row">Staff role</th>
                                <td>
                                    <span class="badge badge-pill bg-soft-success text-success me-2">
                                    {% if user.profile.role == 'AD' %}
                                    Admin
                                    {% elif user.profile.role == 'CL' %}
                                    Client
                                    {% else %}
                                    Auditor
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">First name</th>
                                <td>{{ user.profile.first_name }}</td>
                            </tr>
                            <tr>
                                <th scope="row">Last name</th>
                                <td>{{ user.profile.last_name }}</td>
                            </tr>
                            <tr>
                                <th scope="row">Email</th>
                                <td>{{ user.email }}</td>
                            </tr>
                            <tr>
                                <th scope="row">Phone</th>
                                <td>{{ user.profile.telephone }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-md-4">
                    <img class="rounded-pill img-fluid" src="{{ user.profile.profile_picture.url }}" alt="profile-image">
                </div>
            </div>
        </div>
</div>

{% endblock %}