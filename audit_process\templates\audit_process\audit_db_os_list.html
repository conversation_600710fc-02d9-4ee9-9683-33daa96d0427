{% extends "equipment/base.html" %}
{% load static %}

{% block title %}OS Versions{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>OS Versions</h2>
                <div>
                    <a href="{% url 'ongoing_audit_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Audits
                    </a>
                </div>
            </div>
            <p class="text-muted">Select an OS version to view available control points.</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Available OS Versions</h5>
                </div>
                <div class="card-body">
                    {% if os_versions %}
                    <div class="row row-cols-1 row-cols-md-3 g-4">
                        {% for os in os_versions %}
                        <div class="col">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">{{ os.nomversion }}</h5>
                                    <p class="card-text">Version d'OS pour audit</p>
                                </div>
                                <div class="card-footer">
                                    <a href="{% url 'audit_db_points_list_os' os_id=os.idversion %}" class="btn btn-primary">
                                        <i class="fas fa-list me-2"></i> View Point Versions
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <p class="mb-0">No OS versions found in the database. Please contact the administrator to add OS versions.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
