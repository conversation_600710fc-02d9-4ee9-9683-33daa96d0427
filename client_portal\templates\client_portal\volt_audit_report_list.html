{% extends 'client_portal/volt_base.html' %}

{% block title %}My Audit Reports{% endblock %}
{% block page_title %}My Audit Reports{% endblock %}

{% load client_filters %}

{% block content %}
<div class="py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow">
                <div class="card-header d-flex flex-row align-items-center flex-0 border-bottom">
                    <div class="d-block">
                        <h2 class="h5 mb-0">Audit Reports</h2>
                    </div>
                </div>
                <div class="card-body">
                    {% if audit_reports %}
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap mb-0 rounded">
                            <thead class="thead-light">
                                <tr>
                                    <th class="border-0 rounded-start">ID</th>
                                    <th class="border-0">Date</th>
                                    <th class="border-0">Report Name</th>
                                    <th class="border-0">Type</th>
                                    <th class="border-0">Result</th>
                                    <th class="border-0 rounded-end">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for report in audit_reports %}
                                    {% if report|get_class_name == 'ScriptGeneratedReport' %}
                                    <!-- Script-generated report -->
                                    <tr>
                                        <td>{{ report.id }}</td>
                                        <td>{{ report.date_created|date:"M d, Y" }}</td>
                                        <td>{{ report.title }}</td>
                                        <td><span class="badge bg-info">Script</span></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="small me-2">{{ report.compliance_score }}%</span>
                                                <span class="badge {% if report.is_compliant %}bg-success{% else %}bg-danger{% endif %}">
                                                    {% if report.is_compliant %}Compliant{% else %}Non-Compliant{% endif %}
                                                </span>
                                            </div>
                                        </td>
                                        <td>
                                            <a href="{% url 'client_script_report_detail' report.slug %}" class="btn btn-sm btn-gray-800">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                    {% else %}
                                    <!-- Standard audit report -->
                                    <tr>
                                        <td>{{ report.id }}</td>
                                        <td>{{ report.date|date:"M d, Y" }}</td>
                                        <td>{{ report.report_name|default:report.equipment.name }}</td>
                                        <td><span class="badge bg-primary">Standard</span></td>
                                        <td>
                                            <span class="badge {% if report.audit_result %}bg-success{% else %}bg-danger{% endif %}">
                                                {% if report.audit_result %}Pass{% else %}Fail{% endif %}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{% url 'client_audit_report_detail' report.slug %}" class="btn btn-sm btn-gray-800">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                    {% endif %}
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                    <div class="card-footer px-3 border-0 d-flex align-items-center justify-content-center">
                        <nav aria-label="Page navigation">
                            <ul class="pagination mb-0">
                                {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
                                </li>
                                {% endif %}

                                {% for i in paginator.page_range %}
                                {% if page_obj.number == i %}
                                <li class="page-item active">
                                    <a class="page-link" href="#">{{ i }}</a>
                                </li>
                                {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ i }}">{{ i }}</a>
                                </li>
                                {% endif %}
                                {% endfor %}

                                {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Next</a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-4">
                        <div class="icon icon-shape icon-shape-primary rounded-circle mb-4">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <h3 class="fw-extrabold">No Audit Reports Yet</h3>
                        <p class="text-gray-500 mb-4">You don't have any audit reports yet. They will appear here once your audits are completed.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow">
                <div class="card-header border-bottom d-flex align-items-center justify-content-between">
                    <h2 class="fs-5 fw-bold mb-0">Understanding Audit Reports</h2>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 mb-4">
                            <h5 class="fw-bold mb-3">Types of Audit Reports</h5>
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-3">
                                                <div class="icon-shape icon-shape-primary rounded-circle me-3">
                                                    <i class="fas fa-clipboard-check"></i>
                                                </div>
                                                <h5 class="fw-bold mb-0">Standard Audit Reports</h5>
                                            </div>
                                            <p class="text-gray-500">These reports are created manually by auditors after inspecting your equipment. They provide a simple pass/fail result with detailed findings.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-3">
                                                <div class="icon-shape icon-shape-info rounded-circle me-3">
                                                    <i class="fas fa-file-code"></i>
                                                </div>
                                                <h5 class="fw-bold mb-0">Script-Generated Reports</h5>
                                            </div>
                                            <p class="text-gray-500">These reports are generated by automated audit scripts that analyze your equipment configuration. They provide a detailed compliance score and specific recommendations.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12">
                            <h5 class="fw-bold mb-3">Understanding Results</h5>
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-body text-center">
                                            <div class="icon-shape icon-shape-success rounded-circle mb-3">
                                                <i class="fas fa-check"></i>
                                            </div>
                                            <h5 class="fw-bold">Pass / Compliant</h5>
                                            <p class="text-gray-500">A passing result means your equipment has met all the required standards and specifications during the audit.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-body text-center">
                                            <div class="icon-shape icon-shape-danger rounded-circle mb-3">
                                                <i class="fas fa-times"></i>
                                            </div>
                                            <h5 class="fw-bold">Fail / Non-Compliant</h5>
                                            <p class="text-gray-500">A failing result indicates that your equipment did not meet one or more standards and may require adjustments or repairs.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
