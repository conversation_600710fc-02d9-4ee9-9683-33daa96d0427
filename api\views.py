from rest_framework import generics, permissions, status
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth.models import User
from django.http import JsonResponse

from accounts.models import Profile, Auditor, Client
from .serializers import (
    UserSerializer,
    ProfileSerializer,
    AuditorSerializer,
    ClientSerializer,
    CustomTokenObtainPairSerializer,
    RegisterSerializer
)


class CustomTokenObtainPairView(TokenObtainPairView):
    """
    Custom token view that uses our enhanced serializer
    """
    serializer_class = CustomTokenObtainPairSerializer


class RegisterView(generics.CreateAPIView):
    """
    API endpoint for user registration
    """
    queryset = User.objects.all()
    permission_classes = (AllowAny,)
    serializer_class = RegisterSerializer


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_profile(request):
    """
    Get the profile of the currently authenticated user
    """
    try:
        profile = Profile.objects.get(user=request.user)
        serializer = ProfileSerializer(profile)
        return Response(serializer.data)
    except Profile.DoesNotExist:
        return Response(
            {"detail": "Profile not found"},
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_data(request):
    """
    Get user data including role information
    """
    user = request.user
    data = {
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'first_name': user.first_name,
        'last_name': user.last_name,
    }

    # Add profile data if exists
    try:
        profile = Profile.objects.get(user=user)
        data['role'] = profile.role
        data['status'] = profile.status
        data['telephone'] = profile.telephone
    except Profile.DoesNotExist:
        data['role'] = None
        data['status'] = None
        data['telephone'] = None

    return Response(data)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout_view(request):
    """
    Blacklist the refresh token to logout
    """
    try:
        refresh_token = request.data.get('refresh')
        if refresh_token:
            token = RefreshToken(refresh_token)
            token.blacklist()
            return Response({"detail": "Successfully logged out."}, status=status.HTTP_200_OK)
        else:
            return Response({"detail": "Refresh token is required."}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class UserListView(generics.ListAPIView):
    """
    API endpoint to list all users
    """
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]


class ProfileListView(generics.ListAPIView):
    """
    API endpoint to list all profiles
    """
    queryset = Profile.objects.all()
    serializer_class = ProfileSerializer
    permission_classes = [IsAuthenticated]


class AuditorListView(generics.ListAPIView):
    """
    API endpoint to list all auditors
    """
    queryset = Auditor.objects.all()
    serializer_class = AuditorSerializer
    permission_classes = [IsAuthenticated]


class ClientListView(generics.ListAPIView):
    """
    API endpoint to list all clients
    """
    queryset = Client.objects.all()
    serializer_class = ClientSerializer
    permission_classes = [IsAuthenticated]
