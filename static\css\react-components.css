/* React Components Styling */

/* Dashboard Widgets */
.dashboard-widgets .card {
  transition: all 0.3s ease;
  border-radius: 0.5rem;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

.dashboard-widgets .card:hover {
  transform: translateY(-5px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dashboard-widgets .card-body {
  padding: 1.25rem;
}

.dashboard-widgets .h3 {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0;
}

.dashboard-widgets .h6 {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.dashboard-widgets .icon-shape {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 3rem;
  width: 3rem;
  border-radius: 50%;
}

/* Notification Center */
.notification-center {
  width: 100%;
  max-height: 400px;
  overflow-y: auto;
}

.notification-header {
  padding: 0.75rem 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.notification-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.notification-list {
  padding: 0.5rem 0;
}

.notification-list .list-group-item {
  border-left: none;
  border-right: none;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.notification-list .list-group-item:hover {
  background-color: #f8f9fa;
}

.notification-list .list-group-item:first-child {
  border-top: none;
}

.notification-list .list-group-item:last-child {
  border-bottom: none;
}

.notification-list .badge {
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
  margin-left: 0.5rem;
}

/* Loading Spinners */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #6c757d;
}

/* Error Messages */
.error {
  padding: 1rem;
  color: #dc3545;
  text-align: center;
  font-weight: 500;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .dashboard-widgets .display-4 {
    font-size: 2rem;
  }

  .dashboard-widgets .card-body {
    padding: 1rem;
  }
}
