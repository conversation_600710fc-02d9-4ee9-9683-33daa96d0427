{% extends "equipment/base.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% block title %}
    {% if is_auditor %}
        Update Audit Request (Auditor)
    {% else %}
        Update Audit Request
    {% endif %}
{% endblock title %}

{% block content %}
<div class="col container p-5">
    <div class="row">
        <div class="col-md-3 col-lg-3"></div>
        <div class="col-md-6 col-lg-6">
            <form method="POST" enctype="multipart/form-data">
                {% csrf_token %}
                <section class="section-bg" id="plans">
                    <fieldset class="form-group">
                        <header class="section-header">
                            {% if is_auditor %}
                                <h1 class="text-primary">Update Audit Request (Auditor)</h1>
                                <p class="text-muted">As an auditor, you can update any audit request.</p>
                            {% else %}
                                <h1 class="text-success">Update Audit Request</h1>
                                <p class="text-muted">Please update your audit request information below.</p>
                            {% endif %}
                            <hr>
                        </header>

                        {{ form.media }}
                        {{ form|crispy }}
                    </fieldset>
                </section>
                <div class="form-group mt-4 text-center">
                    <button class="btn btn-success" type="submit">Update Request</button>
                    <a href="{% url 'audit_request_list' %}" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
        <div class="col-md-3 col-lg-3"></div>
    </div>
</div>
{% endblock content %}