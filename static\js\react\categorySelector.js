import React, { useState, useEffect } from 'react';
import <PERSON>actD<PERSON> from 'react-dom';
import axios from 'axios';

// Set up CSRF token for all axios requests
function getCookie(name) {
  let cookieValue = null;
  if (document.cookie && document.cookie !== '') {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.substring(0, name.length + 1) === (name + '=')) {
        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
        break;
      }
    }
  }
  return cookieValue;
}

const csrftoken = getCookie('csrftoken');
axios.defaults.headers.common['X-CSRFToken'] = csrftoken;

const CategorySelector = () => {
  const [categories, setCategories] = useState([]);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Function to fetch categories
    const fetchCategories = async () => {
      try {
        // Fetch categories from our Django API endpoint
        const response = await axios.get('/api/audit-categories/');

        // Check if we got a valid response (array of categories)
        if (Array.isArray(response.data)) {
          setCategories(response.data);
        } else {
          // If we got HTML or other non-array response, it might be a login page
          console.error('Invalid response format:', response.data);
          setError('Authentication error or invalid response. Please refresh the page or log in again.');
        }
        setLoading(false);
      } catch (err) {
        console.error('Error fetching categories:', err);

        // Fallback to hardcoded categories for testing
        const fallbackCategories = [
          { id: 10, name: 'ergr', description: '' },
          { id: 11, name: 'rgergergerg', description: '' }
        ];
        console.log('Using fallback categories:', fallbackCategories);
        setCategories(fallbackCategories);
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const toggleCategory = (categoryId) => {
    setSelectedCategories(prevSelected => {
      if (prevSelected.includes(categoryId)) {
        return prevSelected.filter(id => id !== categoryId);
      } else {
        return [...prevSelected, categoryId];
      }
    });
  };

  // Update hidden input field with selected categories for form submission
  useEffect(() => {
    const hiddenInput = document.getElementById('selected-categories');
    if (hiddenInput) {
      hiddenInput.value = JSON.stringify(selectedCategories);
      console.log('Updated selected categories:', selectedCategories);
    } else {
      console.error('Hidden input field not found!');
    }
  }, [selectedCategories]);

  if (loading) return <div className="loading">Loading categories...</div>;
  if (error) return <div className="error">{error}</div>;

  return (
    <div className="category-selector">
      <div className="form-group">
        <select
          className="form-select"
          onChange={(e) => {
            const selectedValue = parseInt(e.target.value);
            setSelectedCategories(selectedValue ? [selectedValue] : []);
          }}
          value={selectedCategories.length > 0 ? selectedCategories[0] : ''}
        >
          <option value="">Select a category</option>
          {categories.length === 0 ? (
            <option disabled>No categories available</option>
          ) : (
            categories.map(category => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))
          )}
        </select>
      </div>
      <input type="hidden" id="selected-categories" name="selected_categories" value="[]" />
    </div>
  );
};

// Find all category selector mount points in the page
document.addEventListener('DOMContentLoaded', () => {
  const categorySelectorRoot = document.getElementById('category-selector');
  if (categorySelectorRoot) {
    ReactDOM.render(<CategorySelector />, categorySelectorRoot);
  }
});
