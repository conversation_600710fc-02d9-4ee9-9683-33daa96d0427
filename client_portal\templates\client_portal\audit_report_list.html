{% extends 'client_portal/base.html' %}

{% block title %}My Audit Reports{% endblock %}
{% block page_title %}My Audit Reports{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h2>My Audit Reports</h2>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Audit Reports</h5>
                </div>
                <div class="card-body">
                    {% if audit_reports %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Report Name</th>
                                    <th>Date</th>
                                    <th>Equipment</th>
                                    <th>Result</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for report in audit_reports %}
                                <tr>
                                    <td>{{ report.id }}</td>
                                    <td>{{ report.report_name }}</td>
                                    <td>{{ report.date|date:"M d, Y" }}</td>
                                    <td>{{ report.item.name }}</td>
                                    <td>
                                        {% if report.result %}
                                        <span class="badge bg-success">Pass</span>
                                        {% else %}
                                        <span class="badge bg-danger">Fail</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'client_audit_report_detail' report.slug %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if is_paginated %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </span>
                            </li>
                            {% endif %}
                            
                            {% for i in paginator.page_range %}
                            {% if page_obj.number == i %}
                            <li class="page-item active">
                                <span class="page-link">{{ i }}</span>
                            </li>
                            {% else %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ i }}">{{ i }}</a>
                            </li>
                            {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </span>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    
                    {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted">You don't have any audit reports yet.</p>
                        <p>Once your audit requests are processed, the reports will appear here.</p>
                        <a href="{% url 'client_create_audit_request' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i> Create an Audit Request
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
