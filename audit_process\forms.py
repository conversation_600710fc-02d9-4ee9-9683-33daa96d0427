from django import forms
from .models import OngoingAudit, AuditScript, ManualCompliancePoint, ComplianceEvidence


class BootstrapMixin(forms.ModelForm):
    """
    A mixin to add Bootstrap classes to form fields.
    """
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field in self.fields.values():
            field.widget.attrs.setdefault('class', 'form-control')


class OngoingAuditForm(BootstrapMixin, forms.ModelForm):
    """
    A form for creating and updating OngoingAudit instances.
    """

    class Meta:
        model = OngoingAudit
        fields = [
            'client_audit_request', 'item', 'description',
            'completion_date', 'audit_status', 'audit_type', 'script'
        ]
        widgets = {
            'completion_date': forms.DateInput(
                attrs={
                    'class': 'form-control',
                    'type': 'datetime-local'
                }
            ),
            'description': forms.Textarea(
                attrs={'rows': 3, 'cols': 40}
            ),
            'audit_status': forms.Select(
                attrs={'class': 'form-control'}
            ),
            'audit_type': forms.Select(
                attrs={'class': 'form-control'}
            ),
            'script': forms.Select(
                attrs={'class': 'form-control'}
            ),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Import here to avoid circular imports
        from audit_requests.models import AuditRequest as ClientAuditRequest

        # Set the queryset for client_audit_request field to show both pending and approved audit requests
        self.fields['client_audit_request'].queryset = ClientAuditRequest.objects.filter(status__in=['pending', 'approved'])
        self.fields['client_audit_request'].label = "Based on Audit Request"
        self.fields['client_audit_request'].help_text = "Select an audit request to base this audit on"

        # Change Item label to Equipment
        self.fields['item'].label = "Equipment"

        # Set label and help text for audit_type field
        self.fields['audit_type'].label = "Audit Type"
        self.fields['audit_type'].help_text = "Select whether this is an automated or manual audit"

        # Set label and help text for script field
        self.fields['script'].label = "Audit Script"
        self.fields['script'].help_text = "Select a script to use for this audit (required for automated audits)"
        self.fields['script'].required = False

        # Filter to only show active scripts
        self.fields['script'].queryset = AuditScript.objects.filter(is_active=True)


class AuditScriptForm(BootstrapMixin, forms.ModelForm):
    """
    Form for uploading and managing audit scripts.
    """
    class Meta:
        model = AuditScript
        fields = ['name', 'description', 'script_file', 'version', 'category', 'is_active']
        widgets = {
            'description': forms.Textarea(
                attrs={'rows': 3, 'cols': 40}
            ),
            'script_file': forms.FileInput(
                attrs={'class': 'form-control'}
            ),
            'is_active': forms.CheckboxInput(
                attrs={'class': 'form-check-input'}
            ),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Set help text for fields
        self.fields['name'].help_text = "Enter a descriptive name for the script"
        self.fields['description'].help_text = "Describe what the script does and how to use it"
        self.fields['script_file'].help_text = "Upload the script file (.ps1, .sh, etc.)"
        self.fields['version'].help_text = "Enter the version number (e.g., 1.0, 2.1)"
        self.fields['category'].help_text = "Select the equipment category this script is for"
        self.fields['is_active'].help_text = "Uncheck to disable this script without deleting it"

        # Make script_file required for new scripts but optional for updates
        if self.instance.pk:
            self.fields['script_file'].required = False


class ManualCompliancePointForm(BootstrapMixin, forms.ModelForm):
    """
    Form for creating and updating manual compliance points.
    """
    class Meta:
        model = ManualCompliancePoint
        fields = ['point_id', 'description', 'command', 'expected_result', 'actual_result', 'status']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
            'command': forms.Textarea(attrs={'rows': 2}),
            'expected_result': forms.Textarea(attrs={'rows': 2}),
            'actual_result': forms.Textarea(attrs={'rows': 2}),
            'status': forms.Select(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        self.audit = kwargs.pop('audit', None)
        super().__init__(*args, **kwargs)

        # Set help text for fields
        self.fields['point_id'].help_text = "Enter a unique ID for this compliance point (e.g., FW-001)"
        self.fields['description'].help_text = "Describe what this compliance point checks"
        self.fields['command'].help_text = "Enter the command used to check this compliance point"
        self.fields['expected_result'].help_text = "What result is expected for compliance"
        self.fields['actual_result'].help_text = "What was actually found during the audit"
        self.fields['status'].help_text = "Select the compliance status"

    def save(self, commit=True):
        instance = super().save(commit=False)
        if self.audit:
            instance.audit = self.audit
        if commit:
            instance.save()
        return instance


class ComplianceEvidenceForm(BootstrapMixin, forms.ModelForm):
    """
    Form for uploading evidence files for compliance points.
    """
    class Meta:
        model = ComplianceEvidence
        fields = ['file', 'description']
        widgets = {
            'file': forms.FileInput(attrs={'class': 'form-control'}),
            'description': forms.TextInput(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        self.compliance_point = kwargs.pop('compliance_point', None)
        super().__init__(*args, **kwargs)

        # Set help text for fields
        self.fields['file'].help_text = "Upload a file as evidence for this compliance point"
        self.fields['description'].help_text = "Briefly describe what this file shows"

    def save(self, commit=True):
        instance = super().save(commit=False)
        if self.compliance_point:
            instance.compliance_point = self.compliance_point
        if commit:
            instance.save()
        return instance
