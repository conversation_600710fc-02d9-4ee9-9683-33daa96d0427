# Generated by Django 4.2.9 on 2025-05-10 23:20

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("equipment", "0002_delete_delivery"),
        ("audit_process", "0005_remove_ongoingaudit_script_file_auditscript_and_more"),
        ("audit_requests", "0006_alter_auditrequest_slug"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="auditrequest",
            name="payment_details",
        ),
        migrations.AddField(
            model_name="auditrequest",
            name="equipment",
            field=models.ManyToManyField(
                help_text="Equipment to be audited",
                related_name="audit_requests",
                to="equipment.item",
                verbose_name="Equipment",
            ),
        ),
        migrations.AddField(
            model_name="auditrequest",
            name="script",
            field=models.ForeignKey(
                blank=True,
                help_text="Script to be used for the audit",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="client_audit_requests",
                to="audit_process.auditscript",
                verbose_name="Audit Script",
            ),
        ),
        migrations.AlterField(
            model_name="auditrequest",
            name="address",
            field=models.CharField(
                default="", help_text="Address of the client", max_length=255
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="auditrequest",
            name="description",
            field=models.TextField(
                default="", help_text="Description of the audit request"
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="auditrequest",
            name="email",
            field=models.EmailField(
                default="<EMAIL>",
                help_text="Email address of the client",
                max_length=254,
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="auditrequest",
            name="phone_number",
            field=models.CharField(
                default="0000000000",
                help_text="Phone number of the client",
                max_length=30,
            ),
            preserve_default=False,
        ),
    ]
