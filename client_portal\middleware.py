from django.shortcuts import redirect
from django.urls import resolve, reverse
from django.contrib import messages

class ClientAccessMiddleware:
    """
    Middleware to restrict access to client-only pages for non-client users
    and restrict access to staff-only pages for client users.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
    def __call__(self, request):
        # Skip middleware for unauthenticated users
        if not request.user.is_authenticated:
            return self.get_response(request)
        
        # Get current URL name
        current_url = resolve(request.path_info).url_name
        
        # Client-only URLs
        client_urls = [
            'client_dashboard', 
            'client_profile', 
            'client_audit_request_list',
            'client_audit_request_detail',
            'client_create_audit_request',
            'client_audit_report_list',
            'client_audit_report_detail',
            'client_notifications',
            'client_mark_notification_read'
        ]
        
        # Staff-only URLs (dashboard and other staff pages)
        staff_urls = [
            'dashboard',
            'profile_list',
            'user-profile',
            'user-profile-update',
            'audit_request_list',
            'ongoing_audit_list',
            'audit-report-list'
        ]
        
        try:
            # Check if user is a client
            from client_portal.models import ClientProfile
            is_client = ClientProfile.objects.filter(user=request.user).exists()
            
            # If user is a client trying to access staff-only pages
            if is_client and current_url in staff_urls:
                messages.warning(request, 'You do not have access to that page. Redirected to client dashboard.')
                return redirect('client_dashboard')
                
            # If user is staff trying to access client-only pages
            if not is_client and current_url in client_urls:
                messages.warning(request, 'You do not have access to that page. Redirected to staff dashboard.')
                return redirect('dashboard')
                
        except ImportError:
            # If client_portal is not available, continue
            pass
            
        return self.get_response(request)
