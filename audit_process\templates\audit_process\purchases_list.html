{% extends "equipment/base.html" %}{% load static %}{% load render_table from django_tables2 %}{% load querystring from django_tables2 %}{% block title %}Ongoing Audits{%endblock title%}

{% block content %}
<!-- Header Section -->
<div class="container my-4">
    <div class="card shadow-sm rounded p-3">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h4 class="display-6 mb-0 text-success">Ongoing Audits</h4>
            </div>
            <div class="col-md-6 d-flex justify-content-end gap-2">
                <a class="btn btn-success btn-sm rounded-pill shadow-sm" href="{% url 'ongoing_audit_create' %}">
                    <i class="fa-solid fa-plus"></i> Add Ongoing Audit
                </a>
                <a class="btn btn-success btn-sm rounded-pill shadow-sm" href="{% url 'ongoing_audits_export' %}">
                    <i class="fa-solid fa-download"></i> Export to Excel
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <style>
      .table th, .table td {
          text-align: center;
      }
    </style>
    <table class="table table-sm table-striped table-bordered">
        <thead class="thead-light">
            <tr>
                <th scope="col">ID <i class="fa-solid fa-sort"></i></th>
                <th scope="col">Equipment <i class="fa-solid fa-sort"></i></th>
                <th scope="col">Audit Status <i class="fa-solid fa-sort"></i></th>
                <th scope="col">Completion Date <i class="fa-solid fa-sort"></i></th>
                <th scope="col">Auditor <i class="fa-solid fa-sort"></i></th>
                <th scope="col">Action</th>
            </tr>
        </thead>
        <tbody>
            {% for audit in ongoing_audits %}
            <tr>
                <th scope="row">{{ audit.id }}</th>
                <td>{{ audit.item.name }}</td>
                <td>
                    {% if audit.audit_status == 'P' %}
                        <span class="badge badge-pill bg-soft-warning text-warning me-2">
                            In Progress
                        </span>
                    {% else %}
                        <span class="badge badge-pill bg-soft-success text-success me-2">
                            Completed
                        </span>
                    {% endif %}
                </td>
                <td>{{ audit.completion_date }}</td>
                <td>{{ audit.auditor.name }}</td>
                <td>
                    <a class="text-info" href="{% url 'ongoing_audit_update' audit.id %}">
                        <i class="fa-solid fa-pen"></i>
                    </a>
                    <a class="text-danger float-end" href="{% url 'ongoing_audit_delete' audit.id %}">
                        <i class="fa-solid fa-trash"></i>
                    </a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    <div class="mt-4">
        {% if is_paginated %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </span>
                </li>
                {% endif %}
                {% for i in paginator.page_range %}
                {% if page_obj.number == i %}
                <li class="page-item active" aria-current="page">
                    <span class="page-link">{{ i }} <span class="visually-hidden">(current)</span></span>
                </li>
                {% else %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ i }}">{{ i }}</a>
                </li>
                {% endif %}
                {% endfor %}
                {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </span>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>
{% endblock %}
