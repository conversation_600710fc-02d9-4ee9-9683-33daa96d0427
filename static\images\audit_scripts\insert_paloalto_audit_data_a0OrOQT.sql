
CREATE TABLE IF NOT EXISTS paloalto (
    id TEXT PRIMARY KEY,
    description TEXT,
    checktext TEXT,
    severity TEXT,
    remediation TEXT
);


INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228664', $$Inaccurate time stamps make it more difficult to correlate events and can lead to an inaccurate analysis. Determining the correct time a particular event occurred on a system is critical when conducting forensic analysis and investigating system events. Synchronizing internal information system clocks provides uniformity of time stamps for information systems with multiple system clocks and systems connected over a network. 

The Palo Alto Networks security platform can be configured to use specified Network Time Protocol (NTP) servers. NTP is used to synchronize the system clock of a computer to reference time source. Sources outside of the configured acceptable allowance (drift) may be inaccurate. When properly configured, NTP will synchronize all participating computers to within a few milliseconds of the reference time source.$$, $$Go to Device >> Setup >> Services.
In the "Services" window, the names or IP addresses of the Primary NTP Server and Secondary NTP Server must be present.
If the "Primary NTP Server" and "Secondary NTP Server" fields are blank, this is a finding.$$, 'low', $$Go to Device >> Setup >> Services.
Select the "Edit" icon (the gear symbol in the upper-right corner of the pane).
In the "Services" window, in the "Primary NTP Server Address" field and the "Secondary NTP Server Address" field, enter the IP address or hostname of the NTP servers.

In the "Authentication Type" field, select one of the following:
None (default); this option disables NTP authentication.
Symmetric Key; this option uses symmetric key exchange, which are shared secrets. Enter the key ID, algorithm, authentication key, and confirm the authentication key.
Autokey; this option uses auto key, or public key cryptography.
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228639', $$By limiting the number of failed logon attempts, the risk of unauthorized system access via user password guessing, otherwise known as brute-forcing, is reduced.$$, $$Go to Device >> Administrators
If there is no authentication profile configured for each account (aside from the emergency administration account), this is a finding.

Note which authentication profile is used for each account.

Go to Device >> Authentication Profile
Check the authentication profile used for each account (noted in the previous step)

If the "Failed Attempts (#)" field is not set to "3", this is a finding.$$, 'medium', $$This should not be configured in Device >> Setup >> Management >> Authentication Settings; instead, an authentication profile should be configured with lockout settings of three failed attempts and a lockout time of zero minutes.
Go to Device >> Authentication Profile
Select the configured authentication profile or select "Add" (in the bottom-left corner of the pane) to create a new one.
In the "Authentication Profile" field, enter the name of the authentication profile that will be used to control each person''s authentication process.
The "Lockout Time (min)" field is the lockout duration; this must be set to "0".  This will keep the lockout in effect until it is manually unlocked.
In the "Failed Attempts" field, enter "3".
Select "OK".

Apply the authentication profile to the Administrator accounts.
Go to Device >> Administrators
Select each configured account or select "Add" (in the bottom-left corner of the pane) to create a new one.
In the "Authentication Profile" field, enter the configured authentication profile.
Select "OK".

This authentication profile should not be applied to the emergency administration account since it has special requirements.
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228640', $$Display of the DoD-approved use notification before granting access to the network device ensures privacy and security notification verbiage used is consistent with applicable federal laws, Executive Orders, directives, policies, regulations, standards, and guidance.

System use notifications are required only for access via logon interfaces with human users.$$, $$View the logon screen of the Palo Alto Networks security platform.  A white text box at the bottom of the screen will contain the configured text.
If it is blank (there is no white text box) or the wording is not one of the approved banners, this is a finding.

This is the approved verbiage for applications that can accommodate banners of 1300 characters:
"You are accessing a U.S. Government (USG) Information System (IS) that is provided for USG-authorized use only.
By using this IS (which includes any device attached to this IS), you consent to the following conditions:
-The USG routinely intercepts and monitors communications on this IS for purposes including, but not limited to, penetration testing, COMSEC monitoring, network operations and defense, personnel misconduct (PM), law enforcement (LE), and counterintelligence (CI) investigations.
-At any time, the USG may inspect and seize data stored on this IS.
-Communications using, or data stored on, this IS are not private, are subject to routine monitoring, interception, and search, and may be disclosed or used for any USG-authorized purpose.
-This IS includes security measures (e.g., authentication and access controls) to protect USG interests--not for your personal benefit or privacy.
-Notwithstanding the above, using this IS does not constitute consent to PM, LE or CI investigative searching or monitoring of the content of privileged communications, or work product, related to personal representation or services by attorneys, psychotherapists, or clergy, and their assistants. Such communications and work product are private and confidential. See User Agreement for details."
Use the following verbiage for operating systems that have severe limitations on the number of characters that can be displayed in the banner:
"I''ve read & consent to terms in IS user agreem''t."$$, 'low', $$Go to Device >> Setup >> Management >> General Settings ("Edit" icon) >> Login Banner
Type in the required text
Select "OK".
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228642', $$Without generating audit records that are specific to the security and mission needs of the organization, it would be difficult to establish, correlate, and investigate the events relating to an incident or identify those responsible for one.  

By default, the Configuration Log contains the administrator username, client (Web or CLI), and date and time for any changes to configurations and for configuration commit actions.  The System Log also shows both successful and unsuccessful attempts for configuration commit actions.

The System Log and Configuration Log can be configured to send log messages by severity level to specific destinations; the Panorama management console, an SNMP console, an e-mail server, or a syslog server.  Since both the System Log and Configuration Log contain information concerning the use of privileges, both must be configured to send messages to a syslog server at a minimum.$$, $$Go to Device >> Log Settings >> System
If any severity level does not have a Syslog Profile, this is a finding.$$, 'medium', $$Create a syslog server profile. 
Go to Device >> Server Profiles >> Syslog
Select "Add" 
In the "Syslog Server Profile", enter the name of the profile; select "Add".
In the "Servers" tab, enter the required information.
Name: Name of the syslog server
Server: Server IP address where the logs will be forwarded to
Port: Default port 514
Facility: Select from the drop down list
Select "OK".

Go to Device >> Log Settings >> System
For each severity level, select which destinations should receive the log messages.
Note: The "Syslog Profile" field must be completed.

Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228643', $$In order to compile an accurate risk assessment and provide forensic analysis, it is essential for security personnel to know the source of the event.  The source may be a component, module, or process within the device or an external session, administrator, or device.  Associating information about where the source of the event occurred provides a means of investigating an attack; recognizing resource utilization or capacity thresholds; or identifying an improperly configured device.

The device must have a unique hostname that can be used to identify the device; fully qualified domain name (FQDN), hostname, or management IP address is used in audit logs to identify the source of a log message.$$, $$Go to Device >> Setup >> Management
In the "General Settings" window, if the "hostname" field does not contain a unique identifier, this is a finding.

Go to Device >> Setup >> Management
In the "Logging and Reporting Settings" pane, if the "Send Hostname in Syslog" does not show either "FQDN", "hostname", "ipv4-address", or "ipv6-address", this is a finding.$$, 'low', $$Set a unique hostname.
Go to Device >> Setup >> Management
in the "General Settings" window, select the "Edit" icon (the gear symbol in the upper-right corner of the pane).
In the "General Settings" window, in the "hostname" field; enter a unique hostname. 
Select "OK".

Configure the device to send the FQDN, hostname, ipv4-address, or ipv6-address with log messages.
Device >> Setup >> Management
Click the "Edit" icon in the "Logging and Reporting Settings" section.
Select the "Log Export and Reporting" tab.
Select one of the following options from the "Send Hostname in the Syslog" drop-down list:
FQDN — (the default) Concatenates the hostname and domain name defined on the sending device.
hostname — Uses the hostname defined on the sending device.
ipv4-address —Uses the IPv4 address of the interface used to send logs on the device. By default, this is the management interface of the device.
ipv6-address —Uses the IPv6 address of the interface used to send logs on the device. By default, this is the management interface of the device. 
Note that the last two selections must be consistent with the IP address used by the management interface.
Select "OK".
Commit changes by selecting "Commit" in the upper-right corner of the screen.  Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228645', $$In order to prevent unauthorized connection of devices, unauthorized transfer of information, or unauthorized tunneling (i.e., embedding of data types within data types), organizations must disable unused or unnecessary physical and logical ports/protocols on information systems.

Network devices are capable of providing a wide variety of functions and services. Some of the functions and services provided by default may not be necessary to support essential organizational operations. Additionally, it is sometimes convenient to provide multiple services from a single component (e.g., email and web services); however, doing so increases risk over limiting the services provided by any one component.

To support the requirements and principles of least functionality, the network device must support the organizational requirements providing only essential capabilities and limiting the use of ports, protocols, and/or services to only those required, authorized, and approved to conduct official business or to address authorized quality of life issues.

The Palo Alto Networks security platform uses a hardened operating system in which unnecessary services are not present.  The device has a DNS, NTP, update, and e-mail client installed.  Note that these are client applications and not servers; additionally, each has a valid purpose.  However, local policy may dictate that the update service, e-mail client, and statistics (reporting) service capabilities not be used. DNS can be either "Server" or "Proxy"; both are allowed unless local policy declares otherwise. NTP and SNMP are necessary functions.$$, $$Go to Device >> Setup >> Services
In the "Services" window, view which services are configured.
Note: DNS can be either "Server" or "Proxy"; both are allowed unless local policy declares otherwise.
Note: The Palo Alto Networks security platform cannot be a DNS server, only a client or proxy.

NTP is a necessary service.
Note: The Palo Alto Networks security platform cannot be an NTP server, only a client.

Go to Device >> Setup >> Management
In the "Management Interface Settings" window, view the enabled services.
Note: Which management services are enabled.  HTTPS, SSH, ping, and SNMP, are normally allowed.
  
If User-ID, User-ID Syslog Listener-SSL, User-ID Syslog Listener-UDP, or HTTP OCSP is present, verify with the ISSO that this has been authorized.
Go to Device >> Setup >> Operations tab>> Miscellaneous
Select SNMP Setup.
In the "SNMP Setup" window, check if SNMP V3 is selected.
If unauthorized services are configured, this is a finding.$$, 'medium', $$Go to Device >> Setup >> Services
In the "Services" window, select the "Edit" icon (the gear symbol in the upper-right corner of the pane).
Note: DNS can be either "Server" or "Proxy"; both are allowed unless local policy declares otherwise.
Note: The Palo Alto Networks security platform cannot be a DNS server, only a client or proxy.

NTP is a necessary service.
Note: The Palo Alto Networks security platform cannot be an NTP server, only a client.

Go to Device >> Setup >> Management
In the "Management Interface Settings" window, select the "Edit" icon (the gear symbol in the upper-right corner of the pane).
In the "Management Interface Settings" window, select HTTP OCSP, HTTPS, SSH,  SNMP, User-ID, User-ID Syslog Listener-SSL, User-ID Syslog Listener-UDP if these protocols will be used.  
Select "OK".
Note: SNMP Versions 1 and 2 are not considered secure; use SNMP Version 3.

Device >> Setup >> Operations tab>> Miscellaneous
Select SNMP Setup.
In the "SNMP Setup" window, select V3. 
Select "OK".
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228646', $$To assure accountability and prevent unauthenticated access, organizational administrators must be uniquely identified and authenticated for all network management accesses to prevent potential misuse and compromise of the system.$$, $$Go to Device >> Administrators
View the list of configured Administrators.
If there are any accounts other than the emergency administration account than does not uniquely identify an individual, this is a finding.

If there is not an authentication profile for each account (with the exception of the emergency administration account), this is a finding.$$, 'medium', $$Create a separate administrative account for each person who needs access to the administrative or reporting functions of the firewall.
Go to Device >> Administrators
Select "Add" (in the lower-left corner of the pane).
Complete the required information;
In the "Name" field, enter the name of the Administrator.
Note: That accounts must identify a single person; the only exception allowed is the emergency administration account.

In the "Authentication Profile" field, enter the name of the authentication profile that will be used to control that person''s authentication process.
For the Role, select either "Dynamic" or "Role Based".
If selecting "Dynamic", then select the role assigned for this person; Administrators can be assigned one of these built-in roles: Superuser, Superuser (read-only), Device administrator, Device administrator (read-only), Virtual system administrator, and Virtual system administrator (read-only).
If "Role Based" is selected, then select one of the three pre-configured profiles (auditadmin, cryptoadmin, or securityadmin) or a custom profile.
Select "OK".
Commit changes by selecting "Commit" in the upper-right corner of the screen.  Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228647', $$A replay attack may enable an unauthorized user to gain access to the application. Authentication sessions between the authenticator and the application validating the user credentials must not be vulnerable to a replay attack.  An authentication process resists replay attacks if it is impractical to achieve a successful authentication by recording and replaying a previous authentication message.

Techniques used to address this include protocols using nonces (e.g., numbers generated for a specific one-time use) or challenges (e.g., TLS, WS_Security). Additional techniques include time-synchronous or challenge-response one-time authenticators.  Of the three authentication protocols on the Palo Alto Networks security platform, only Kerberos is inherently replay-resistant.  If LDAP is selected, TLS must also be used.  If RADIUS is used, the device must be operating in FIPS mode.$$, $$Ask the Administrator which form of centralized authentication server is being used. 
Navigate to the appropriate window to view the configured server(s). 
For RADIUS, go to Device >> Server Profiles >> RADIUS
For LDAP, go to Device >> Server Profiles >> LDAP
For Kerberos, go to Device >> Server Profiles >> Kerberos 

If Kerberos is used, this is a not finding.

If LDAP is used, view the LDAP Server Profile; if the SSL checkbox is not checked, this is a finding.

If RADIUS is used, use the command line interface to determine if the device is operating in FIPS mode. Enter the CLI command "show fips-mode" or the command show fips-cc (for more recent releases).

If FIPS mode is set to "off", this is a finding.$$, 'medium', $$To configure the Palo Alto Networks security platform to use an LDAP server with SSL/TLS.
Go to Device >> Server-Profiles >> LDAP
Select "Add" (lower left of window).
Populate the required fields.
Enter the name of the profile in the "Name" field.

In the server box:
Enter the name of the server in the "Name" field.
Enter the IP Address of the server. 
Enter the Port number the firewall should use to connect to the LDAP server (default=389 for LDAP; 636 for LDAP over SSL). 
Enter the LDAP Domain name to prepend to all objects learned from the server. The value entered here depends on the specific deployment. If using Active Directory, enter the NetBIOS domain name, not a FQDN (for example, enter acme, not acme.com). Note that if collecting data from multiple domains, it is necessary to create separate server profiles. If using a global catalog server, leave this field blank.
Select the Type of LDAP server connecting to. The correct LDAP attributes in the group mapping settings will automatically be populated based on the selection.
In the Base field, select the DN that corresponds to the point in the LDAP tree where the firewall is to begin its search for user and group information.
Select (check) the SSL checkbox.
Select "OK".
Commit changes by selecting "Commit" in the upper-right corner of the screen.  Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228648', $$Password complexity, or strength, is a measure of the effectiveness of a password in resisting attempts at guessing and brute-force attacks. Password length is one factor of several that helps to determine strength and how long it takes to crack a password.

The shorter the password, the lower the number of possible combinations that needs to be tested before the password is compromised. Use of more characters in a password helps to exponentially increase the time and/or resources required to compromise the password.$$, $$Go to Device >> Setup >> Management.
View the "Minimum Password Complexity" window.
If the "Minimum Length" field is not "15", this is a finding.$$, 'medium', $$Go to Device >> Setup >> Management.
In the "Minimum Password Complexity" window, select the "Edit" icon (the gear symbol in the upper-right corner of the pane).
In the "Minimum Length" field, enter "15".
Check the "Enabled" box, then select "OK".
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228650', $$Use of a complex passwords helps to increase the time and resources required to compromise the password. Password complexity, or strength, is a measure of the effectiveness of a password in resisting attempts at guessing and brute-force attacks.

Password complexity is one factor of several that determines how long it takes to crack a password. The more complex the password is, the greater the number of possible combinations that needs to be tested before the password is compromised.$$, $$Go to Device >> Setup >> Management.
View the "Minimum Password Complexity" window.
If the "Minimum Uppercase Letters" field is not "1", this is a finding.$$, 'medium', $$Go to Device >> Setup >> Management.
In the "Minimum Password Complexity" window, select the "Edit" icon (the gear symbol in the upper-right corner of the pane).
In the "Minimum Uppercase Letters" field, enter "1".
Check the "Enabled" box, then select "OK".
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228651', $$Use of a complex password helps to increase the time and resources required to compromise the password. Password complexity, or strength, is a measure of the effectiveness of a password in resisting attempts at guessing and brute-force attacks.

Password complexity is one factor of several that determines how long it takes to crack a password. The more complex the password, the greater the number of possible combinations that needs to be tested before the password is compromised.$$, $$Go to Device >> Setup >> Management.
View the "Minimum Password Complexity" window.
If the "Minimum Lowercase Letters" field is not "1", this is a finding.$$, 'medium', $$Go to Device >> Setup >> Management.
In the "Minimum Password Complexity" window, select the "Edit" icon (the gear symbol in the upper-right corner of the pane).
In the "Minimum Lowercase Letters" field, enter "1".
Check the "Enabled" box, then select "OK".
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228652', $$Use of a complex password helps to increase the time and resources required to compromise the password. Password complexity, or strength, is a measure of the effectiveness of a password in resisting attempts at guessing and brute-force attacks.

Password complexity is one factor of several that determines how long it takes to crack a password. The more complex the password, the greater the number of possible combinations that needs to be tested before the password is compromised.$$, $$Go to Device >> Setup >> Management.
View the "Minimum Password Complexity" window.
If the "Minimum Numeric Letters" field is not "1", this is a finding.$$, 'medium', $$Go to Device >> Setup >> Management.
In the "Minimum Password Complexity" window, select the "Edit" icon (the gear symbol in the upper-right corner of the pane).
In the "Minimum Numeric Letters" field, enter "1".
Check the "Enabled" box, then select "OK".
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228653', $$Use of a complex password helps to increase the time and resources required to compromise the password. Password complexity, or strength, is a measure of the effectiveness of a password in resisting attempts at guessing and brute-force attacks.

Password complexity is one factor of several that determines how long it takes to crack a password. The more complex the password, the greater the number of possible combinations that needs to be tested before the password is compromised.$$, $$Go to Device >> Setup >> Management.
View the "Minimum Password Complexity" window.
If the "Minimum Special Letters" field is not "1", this is a finding.$$, 'medium', $$Go to Device >> Setup >> Management.
In the "Minimum Password Complexity" window, select the "Edit" icon (the gear symbol in the upper-right corner of the pane).
In the "Minimum Special Letters" field, enter "1".
Check the "Enabled box", then select "OK".
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228654', $$If the application allows the user to consecutively reuse extensive portions of passwords, this increases the chances of password compromise by increasing the window of opportunity for attempts at guessing and brute-force attacks.

The number of changed characters refers to the number of changes required with respect to the total number of positions in the current password. In other words, characters may be the same within the two passwords; however, the positions of the like characters must be different.$$, $$Go to Device >> Setup >> Management.
View the "Minimum Password Complexity" window.
If the "New Password Differs by Characters" field is not "8", this is a finding.$$, 'medium', $$Go to Device >> Setup >> Management.
In the "Minimum Password Complexity" window, select the "Edit" icon (the gear symbol in the upper-right corner of the pane).
In the "New Password Differs by Characters" field, enter "8".
Check the "Enabled box", then select "OK".
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228655', $$Passwords need to be protected at all times, and encryption is the standard method for protecting passwords. If passwords are not encrypted, they can be plainly read (i.e., clear text) and easily compromised.

Network devices can accomplish this by making direct function calls to encryption modules or by leveraging operating system encryption capabilities.$$, $$Go to Device >> Setup >> Management
View the "Management Interface Settings" pane.
If either Telnet or HTTP is listed in the "Services" field, this is a finding.$$, 'medium', $$Go to Device >> Setup >> Management
In the "Management Interface Settings" window, select the "Edit" icon (the gear symbol in the upper-right corner of the pane).  In the "Management Interface Settings" window, make sure that HTTP and Telnet are not checked (enabled). 
If they are not checked, select either "OK" or "Cancel".
If either one is checked, select the check box to disable it, then select "OK".
If any changes were made, commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228658', $$Terminating an idle session within a short time period reduces the window of opportunity for unauthorized personnel to take control of a management session enabled on the console or console port that has been left unattended. In addition, quickly terminating an idle session will also free up resources committed by the managed network element.

Terminating network connections associated with communications sessions includes, for example, de-allocating associated TCP/IP address/port pairs at the operating system level, or de-allocating networking assignments at the application level if multiple application sessions are using a single, operating system-level network connection. This does not mean that the device terminates all sessions or network access; it only ends the inactive session and releases the resources associated with that session.

Device management sessions are normally ended by the Administrator when he or she has completed the management activity.  The session termination takes place from the web client by selecting "Logout" (located at the bottom-left of the GUI window) or using the command line commands "exit" or "quit" at Operational mode.$$, $$Go to Device >> Setup >> Management.
View the "Authentication Settings" pane.
If the "Idle Timeout (min)" field is not "10" or less, ask the Administrator to produce documentation signed by the Authorizing Official that the configured value exists to support mission requirements.
If this documentation is not made available, this is a finding.$$, 'high', $$Go to Device >> Setup >> Management.
In the "Authentication Settings" pane, select the "Edit" icon (the gear symbol in the upper-right corner of the pane).
In the "Idle Timeout (min)" field, enter "10", then select "OK".
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228665', $$The loss of connectivity to a particular authoritative time source will result in the loss of time synchronization (free-run mode) and increasingly inaccurate time stamps on audit events and other functions. Multiple time sources provide redundancy by including a secondary source. Time synchronization is usually a hierarchy; clients synchronize time to a local source while that source synchronizes its time to a more accurate source. 

DOD-approved solutions consist of a combination of a primary and secondary time source using a combination or multiple instances of the following: a time server designated for the appropriate DOD network (NIPRNet/SIPRNet); United States Naval Observatory (USNO) time servers; and/or the Global Positioning System (GPS). The secondary time source must be located in a different geographic region from the primary time source.$$, $$Go to Device >> Setup >> Services.
If there is only one NTP Server configured, this is a finding.

Ask the firewall administrator where the Primary NTP Server and Secondary NTP Server are located; if they are not in different geographic regions, this is a finding.$$, 'medium', $$Go to Device >> Setup >> Services.
Select the "Edit" icon (the gear symbol in the upper-right corner of the pane).
In the "Services" window, in the "Primary NTP Server Address" field and the "Secondary NTP Server Address" field, enter the IP address or hostname of the NTP servers.

In the "Authentication Type" field, select one of the following:
None (default); this option disables NTP authentication.
Symmetric Key; this option uses symmetric key exchange, which are shared secrets. Enter the key ID, algorithm, authentication key, and confirm the authentication key.
Autokey; this option uses auto key, or public key cryptography.
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228659', $$The Palo Alto Networks security platform has both pre-configured and configurable Administrator roles. Administrator roles determine the functions that the administrator is permitted to perform after logging in. Roles can be assigned directly to an administrator account, or define role profiles, which specify detailed privileges, and assign those to administrator accounts.

There are three preconfigured roles designed to comply with Common Criteria requirements - Security Administrator, Audit Administrator, and Cryptographic Administrator. Of the three, only the Audit Administrator can delete audit records.  The Palo Alto Networks security platform can use both pre-configured and configurable Administrator roles.$$, $$For the roles of Security Administrator, Cryptographic Administrator, or Audit Administators, verify the same individual does not have more than one of these roles.

If the Palo Alto Networks security platform has any accounts where the same person is in the role of Security Administrator, Cryptographic Administrator, or Audit Administrator, this is a finding.$$, 'medium', $$Do not assign or configure more than one account to the same Administrator. Also, neither the Security Administrator nor the Cryptographic Administrator can be have the role of Audit Administrator.

Note that the system allows each account to have only one role assigned. However, individuals, either accidentally or intentionally, may have more than one account.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228660', $$By limiting the number of failed logon attempts, the risk of unauthorized system access via user password guessing, otherwise known as brute-forcing, is reduced. Limits are imposed by locking the account.

This should not be configured in Device >> Setup >> Management >> Authentication Settings; instead, an authentication profile should be configured with lockout settings of three failed attempts and a lockout time of zero minutes.  The Lockout Time is the number of minutes that a user is locked out if the number of failed attempts is reached (0-60 minutes, default 0). 0 means that the lockout is in effect until it is manually unlocked.$$, $$Go to Device >> Administrators.
If there is no authentication profile configured for each account (aside from the emergency administration account), this is a finding.

Note which authentication profile is used for each account.
Go to Device >> Authentication Profile.
Check the authentication profile used for each account (noted in the previous step). 
If the Lockout Time is not set to "0" (zero), this is a finding.$$, 'medium', $$This should not be configured in Device >> Setup >> Management >> Authentication Settings; instead, an authentication profile should be configured with lockout settings of three failed attempts and a lockout time of zero minutes.
Go to Device >> Authentication Profile
Select the configured authentication profile, or select "Add" (in the bottom-left corner of the pane) to create a new one.
In the "Authentication Profile" field, enter the name of the authentication profile that will be used to control each person''s authentication process.
The "Lockout Time (min)" field is the lockout duration; this must be set to "0".  This will keep the lockout in effect until it is manually unlocked.
In the "Failed Attempts" field, enter "3".
Select "OK".

Apply the authentication profile to the Administrator accounts.
Go to Device >> Administrators
Select each configured account, or select "Add" (in the bottom-left corner of the pane) to create a new one.
In the "Authentication Profile" field, enter the configured authentication profile.
Select "OK".

This authentication profile should not be applied to the emergency administration account since it has special requirements.
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228661', $$If security personnel are not notified immediately upon storage volume utilization reaching 75%, they are unable to plan for storage capacity expansion. This could lead to the loss of audit information. Note that while the network device must generate the alert, notification may be done by a management server.$$, $$Go to Device >> Log Settings >> Alarms
If the Traffic Log DB, Threat Log DB, Configuration Log DB, System Log DB, Alarm DB, and HIP Match Log DB fields are not "75", this is a finding.$$, 'low', $$Go to Device >> Log Settings >> Alarms
Select the "Edit" icon (the gear symbol in the upper-right corner of the pane).

In the "Alarm Settings" window:
Select the "Enable Alarms" box.
In the "Traffic Log DB" field, enter "75".
In the "Threat Log DB" field, enter "75".
In the "Configuration Log DB" field, enter "75".
In the "System Log DB" field, enter "75".
In the "Alarm DB" field, enter "75".
In the "HIP Match Log DB" field, enter "75".
Select "OK".
Commit changes by selecting "Commit" in the upper-right corner of the screen.  Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228662', $$It is critical for the appropriate personnel to be aware if a system is at risk of failing to process audit logs as required. Without a real-time alert, security personnel may be unaware of an impending failure of the audit capability, and system operation may be adversely affected. 

Alerts provide organizations with urgent messages. Real-time alerts provide these messages immediately (i.e., the time from event detection to alert occurs in seconds or less).$$, $$Go to Device >> Log Settings >> Alarms.
If the "Enable Alarms" box is not checked, this is a finding.$$, 'low', $$Go to Device >> Log Settings >> Alarms.
Select the "Edit" icon (the gear symbol in the upper-right corner of the pane).
In the "Alarm Settings" window; select the "Enable Alarms" box.
Select "OK".
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228663', $$Inaccurate time stamps make it more difficult to correlate events and can lead to an inaccurate analysis. Determining the correct time a particular event occurred on a system is critical when conducting forensic analysis and investigating system events. Synchronizing internal information system clocks provides uniformity of time stamps for information systems with multiple system clocks and systems connected over a network.

Network Time Protocol (NTP) is used to synchronize the system clock of a computer to reference time source. The Palo Alto Networks security platform can be configured to use specified NTP servers. For synchronization with the NTP server(s), NTP uses a minimum polling value of 64 seconds and a maximum polling value of 1024 seconds. These minimum and maximum polling values are not configurable on the firewall.$$, $$Go to Device >> Setup >> Services.
In the "Services" window, the names or IP addresses of the Primary NTP Server and Secondary NTP Server must be present.
If the "Primary NTP Server" and "Secondary NTP Server" fields are blank, this is a finding.$$, 'low', $$Go to Device >> Setup >> Services.
Select the "Edit" icon (the gear symbol in the upper-right corner of the pane).
In the "Services" window, in the NTP tab, in the "Primary NTP Server Address" field and the "Secondary NTP Server Address" field, enter the IP address or hostname of the NTP servers.

In the "Authentication Type" field, select one of the following:
Symmetric Key; this option uses symmetric key exchange, which are shared secrets. Enter the key ID, algorithm, authentication key, and confirm the authentication key; for the algorithm, select "SHA1".
Autokey; this option uses auto key, or public key cryptography.
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228666', $$If time stamps are not consistently applied and there is no common time reference, it is difficult to perform forensic analysis.  Time stamps generated by the application include date and time and must be expressed in Coordinated Universal Time (UTC), also known as Zulu time, a modern continuation of Greenwich Mean Time (GMT).$$, $$Go to Device >> Setup >> Management
In the "General Settings" window, if the time zone is not set to "GMT" or "UTC", this is a finding.$$, 'medium', $$Go to Device >> Setup >> Management
In the "General Settings" window, select the "Edit" icon (the gear symbol in the upper-right corner of the pane).
In the "General Settings" window, in the "Time Zone" field, select "GMT" or "UTC" from the list of time zones.
Select "OK".
Commit changes by selecting "Commit" in the upper-right corner of the screen.  Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228667', $$The use of PIV credentials facilitates standardization and reduces the risk of unauthorized access.

DOD has mandated the use of the CAC to support identity management and personal authentication for systems covered under HSPD 12 and as a primary component of layered protection for national security systems.$$, $$Go to Device >> Certificate Management >> Certificates.
If no DOD Certification Authority (CA) certificates and subordinate certificates are imported, this is a finding.

Go to Device >> Setup >> Management.
In the Authentication Settings pane, if the Certificate Profile field is blank, this is a finding.

View the Certificate Profile, if it does not list the DOD CA certificates and subordinate certificates, this is a finding.

If the Use OCSP checkbox is not selected, this is a finding.$$, 'medium', $$Import the DOD CA certificates and subordinate certificates for all of the certificate authorities.
Go to Device >> Certificate Management >> Certificates.
Select the Import icon at the bottom of the pane.
In the Import Certificate window, complete the required information.
Select "OK".

Create a certificate profile.
Go to Device >> Setup >> Management.
In the Authentication Settings pane, select the select the "Edit" icon (the gear symbol in the upper-right corner).
In the Authentication Settings window, complete the required information.
In the Authentication Profile field, select "None".
In the Certificate Profile field, select "New Certificate Profile". This will change the Authentication Settings window to the Certificate Profile window.
Leave the username field blank.
Leave the domain field blank.
 
In the Certificate Profile window, complete the required fields.
In the CA Certificates section, select "Add" to import the DOD certificate authorities.
Select the Use OCSP checkbox.
When importing the top level DOD CA Certificate, for the Default OCSP URL field, add the DOD/DISA OCSP URL.
Select "OK".
Select "OK" again.
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228669', $$This requires the use of secure protocols instead of their unsecured counterparts, such as SSH instead of telnet, SCP instead of FTP, and HTTPS instead of HTTP. Note that HTTP OCSP is permitted to support OCSP where used. If unsecured protocols (lacking cryptographic mechanisms) are used for sessions, the contents of those sessions will be susceptible to manipulation, potentially allowing alteration and hijacking of maintenance sessions.$$, $$Go to Device >> Setup >> Management
In the "Management Interface Settings" window, view the enabled services.  
Note: Which management services are enabled.

If Telnet or HTTP is selected, this is a finding.$$, 'medium', $$Go to Device >> Setup >> Management.
In the "Management Interface Settings" window, select the "Edit" icon (the gear symbol in the upper-right corner).
In the "Management Interface Settings" window, make sure that Telnet or HTTP are not selected.
Select "OK".
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228670', $$SNMP Versions 1 and 2 are not considered secure. Without the strong authentication and privacy that is provided by the SNMP Version 3 User-based Security Model (USM), an unauthorized user can gain access to network management information used to launch an attack against the network.  SNMP Versions 1 and 2 cannot authenticate the source of a message nor can they provide encryption. Without authentication, it is possible for nonauthorized users to exercise SNMP network management functions. It is also possible for nonauthorized users to eavesdrop on management information as it passes from managed systems to the management system.$$, $$Go to Device >> Setup >> Operations; in the Miscellaneous pane, select SNMP Setup.
In the SNMP Setup window, check if SNMP V3 is selected.  
If V3 is not selected, this is a finding.

Go to Device >> Server Profiles >> SNMP Trap.
View the list of configured SNMP servers; if the Version is not "v3", this is a finding.$$, 'high', $$Go to Device >> Setup >> Operations; in the Miscellaneous pane, select SNMP Setup.
In the SNMP Setup window, complete the required fields.
For the Version, select V3.
Configure a view and assign it to a user.
In the upper half of the SNMP Setup window, select "Add".
In the Views window, complete the required fields; obtain the values for the OID and Mask fields from product documentation or vendor support.
In the Option field, select "include". 
Select "OK".
In the lower half of the SNMP Setup window, select "Add".
Complete the required fields.
Select "OK".
Obtain the engineID of the Palo Alto device by issuing an SNMPv3 GET from the management workstation against the OID of the Palo Alto device.
Configure the SNMPv3 Trap Server profile; go to Device >> Server Profiles >> SNMP Trap; select "Add".
In the SNMP Trap Server Profile window, complete the required fields. 
Select "OK".
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228671', $$Information stored in one location is vulnerable to accidental or incidental deletion or alteration.  Off-loading is a common process in information systems with limited audit storage capacity.

The Palo Alto Networks security platform has multiple log types; at a minimum, the Traffic, Threat, System, and Configuration logs must be sent to a Syslog server.$$, $$To view a syslog server profile,
Go to Device >> Server Profiles >> Syslog
If there are no Syslog Server Profiles present, this is a finding.

Select each Syslog Server Profile.
If no server is configured, this is a finding.

View the log-forwarding profile to determine which logs are forwarded to the syslog server.
Go to Objects >> Log forwarding
If no Log Forwarding Profile is present, this is a finding.

The "Log Forwarding Profile" window has five columns.  
If there are no Syslog Server Profiles present in the Syslog column for the Traffic Log Type, this is a finding.

If there are no Syslog Server Profiles present for each of the severity levels of the Threat Log Type, this is a finding.
 
Go to Device >> Log Settings >> System Logs
The list of Severity levels is displayed.  
If any of the Severity levels does not have a configured Syslog Profile, this is a finding.

Go to Device >> Log Settings >> Config Logs
If the "Syslog" field is blank, this is a finding.$$, 'medium', $$To create a syslog server profile:
Go to Device >> Server Profiles >> Syslog
Select "Add". 
In the Syslog Server Profile, enter the name of the profile.
Select "Add".
In the "Servers" tab, enter the required information.
Name: Name of the syslog server
Server: Server IP address where the logs will be forwarded to
Port: Default port 514
Facility: Select from the drop-down list.
Select "OK".

After creating the Server Profiles that define where to logs, enable log forwarding.  
The way to enable forwarding depends on the log type:
Traffic Logs—Enable forwarding of Traffic logs by creating a Log Forwarding Profile (Objects >> Log Forwarding) and adding it to the security policies to trigger the log forwarding. Only traffic that matches a specific rule within the security policy will be logged and forwarded.
Configure the log-forwarding profile to select the logs to be forwarded to syslog server.
Go to Objects >> Log forwarding
The Log Forwarding Profile window appears.  Note that it has five columns.  In the Syslog column, select the syslog server profile for forwarding threat logs to the configured server(s).
Select "OK".

When the Log Forwarding Profile window disappears, the screen will show the configured log-forwarding profile.
Threat Logs—Enable forwarding of Threat logs by creating a Log Forwarding Profile (Objects >> Log Forwarding) that specifies which severity levels to forward and then adding it to the security policies, which triggers the log forwarding. A Threat log entry will only be created (and therefore forwarded) if the associated traffic matches a Security Profile (Antivirus, Anti-spyware, Vulnerability, URL Filtering, File Blocking, Data Filtering, or DoS Protection).
Configure the log-forwarding profile to select the logs to be forwarded to syslog server.
Go to Objects >> Log forwarding
The Log Forwarding Profile window appears.  Note that it has five columns.  In the "Syslog" column, select the syslog server profile for forwarding threat logs to the configured server(s).
Select "OK".

When the Log Forwarding Profile window disappears, the screen will show the configured log-forwarding profile.
System Logs—Enable forwarding of System logs by specifying a Server Profile in the log settings configuration. 
Go to Device >> Log Settings >> System Logs
The list of severity levels is displayed.
Select a Server Profile for each severity level to forward.  
Select each severity level in turn; with each selection, the "Log Systems - Setting" window will appear.  
In the "Log Systems - Setting" window, in the "Syslog drop-down" box, select the configured Server Profile.
Select "OK". 
Config Logs—Enable forwarding of Config logs by specifying a Server Profile in the log settings configuration. 
Go to Device >> Log Settings >> Config Logs
Select the "Edit" icon (the gear symbol in the upper-right corner of the pane).
In the "Log Settings Config" window, in the "Syslog drop-down" box, select the configured Server Profile.
Select "OK".

For Traffic Logs and Threat Logs, use the log forwarding profile in the security rules.
Go to Policies >> Security Rule
Select the rule for which the log forwarding needs to be applied.
Apply the security profiles to the rule.
Go to Actions >> Log forwarding
Select the log forwarding profile from drop-down list.
Commit changes by selecting "Commit" in the upper-right corner of the screen.  Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228672', $$CJCSM 6510.01B, "Cyber Incident Handling Program", in subsection e.(6)(c) sets forth three requirements for Cyber events detected by an automated system:
If the cyber event is detected by an automated system, an alert will be sent to the POC designated for receiving such automated alerts.
CC/S/A/FAs that maintain automated detection systems and sensors must ensure that a POC for receiving the alerts has been defined and that the IS configured to send alerts to that POC.
The POC must then ensure that the cyber event is reviewed as part of the preliminary analysis phase and reported to the appropriate individuals if it meets the criteria for a reportable cyber event or incident.

By immediately displaying an alarm message, potential security violations can be identified more quickly even when administrators are not logged on to the network device. An example of a mechanism to facilitate this would be through the utilization of SNMP traps.

The Palo Alto Networks security platform can be configured to send messages to an SNMP server and to an email server as well as a Syslog server. SNMP traps can be generated for each of the five logging event types on the firewall: traffic, threat, system, hip, config. For this requirement, only the threat logs must be sent. Note that only traffic that matches an action in a rule will be logged and forwarded. In the case of traps, the messages are initiated by the firewall and sent unsolicited to the management stations. 

The use of email as a notification method may result in a very larger number of messages being sent and possibly overwhelming the email server as well as the POC. The use of SNMP is preferred over email in general, but organizations may want to use email in addition to SNMP for high-priority messages.$$, $$Note: The actual method is determined by the organization.
Review the system/network documentation to determine who the Points of Contact are and which methods are being used. 
If the selected method is SNMP, verify that the device is configured.
Go to Device >> Server Profiles.
If no SNMP servers are configured, this is a finding.
 
Go to Objects >> Log Forwarding.
If no Log Forwarding Profile is listed, this is a finding.

If the "Log Type" column does not include "Threat", this is a finding.

If any Severity is not listed, this is a finding.$$, 'medium', $$For SNMP traps, follow the following steps:
Configure the SNMP Trap Destinations; go to Device >> Server Profiles >> SNMP Trap.
Select "Add".

In the "SNMP Trap Server Profile" window, enter the required information.
For SNMP Version, select "V3". 
Enter the name of the SNMP Server Profile.
Select "Add". 
Server—Specify the SNMP trap destination name (up to 31 characters).
Manager—Specify the IP address of the trap destination.
User—Specify the SNMP user.
EngineID—Specify the engine ID of the firewall. The input is a string in hexadecimal representation. The engine ID is any number between 5 to 64 bytes. When represented as a hexadecimal string, this is between 10 and 128 characters (2 characters for each byte) with two additional characters for 0x that must be used as a prefix in the input string.
Auth Password—Specify the user’s authentication password (minimum 8 characters, maximum of 256 characters, and no character restrictions). Only Secure Hash Algorithm (SHA) is supported.
Priv Password—Specify the user’s encryption password (minimum 8 characters, maximum of 256 characters, and no character restrictions). Only Advanced Encryption Standard (AES) is supported.
Select "OK".

Configure generating "Traps for Threat" events:
Go to Objects >> Log Forwarding.
Select "Add".
In the "Log Forwarding Profile" window, enter the required information.
Enter the name of the Log Forwarding Profile.
In the "Threat Settings" section, in the "SNMP Trap" field for each Severity, select the SNMP Trap Server Profile.
Select "OK".

Add the Log Forwarding Profile to the security policies to trigger log forwarding to the SNMP server.
Go to Policies >> Security.
Select the rule for which the log forwarding needs to be applied. Apply the security profiles to the rule.
Go to "Actions" (tab); in the "Log forwarding" field, select the "log forwarding" profile.
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228673', $$The use of authentication servers or other centralized management servers for providing centralized authentication services is required for network device management. Maintaining local administrator accounts for daily usage on each network device without centralized management is not scalable or feasible. Without centralized management, it is likely that credentials for some network devices will be forgotten, leading to delays in administration, which itself leads to delays in remediating production problems and in addressing compromises in a timely fashion.

Only the emergency administration account, also known as the account of last resort, can be locally configured on the device.$$, $$Ask the Administrator which form of centralized authentication server is being used.
Navigate to the appropriate window to view the configured server(s). 
For RADIUS, go to Device >> Server Profiles >> RADIUS.
For LDAP, go to Device >> Server Profiles >> LDAP.
For Kerberos, go to Device >> Server Profiles >> Kerberos.
If there are no servers configured in the window that match the specified form of centralized authentication, this is a finding.

Go to Device >> Authentication Profile.
If no authentication profile exists that match the specified form of centralized authentication, this is a finding.

Go to Device >> Administrators.
View each Administrator''s account.
If no authentication profile exists that match the specified form of centralized authentication, this is a finding. The only exception is the emergency administration account.$$, 'medium', $$The device allows three different authentication protocols; RADIUS, LDAP, and Kerberos. In this explanation, LDAP is used. 
To configure the Palo Alto Networks security platform to use an LDAP server, follow these steps:
Go to Device >> Server-Profiles >> LDAP.
Select "Add" (lower left of window).
Populate the required fields.
Enter the name of the profile in the "Name" field.
In the server box enter the name of the server in the "Name" field.
Enter the IP Address of the server. 
Enter the Port number the firewall should use to connect to the LDAP server (default=389 for LDAP; 636 for LDAP over SSL). 
Enter the LDAP Domain name to prepend to all objects learned from the server. The value entered here depends on the specific deployment. If using Active Directory, enter the NetBIOS domain name; not a FQDN (for example, enter acme, not acme.com). Note that if collecting data from multiple domains, it is necessary to create separate server profiles. If using a global catalog server, leave this field blank.
Select the Type of LDAP server connecting to. The correct LDAP attributes in the group mapping settings will automatically be populated based on the selection.
In the Base field, select the DN that corresponds to the point in the LDAP tree where the firewall is to begin its search for user and group information.
Select (check) the SSL checkbox.
Select "OK".

To create an Authentication Profile using the newly created LDAP server, follow these steps:
Go to Device >> Authentication Profile.
Select "Add" (lower left of window).
Populate the required fields as needed.
In the Authentication field, select "LDAP".
In the Server Profile field, select the configured LDAP server profile.
In the Login Attribute field, enter “sAMAccountName”. 
Select "OK".

Apply the authentication profile to the Administrator accounts.
Go to Device >> Administrators.
Select each configured account or select "Add" (in the bottom-left corner of the pane) to create a new one.
In the "Authentication Profile" field, enter the configured LDAP authentication profile.
Select "OK".
Note: The name of the administrator must match the name of the user in the LDAP server.
Note: The authentication profile should not be applied to the emergency administration account since it has special requirements.
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.

Note that the emergency administration account is the only account that is configured locally on the device itself.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228674', $$DoD Instruction 8520.02, Public Key Infrastructure (PKI) and Public Key (PK) Enabling mandates that certificates must be issued by the DoD PKI or by a DoD-approved PKI for authentication, digital signature, or encryption.$$, $$Go to Device >> Certificate Management >> Certificates
Installed Certificates are listed in the "Device Certificates" tab.
If any of the have the name or identifier of a non-approved source in the "Issuer" field, this is a finding.$$, 'medium', $$Obtain a Device Certificate from the DoD PKI or from a DoD-approved PKI:
Go to Device >> Certificate Management >> Certificates
Select "Import" (at the bottom of the pane). 
In the "Import Certificate" pane, complete each field.
Select "OK".$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228675', $$Password profiles override settings made in the Minimum Password Complexity window.  If Password Profiles are used they can bypass password complexity requirements.$$, $$Go to Device >> Password Profiles
If there are configured Password Profiles, this is a finding.$$, 'medium', $$Go to Device >> Password Profiles
If the screen is blank (no configured Password Profiles), do nothing.

If there are configured Password Profiles, identify which accounts are using them and bring this to the attention of the ISSO immediately.
Delete the Password Profiles when authorized to make changes to the device in accordance with local change management policies.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228676', $$To assure accountability and prevent unauthenticated access, organizational administrators must be uniquely identified and authenticated for all network management accesses to prevent potential misuse and compromise of the system.

The use of a default password for any account, especially one for administrative access, can quickly lead to a compromise of the device and subsequently, the entire enclave or system.  The "admin" account is intended solely for the initial setup of the device and must be disabled when the device is initially configured.  The default password for this account must immediately be changed at the first login of an authorized administrator.$$, $$Open a web browser at an authorized workstation and enter the management IP address of the Palo Alto Networks security platform.
Use HTTP Secure (HTTPS) instead of HTTP since HTTP is disabled by default.
The logon window will appear.
Enter "admin" into both the "Name" and "Password" fields.  
If anything except the logon screen with the message "Invalid username or password" appears, this is a finding.$$, 'high', $$Go to Device >> Administrators
Select the admin user.
In the "Old Password" field, enter "admin".
In the "New Password" field, enter the new password.
In the "Confirm New Password" field, enter the new password.
Select "OK".
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228677', $$Auditing and logging are key components of any security architecture. Logging the actions of specific events provides a means to investigate an attack; to recognize resource utilization or capacity thresholds; or to identify an improperly configured network device. If auditing is not comprehensive, it will not be useful for intrusion monitoring, security investigations, and forensic analysis.

If the Data Plane CPU utilization is 100%, this may indicate an attack or simply an over-utilized device.  In either case, action must be taken to identify the source of the issue and take corrective action.$$, $$Go to Device >> Setup >> Management
In the "Logging and Reporting Settings" pane.
If the "Enable Log on High DP Load" check box is not selected, this is a finding.$$, 'medium', $$Go to Device >> Setup >> Management
In the "Logging and Reporting Settings" pane, select the "Edit" icon (the gear symbol in the upper-right corner of the pane).
In the "Log Export and Reporting" tab, select the "Enable Log on High DP Load" check box.  
Select "OK".
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-228678', $$If Network Time Protocol is not authenticated, an attacker can introduce a rogue NTP server.  This rogue server can then be used to send incorrect time information to network devices, which will make log timestamps inaccurate and affected scheduled actions.  NTP authentication is used to prevent this tampering by authenticating the time source.$$, $$Go to Device >> Setup >> Services
In the "Services" window, the Primary NTP Server Authentication Type and Secondary NTP Server Authentication Type must be either Symmetric Key or Autokey. If the "Primary NTP Server Authentication Type" and "Secondary NTP Server Authentication Type" fields are "none", this is a finding.$$, 'medium', $$Go to Device >> Setup >> Services
Select the "Edit" icon (the gear symbol in the upper-right corner of the pane).
In the "Services" window, in the NTP tab, in the "Primary NTP Server Address" field and the "Secondary NTP Server Address" field, enter the IP address or hostname of the NTP servers.

In the "Authentication Type" field, select one of the following:
Symmetric Key; this option uses symmetric key exchange, which are shared secrets. Enter the key ID, algorithm, authentication key, and confirm the authentication key; for the algorithm, select "SHA1".
Autokey; this option uses auto key, or public key cryptography.
Commit changes by selecting "Commit" in the upper-right corner of the screen.
Select "OK" when the confirmation dialog appears.$$);

INSERT INTO paloalto (id, description, checktext, severity, remediation) VALUES ('V-268323', $$Authentication for administrative (privileged level) access to the device is required at all times. An account can be created on the device''s local database for use when the authentication server is down or connectivity between the device and the authentication server is not operable. This account is referred to as the account of last resort since it is intended to be used as a last resort and when immediate administrative access is absolutely necessary.

The account of last resort logon credentials must be stored in a sealed envelope and kept in a safe. The safe must be periodically audited to verify the envelope remains sealed. The signature of the auditor and the date of the audit should be added to the envelope as a record. Administrators should secure the credentials and disable the root account (if possible) when not needed for system administration functions.$$, $$Navigate to Device >> Administrators.

If there is an authentication profile/user account configured (and enabled) for any account other than the emergency administration account, this is a finding.$$, 'medium', $$Navigate to Device >> Authentication Profile.
Remove any unauthorized or unnecessary authentication profiles for users other than the account of last resort.
Select "OK".$$);