# Generated by Django 5.1 on 2025-04-30 14:09

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0005_auditornotification"),
        ("audit_reports", "0003_scriptgeneratedreport"),
        ("client_portal", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="scriptgeneratedreport",
            name="auditor",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="script_reports",
                to="accounts.profile",
                verbose_name="Auditor",
            ),
        ),
        migrations.AddField(
            model_name="scriptgeneratedreport",
            name="client",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="script_reports",
                to="client_portal.clientprofile",
                verbose_name="Client",
            ),
        ),
    ]
