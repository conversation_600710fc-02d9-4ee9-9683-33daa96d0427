{"name": "audit-platform-react", "version": "1.0.0", "description": "React components for Audit Platform", "main": "index.js", "scripts": {"dev": "webpack --mode development --watch", "build": "webpack --mode production"}, "keywords": ["react", "django", "audit"], "author": "", "license": "ISC", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "axios": "^1.6.2"}, "devDependencies": {"@babel/core": "^7.23.3", "@babel/preset-env": "^7.23.3", "@babel/preset-react": "^7.23.3", "babel-loader": "^9.1.3", "webpack": "^5.89.0", "webpack-cli": "^5.1.4"}}