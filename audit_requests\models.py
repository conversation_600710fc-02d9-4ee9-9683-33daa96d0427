from django.db import models
from django_extensions.db.fields import AutoSlugField
from equipment.models import Item
from audit_process.models import AuditScript


class AuditRequest(models.Model):
    """
    Model representing an audit request from a client with various details and status.

    NOTE: This model should be consolidated with the AuditRequest model in audit_process/models.py
    in the future. Currently, they serve different purposes but have the same name, which
    can cause confusion. This model is used primarily by the client portal.
    """

    slug = AutoSlugField(populate_from='institution_name', unique=True)
    date = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Request Date'
    )
    institution_name = models.CharField(
        max_length=30,
        blank=False,
        null=False,
        verbose_name='Institution Name'
    )
    phone_number = models.CharField(
        max_length=30,
        blank=False,
        null=False,
        help_text='Phone number of the client'
    )
    email = models.EmailField(
        blank=False,
        null=False,
        help_text='Email address of the client'
    )
    address = models.Char<PERSON>ield(
        max_length=255,
        blank=False,
        null=False,
        help_text='Address of the client'
    )
    description = models.TextField(
        blank=False,
        null=False,
        help_text='Description of the audit request'
    )
    equipment = models.ManyToManyField(
        Item,
        related_name='audit_requests',
        verbose_name='Equipment',
        help_text='Equipment to be audited'
    )
    script = models.ForeignKey(
        AuditScript,
        on_delete=models.SET_NULL,
        related_name='client_audit_requests',
        null=True,
        blank=True,
        verbose_name='Audit Script',
        help_text='Script to be used for the audit'
    )
    amount = models.FloatField(
        verbose_name='Amount',
        help_text='Amount for the audit (optional)',
        blank=True,
        null=True
    )
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('completed', 'Completed')
    ]
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name='Request Status',
        help_text='Current status of the audit request'
    )

    def __str__(self):
        return f"Audit Request from {self.institution_name} ({self.date.strftime('%Y-%m-%d')})"

    class Meta:
        verbose_name = "Client Audit Request"
        verbose_name_plural = "Client Audit Requests"
        ordering = ["-date"]
        db_table = "client_audit_requests"  # Renamed to avoid conflicts with audit_process.AuditRequest
