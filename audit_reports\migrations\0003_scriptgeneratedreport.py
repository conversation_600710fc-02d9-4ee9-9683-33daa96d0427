# Generated by Django 5.1 on 2025-04-30 13:32

import django.db.models.deletion
import django.utils.timezone
import django_extensions.db.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("audit_process", "0003_ongoingaudit_client_audit_request"),
        ("audit_reports", "0002_alter_auditreport_additional_fees_and_more"),
        ("equipment", "0002_delete_delivery"),
    ]

    operations = [
        migrations.CreateModel(
            name="ScriptGeneratedReport",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "slug",
                    django_extensions.db.fields.AutoSlugField(
                        blank=True, editable=False, populate_from="title", unique=True
                    ),
                ),
                (
                    "title",
                    models.CharField(max_length=100, verbose_name="Report Title"),
                ),
                (
                    "date_created",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="Date Created"
                    ),
                ),
                (
                    "date_uploaded",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Date Uploaded"
                    ),
                ),
                (
                    "report_file",
                    models.FileField(
                        upload_to="script_reports/",
                        verbose_name="Report File (Excel/CSV)",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, null=True, verbose_name="Report Description"
                    ),
                ),
                (
                    "findings",
                    models.TextField(
                        blank=True, null=True, verbose_name="Key Findings"
                    ),
                ),
                (
                    "recommendations",
                    models.TextField(
                        blank=True, null=True, verbose_name="Recommendations"
                    ),
                ),
                (
                    "compliance_score",
                    models.FloatField(default=0.0, verbose_name="Compliance Score (%)"),
                ),
                (
                    "is_compliant",
                    models.BooleanField(default=False, verbose_name="Is Compliant"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Review"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("archived", "Archived"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Report Status",
                    ),
                ),
                (
                    "script_name",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Script Name",
                    ),
                ),
                (
                    "script_version",
                    models.CharField(
                        blank=True,
                        max_length=20,
                        null=True,
                        verbose_name="Script Version",
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="script_reports",
                        to="equipment.category",
                        verbose_name="Equipment Category",
                    ),
                ),
                (
                    "equipment",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="script_reports",
                        to="equipment.item",
                        verbose_name="Audited Equipment",
                    ),
                ),
                (
                    "ongoing_audit",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="script_reports",
                        to="audit_process.ongoingaudit",
                        verbose_name="Related Audit",
                    ),
                ),
            ],
            options={
                "verbose_name": "Script Generated Report",
                "verbose_name_plural": "Script Generated Reports",
                "ordering": ["-date_created"],
            },
        ),
    ]
