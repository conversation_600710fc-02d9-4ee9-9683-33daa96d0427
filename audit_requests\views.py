# Django core imports
from django.urls import reverse
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from django.contrib.auth.decorators import login_required

# Class-based views
from django.views.generic import (
    CreateView,
    UpdateView,
    DeleteView
)

# Authentication and permissions
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin

# Third-party packages
from django_tables2 import SingleTableView
from django_tables2.export.views import ExportMixin

# Local app imports
from .models import AuditRequest as ClientAuditRequest  # Renamed to avoid confusion
from .tables import AuditRequestTable
from .forms import AuditRequestForm, AuditorAuditRequestForm
from accounts.models import Profile


class AuditRequestListView(LoginRequiredMixin, ExportMixin, SingleTableView):
    """View for listing audit requests."""
    model = ClientAuditRequest
    table_class = AuditRequestTable
    template_name = 'audit_requests/bill_list.html'
    context_object_name = 'audit_requests'
    paginate_by = 10
    SingleTableView.table_pagination = False


class AuditRequestCreateView(LoginRequiredMixin, CreateView):
    """View for creating a new audit request."""
    model = ClientAuditRequest
    form_class = AuditRequestForm

    def get_template_names(self):
        """Return the template name based on the user's role."""
        # If the user is an auditor, use the auditor template
        if hasattr(self.request.user, 'profile') and self.request.user.profile.role == 'AU':
            return ['audit_requests/auditor_audit_request_form.html']
        # Otherwise, use the default template
        return ['audit_requests/billcreate.html']

    def get_form_class(self):
        """Return the form class based on the user's role."""
        # If the user is an auditor, use the auditor form
        if hasattr(self.request.user, 'profile') and self.request.user.profile.role == 'AU':
            return AuditorAuditRequestForm
        # Otherwise, use the default form
        return AuditRequestForm

    def get_form_kwargs(self):
        """Pass the user to the form."""
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def get_success_url(self):
        """Redirect to the list of audit requests after a successful update."""
        return reverse('audit_request_list')

    def get_context_data(self, **kwargs):
        """Add additional context for the template."""
        context = super().get_context_data(**kwargs)
        # Add a flag to indicate if the user is an auditor
        context['is_auditor'] = hasattr(self.request.user, 'profile') and self.request.user.profile.role == 'AU'
        return context


class AuditRequestUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    """View for updating an existing audit request."""
    model = ClientAuditRequest
    template_name = 'audit_requests/billupdate.html'
    form_class = AuditRequestForm

    def get_form_class(self):
        """Return the form class based on the user's role."""
        # If the user is an auditor, use the auditor form
        if hasattr(self.request.user, 'profile') and self.request.user.profile.role == 'AU':
            return AuditorAuditRequestForm
        # Otherwise, use the default form
        return AuditRequestForm

    def get_form_kwargs(self):
        """Pass the user to the form."""
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def test_func(self):
        """Check if the user has the required permissions."""
        # Allow superusers and auditors to update audit requests
        if self.request.user.is_superuser:
            return True

        # Check if the user has a profile
        if not hasattr(self.request.user, 'profile'):
            return False

        # Allow auditors to update any audit request
        if self.request.user.profile.role == 'AU':
            return True

        # For clients, check if they created the audit request
        audit_request = self.get_object()
        if hasattr(self.request.user, 'client_profile') and self.request.user.client_profile:
            # Check if the email matches
            if audit_request.email and self.request.user.email:
                if audit_request.email.lower() == self.request.user.email.lower():
                    return True

        return False

    def get_success_url(self):
        """Redirect to the list of audit requests after a successful update."""
        return reverse('audit_request_list')

    def get_context_data(self, **kwargs):
        """Add additional context for the template."""
        context = super().get_context_data(**kwargs)
        # Add a flag to indicate if the user is an auditor
        context['is_auditor'] = hasattr(self.request.user, 'profile') and self.request.user.profile.role == 'AU'
        return context


class AuditRequestDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    """View for deleting an audit request."""
    model = ClientAuditRequest
    template_name = 'audit_requests/billdelete.html'

    def test_func(self):
        """Check if the user is a superuser or an auditor."""
        # Allow superusers and auditors to delete audit requests
        if self.request.user.is_superuser:
            return True

        # Check if the user has a profile and is an auditor
        try:
            profile = self.request.user.profile
            return profile.is_auditor
        except (Profile.DoesNotExist, AttributeError):
            return False

    def get_success_url(self):
        """Redirect to the list of audit requests after successful deletion."""
        return reverse('audit_request_list')


@login_required
def get_audit_request_details(request, pk):
    """API endpoint to get audit request details."""
    audit_request = get_object_or_404(ClientAuditRequest, pk=pk)

    # Return the audit request details as JSON
    return JsonResponse({
        'id': audit_request.id,
        'institution_name': audit_request.institution_name,
        'phone_number': str(audit_request.phone_number) if audit_request.phone_number else '',
        'email': audit_request.email or '',
        'description': audit_request.description or '',
        'status': audit_request.status,
    })
