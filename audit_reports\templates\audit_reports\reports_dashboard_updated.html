{% extends "equipment/base.html" %}
{% load static %}

{% block title %}Equipment Audit Reports{% endblock title %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'audit_reports/css/dashboard.css' %}">
{% endblock %}

{% block content %}
<div class="container dashboard-container">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <div>
                <h2 class="text-success">Equipment Audit Reports</h2>
                <p class="text-muted">View statistics and reports about audit results, equipment categories, and audit findings.</p>
            </div>
            <div>
                <a href="{% url 'audit_process_info' %}" class="btn btn-outline-info">
                    <i class="fas fa-info-circle me-2"></i> How Audit Process Works
                </a>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card dashboard-card">
                <div class="card-body summary-card">
                    <h5 class="card-title">Total Audit Reports</h5>
                    <h2 class="display-4 text-success">{{ total_reports }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card dashboard-card">
                <div class="card-body summary-card">
                    <h5 class="card-title">Passed Audits</h5>
                    <h2 class="display-4 text-success">{{ passed_reports }}</h2>
                    <p class="text-muted">{{ passed_percentage }}% of total</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card dashboard-card">
                <div class="card-body summary-card">
                    <h5 class="card-title">Failed Audits</h5>
                    <h2 class="display-4 text-danger">{{ failed_reports }}</h2>
                    <p class="text-muted">{{ failed_percentage }}% of total</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card dashboard-card">
                <div class="card-body summary-card">
                    <h5 class="card-title">Equipment Categories</h5>
                    <h2 class="display-4 text-primary">{{ category_count }}</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-md-6 mb-3">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Audit Results</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="auditResultsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-3">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Equipment Categories</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="equipmentCategoriesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reports by Month Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Audit Reports by Month</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="reportsByMonthChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Reports Table -->
    <div class="row">
        <div class="col-12">
            <div class="card dashboard-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Recent Audit Reports</h5>
                    <a href="{% url 'auditreportlist' %}" class="btn btn-sm btn-success">View All</a>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table table-striped table-hover dashboard-table">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Client</th>
                                    <th>Equipment</th>
                                    <th>Result</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for report in recent_reports %}
                                <tr>
                                    <td>{{ report.date|date:"Y-m-d" }}</td>
                                    <td>{{ report.client_name }}</td>
                                    <td>{{ report.equipment.name }}</td>
                                    <td>
                                        {% if report.audit_result %}
                                        <span class="badge bg-success status-badge">Pass</span>
                                        {% else %}
                                        <span class="badge bg-danger status-badge">Fail</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'auditreport-detail' report.slug %}" class="btn btn-sm btn-info action-btn">
                                            <i class="fa-solid fa-eye"></i> View
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center">No audit reports found.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // Audit Results Chart
    const auditResultsCtx = document.getElementById('auditResultsChart').getContext('2d');
    const auditResultsChart = new Chart(auditResultsCtx, {
        type: 'doughnut',
        data: {
            labels: ['Passed', 'Failed'],
            datasets: [{
                data: [{{ passed_reports }}, {{ failed_reports }}],
                backgroundColor: [
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(220, 53, 69, 0.8)'
                ],
                borderColor: [
                    'rgba(40, 167, 69, 1)',
                    'rgba(220, 53, 69, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Equipment Categories Chart
    const categoriesCtx = document.getElementById('equipmentCategoriesChart').getContext('2d');
    const categoriesChart = new Chart(categoriesCtx, {
        type: 'bar',
        data: {
            labels: {{ category_names|safe }},
            datasets: [{
                label: 'Number of Audits',
                data: {{ category_counts|safe }},
                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });

    // Reports by Month Chart
    const monthlyCtx = document.getElementById('reportsByMonthChart').getContext('2d');
    const monthlyChart = new Chart(monthlyCtx, {
        type: 'line',
        data: {
            labels: {{ monthly_labels|safe }},
            datasets: [{
                label: 'Passed Audits',
                data: {{ monthly_passed|safe }},
                backgroundColor: 'rgba(40, 167, 69, 0.2)',
                borderColor: 'rgba(40, 167, 69, 1)',
                borderWidth: 2,
                tension: 0.1
            }, {
                label: 'Failed Audits',
                data: {{ monthly_failed|safe }},
                backgroundColor: 'rgba(220, 53, 69, 0.2)',
                borderColor: 'rgba(220, 53, 69, 1)',
                borderWidth: 2,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });
</script>
{% endblock content %}
