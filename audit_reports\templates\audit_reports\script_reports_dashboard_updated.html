{% extends "equipment/base.html" %}
{% load static %}

{% block title %}Script Generated Reports{% endblock title %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'audit_reports/css/dashboard.css' %}">
{% endblock %}

{% block content %}
<div class="container dashboard-container">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <div>
                <h2 class="text-success">Script Generated Reports</h2>
                <p class="text-muted">View and manage reports generated by audit scripts.</p>
            </div>
            <div>
                <a href="{% url 'audit_process_info' %}" class="btn btn-outline-info">
                    <i class="fas fa-info-circle me-2"></i> How Audit Scripts Work
                </a>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card dashboard-card">
                <div class="card-body summary-card">
                    <h5 class="card-title">Total Reports</h5>
                    <h2 class="display-4 text-success">{{ total_reports }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card dashboard-card">
                <div class="card-body summary-card">
                    <h5 class="card-title">Compliant</h5>
                    <h2 class="display-4 text-success">{{ compliant_reports }}</h2>
                    <p class="text-muted">{{ compliant_percentage }}% of total</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card dashboard-card">
                <div class="card-body summary-card">
                    <h5 class="card-title">Non-Compliant</h5>
                    <h2 class="display-4 text-danger">{{ non_compliant_reports }}</h2>
                    <p class="text-muted">{{ non_compliant_percentage }}% of total</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card dashboard-card">
                <div class="card-body summary-card">
                    <h5 class="card-title">Average Compliance</h5>
                    <h2 class="display-4 text-primary">{{ avg_compliance_score }}%</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-md-6 mb-3">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Compliance Status</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="complianceChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-3">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Reports by Category</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="categoryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Upload New Report Button -->
    <div class="row mb-4">
        <div class="col-12">
            <a href="{% url 'script_report_create' %}" class="btn btn-success action-btn">
                <i class="fa-solid fa-upload me-2"></i> Upload New Script Report
            </a>
        </div>
    </div>

    <!-- Reports Table -->
    <div class="row">
        <div class="col-12">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Script Generated Reports</h5>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table table-striped table-hover dashboard-table">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Date</th>
                                    <th>Equipment</th>
                                    <th>Category</th>
                                    <th>Compliance Score</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for report in reports %}
                                <tr>
                                    <td>{{ report.title }}</td>
                                    <td>{{ report.date_created|date:"Y-m-d" }}</td>
                                    <td>{{ report.equipment.name }}</td>
                                    <td>{{ report.category.name }}</td>
                                    <td>
                                        <div class="progress compliance-progress">
                                            <div class="progress-bar {% if report.is_compliant %}bg-success{% else %}bg-danger{% endif %}" 
                                                 role="progressbar" 
                                                 style="width: {{ report.compliance_score }}%;" 
                                                 aria-valuenow="{{ report.compliance_score }}" 
                                                 aria-valuemin="0" 
                                                 aria-valuemax="100">
                                                {{ report.compliance_score }}%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if report.status == 'pending' %}
                                            <span class="badge bg-warning status-badge">Pending Review</span>
                                        {% elif report.status == 'approved' %}
                                            <span class="badge bg-success status-badge">Approved</span>
                                        {% elif report.status == 'rejected' %}
                                            <span class="badge bg-danger status-badge">Rejected</span>
                                        {% elif report.status == 'archived' %}
                                            <span class="badge bg-secondary status-badge">Archived</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'script_report_detail' report.slug %}" class="btn btn-sm btn-info action-btn">
                                                <i class="fa-solid fa-eye"></i>
                                            </a>
                                            <a href="{{ report.report_file.url }}" class="btn btn-sm btn-primary action-btn" download>
                                                <i class="fa-solid fa-download"></i>
                                            </a>
                                            <a href="{% url 'script_report_update' report.slug %}" class="btn btn-sm btn-warning action-btn">
                                                <i class="fa-solid fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center">No script-generated reports found.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // Compliance Status Chart
    const complianceCtx = document.getElementById('complianceChart').getContext('2d');
    const complianceChart = new Chart(complianceCtx, {
        type: 'doughnut',
        data: {
            labels: ['Compliant', 'Non-Compliant'],
            datasets: [{
                data: [{{ compliant_reports }}, {{ non_compliant_reports }}],
                backgroundColor: [
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(220, 53, 69, 0.8)'
                ],
                borderColor: [
                    'rgba(40, 167, 69, 1)',
                    'rgba(220, 53, 69, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Category Chart
    const categoryCtx = document.getElementById('categoryChart').getContext('2d');
    const categoryChart = new Chart(categoryCtx, {
        type: 'bar',
        data: {
            labels: {{ category_names|safe }},
            datasets: [{
                label: 'Number of Reports',
                data: {{ category_counts|safe }},
                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });
</script>
{% endblock content %}
