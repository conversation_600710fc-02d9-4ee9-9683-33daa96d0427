import django_tables2 as tables
from .models import AuditRequest as ClientAuditRequest  # Renamed to avoid confusion


class AuditRequestTable(tables.Table):
    """Table view for displaying audit requests."""

    class Meta:
        """Meta options for the AuditRequestTable."""
        model = ClientAuditRequest
        template_name = "django_tables2/semantic.html"
        fields = (
            'date',
            'institution_name',
            'phone_number',
            'email',
            'address',
            'description',
            'status'
        )
        order_by_field = 'sort'
        order_by = 'date'
