"""
URL patterns for the equipment app.
"""

from django.urls import path
from . import views

urlpatterns = [
    # Dashboard
    path('', views.dashboard, name='dashboard'),

    # Equipment items
    path('products/', views.ProductListView.as_view(), name='product-list'),
    path('products/search/', views.ItemSearchListView.as_view(), name='product-search'),
    path('products/new/', views.ProductCreateView.as_view(), name='product-create'),
    path('products/<slug:slug>/', views.ProductDetailView.as_view(), name='product-detail'),
    path('products/<slug:slug>/update/', views.ProductUpdateView.as_view(), name='product-update'),
    path('products/<slug:slug>/delete/', views.ProductDeleteView.as_view(), name='product-delete'),

    # Categories
    path('categories/', views.CategoryListView.as_view(), name='category-list'),
    path('categories/new/', views.CategoryCreateView.as_view(), name='category-create'),
    path('categories/<int:pk>/', views.CategoryDetailView.as_view(), name='category-detail'),
    path('categories/<int:pk>/update/', views.CategoryUpdateView.as_view(), name='category-update'),
    path('categories/<int:pk>/delete/', views.category_delete_view, name='category-delete'),

    # Technological solutions
    path('solutions/', views.SolutionTechnologiqueListView.as_view(), name='solutiontechnologique-list'),
    path('solutions/new/', views.SolutionTechnologiqueCreateView.as_view(), name='solutiontechnologique-create'),
    path('solutions/<int:pk>/', views.SolutionTechnologiqueDetailView.as_view(), name='solutiontechnologique-detail'),
    path('solutions/<int:pk>/update/', views.SolutionTechnologiqueUpdateView.as_view(), name='solutiontechnologique-update'),
    path('solutions/<int:pk>/delete/', views.solution_delete_view, name='solutiontechnologique-delete'),

    # Deliveries URLs removed as the Delivery model is no longer needed

    # AJAX
    path('get-items/', views.get_items_ajax_view, name='get-items-ajax'),

    # API for React components
    path('api/dashboard/stats/', views.dashboard_stats, name='api-dashboard-stats'),
    path('api/notifications/', views.notifications, name='api-notifications'),
    path('api/notifications/<int:notification_id>/read/', views.mark_notification_read, name='api-mark-notification-read'),
    path('api/audit-categories/', views.get_audit_categories, name='api-audit-categories'),

    # API for equipment creation
    path('api/categories/', views.get_categories_api, name='api-categories'),
    path('api/items/create/', views.create_equipment_api, name='api-create-equipment'),
]
