import React, { useState, useEffect } from 'react';

const App = () => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    // Update the time every minute
    const intervalId = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    // Clean up on unmount
    return () => clearInterval(intervalId);
  }, []);

  // Format the date and time
  const formattedDate = currentTime.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const formattedTime = currentTime.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  });

  return (
    <div className="react-app-container p-3 bg-light rounded shadow-sm mb-4">
      <div className="d-flex justify-content-between align-items-center">
        <div>
          <h5 className="mb-1">Welcome to Audit Platform</h5>
          <p className="text-muted mb-0 small">Your comprehensive audit management solution</p>
        </div>
        <div className="text-end">
          <p className="mb-0 fw-bold">{formattedDate}</p>
          <p className="mb-0 text-muted">{formattedTime}</p>
        </div>
      </div>
    </div>
  );
};

export default App;
