/* Importing Google Font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap');

/* Root variables */
:root {
  --dk-gray-100: #f3f4f6;
  --dk-gray-200: #e5e7eb;
  --dk-gray-300: #d1d5db;
  --dk-gray-400: #9ca3af;
  --dk-gray-500: #6b7280;
  --dk-gray-600: #4b5563;
  --dk-gray-700: #374151;
  --dk-gray-800: #1f2937;
  --dk-gray-900: #111827;
  --dk-dark-bg: #313348;
  --dk-darker-bg: #2a2b3d;
  --navbar-bg-color: #6f6486;
  --sidebar-bg-color: #252636;
  --sidebar-width: 250px;
  --sidebar-expanded-width: 230px;
  --sidebar-collapsed-width: 100px;
  --sidebar-header-bg: #212529;
  --nav-link-color: #adb5bd;
  --nav-link-active-bg: #495057;
  --dropdown-menu-bg: #495057;
  --dropdown-item-color: #adb5bd;
  --dropdown-item-active-bg: #6c757d;
  --sidebar-separator-bg: #4caf50;
  --logo-separator-height: 60px;
  --sidebar-item-height: 50px;
  --submenu-item-height: 45px;
}

/* General styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", sans-serif;
  background-color: #fff;
}

a {
  text-decoration: none;
}

/* Wrapper */
#wrapper {
  margin-left: var(--sidebar-width);
  transition: margin-left 0.3s ease-in-out;
}

#wrapper.fullwidth {
  margin-left: 0;
}

/* Sidebar */
.sidebar {
  font-size: 0.925rem;
  background-color: var(--sidebar-bg-color);
  width: var(--sidebar-width);
  transition: width 0.3s ease-in-out;
  z-index: 9999999;
}

.sidebar .close-aside {
  position: absolute;
  top: 7px;
  right: 7px;
  cursor: pointer;
  color: #eee;
}

.sidebar .sidebar-header {
  background-color: var(--sidebar-header-bg);
  border-bottom: 1px solid #2a2b3c;
}

.sidebar .sidebar-header h5 a,
.sidebar .sidebar-header p {
  color: var(--dk-gray-300);
  font-size: 0.825rem;
}

.sidebar .search .form-control ~ i {
  color: #2b2f3a;
  right: 40px;
  top: 22px;
}

.sidebar > ul > li {
  padding: 0.7rem 1.75rem;
}

.sidebar ul > li > a {
  color: var(--dk-gray-400);
  text-decoration: none;
}

/* Menu items */
#sidebar-container .list-group a {
  height: var(--sidebar-item-height);
  color: white;
}

/* Submenu items */
#sidebar-container .list-group li.list-group-item {
  background-color: var(--sidebar-bg-color);
}

#sidebar-container .list-group .sidebar-submenu a {
  height: var(--submenu-item-height);
  padding-left: 30px;
}

/* Separators */
.sidebar-separator-title,
.sidebar-separator,
.logo-separator {
  background-color: var(--sidebar-separator-bg);
}

.sidebar-separator-title {
  height: 35px;
}

.sidebar-separator {
  height: 25px;
}

.logo-separator {
  height: var(--logo-separator-height);
}

/* Navbar */
.navbar {
  border: none;
  background-color: var(--navbar-bg-color);
}

.navbar .dropdown-menu {
  right: auto;
  left: 0;
}

.navbar .navbar-nav > li > a {
  color: #eee;
  line-height: 55px;
  padding: 0 10px;
}

.navbar .navbar-brand {
  color: #fff;
  line-height: 55px;
  padding: 0;
}

.navbar .navbar-nav > li > a:focus,
.navbar .navbar-nav > li > a:hover {
  color: #eee;
}

.navbar .navbar-nav > .open > a,
.navbar .navbar-nav > .open > a:focus,
.navbar .navbar-nav > .open > a:hover {
  background-color: transparent;
  color: #fff;
}

.navbar .navbar-brand:focus,
.navbar .navbar-brand:hover {
  color: #fff;
}

.navbar > .container .navbar-brand,
.navbar > .container-fluid .navbar-brand {
  margin: 0;
}

@media (max-width: 767px) {
  .navbar > .container-fluid .navbar-brand {
    margin-left: 15px;
  }

  .navbar .navbar-nav > li > a {
    padding-left: 0;
  }

  .navbar-nav {
    margin: 0;
  }

  .navbar .navbar-collapse,
  .navbar .navbar-form {
    border: none;
  }
}

.navbar .navbar-nav > li > a {
  float: left;
}

.navbar .navbar-nav > li > a > span:not(.caret) {
  background-color: #e74c3c;
  border-radius: 50%;
  height: 25px;
  width: 25px;
  padding: 2px;
  font-size: 11px;
  position: relative;
  top: -10px;
  right: 5px;
}

.dropdown-menu > li > a {
  padding-top: 5px;
  padding-right: 5px;
}

.navbar .navbar-nav > li > a > i {
  font-size: 18px;
}
