# Django core imports
from django.urls import path
from django.conf import settings
from django.conf.urls.static import static

# Local app imports
from .views import (
    OngoingAuditListView,
    OngoingAuditDetailView,
    OngoingAuditCreateView,
    OngoingAuditUpdateView,
    OngoingAuditDeleteView,
    AuditRequestListView,
    AuditRequestDetailView,
    AuditRequestCreateView,
    AuditRequestDeleteView,
    launch_audit,
    reject_audit,

    AuditScriptListView,
    AuditScriptCreateView,
    AuditScriptUpdateView,
    AuditScriptDeleteView,

    export_audit_requests_to_excel,
    export_ongoing_audits_to_excel,

    # Manual compliance points views
    manual_compliance_list,
    manual_audit_compliance_list,
    add_compliance_point,
    edit_compliance_point,
    delete_compliance_point,
    add_compliance_evidence,
    delete_compliance_evidence,
    bulk_import_compliance_points,

    # Audit DB views
    audit_db_os_list,
    audit_db_points_list,
    audit_db_import_points,
    audit_db_add_preuve
)

# URL patterns
urlpatterns = [
    # Audit Script URLs
    path('audit-scripts/', AuditScriptListView.as_view(), name='audit_script_list'),
    path('audit-script/create/', AuditScriptCreateView.as_view(), name='audit_script_create'),
    path('audit-script/<int:pk>/update/', AuditScriptUpdateView.as_view(), name='audit_script_update'),
    path('audit-script/<int:pk>/delete/', AuditScriptDeleteView.as_view(), name='audit_script_delete'),

    # Ongoing Audit URLs
    path('ongoing-audits/', OngoingAuditListView.as_view(), name='ongoing_audit_list'),
    path(
         'ongoing-audit/<int:pk>/', OngoingAuditDetailView.as_view(),
         name='ongoing_audit_detail'
     ),
    path(
         'new-ongoing-audit/', OngoingAuditCreateView.as_view(),
         name='ongoing_audit_create'
     ),
    path(
         'ongoing-audit/<int:pk>/update/', OngoingAuditUpdateView.as_view(),
         name='ongoing_audit_update'
     ),
    path(
         'ongoing-audit/<int:pk>/delete/', OngoingAuditDeleteView.as_view(),
         name='ongoing_audit_delete'
     ),

    # Audit Request URLs
    path('audit-requests/', AuditRequestListView.as_view(), name='audit_request_list'),
    path('audit-request/<int:pk>/', AuditRequestDetailView.as_view(), name='audit_request_detail'),
    path('new-audit-request/', AuditRequestCreateView, name='audit_request_create'),
    path(
         'audit-request/<int:pk>/delete/', AuditRequestDeleteView.as_view(),
         name='audit_request_delete'
     ),
    path('audit-request/<int:pk>/launch/', launch_audit, name='launch_audit'),
    path('audit-request/<int:pk>/reject/', reject_audit, name='reject_audit'),

    # Audit requests and ongoing audits export
    path('audit-requests/export/', export_audit_requests_to_excel, name='audit_requests_export'),
    path('ongoing-audits/export/', export_ongoing_audits_to_excel,
         name='ongoing_audits_export'),

    # Manual compliance points URLs
    path('ongoing-audit/<int:audit_id>/compliance/', manual_compliance_list, name='manual_compliance_list'),
    path('ongoing-audit/manual/', manual_audit_compliance_list, name='manual_audit_compliance_list'),  # New URL for manual audit compliance
    path('ongoing-audit/<int:audit_id>/compliance/add/', add_compliance_point, name='add_compliance_point'),
    path('compliance-point/<int:point_id>/edit/', edit_compliance_point, name='edit_compliance_point'),
    path('compliance-point/<int:point_id>/delete/', delete_compliance_point, name='delete_compliance_point'),
    path('compliance-point/<int:point_id>/evidence/add/', add_compliance_evidence, name='add_compliance_evidence'),
    path('compliance-evidence/<int:evidence_id>/delete/', delete_compliance_evidence, name='delete_compliance_evidence'),
    path('ongoing-audit/<int:audit_id>/compliance/import/', bulk_import_compliance_points, name='bulk_import_compliance_points'),

    # API for control points is no longer needed

    # Audit DB URLs
    path('audit-db/os/', audit_db_os_list, name='audit_db_os_list'),
    path('audit-db/points/', audit_db_points_list, name='audit_db_points_list'),
    path('audit-db/points/<int:os_id>/', audit_db_points_list, name='audit_db_points_list_os'),
    path('audit-db/points/<int:os_id>/<int:version_id>/', audit_db_points_list, name='audit_db_points_list_version'),
    path('ongoing-audit/<int:audit_id>/import-points/<int:version_id>/', audit_db_import_points, name='audit_db_import_points'),
    path('ongoing-audit/<int:audit_id>/compliance/<int:point_id>/add-preuve/', audit_db_add_preuve, name='audit_db_add_preuve'),

    # Legacy URLs (for backward compatibility)
    # OngoingAudit legacy URLs (previously called "purchases")
    path('purchases/', OngoingAuditListView.as_view(), name='purchaseslist'),
    path('purchase/<int:pk>/', OngoingAuditDetailView.as_view(), name='purchase-detail'),
    path('new-purchase/', OngoingAuditCreateView.as_view(), name='purchase-create'),
    path('purchase/<int:pk>/update/', OngoingAuditUpdateView.as_view(), name='purchase-update'),
    path('purchase/<int:pk>/delete/', OngoingAuditDeleteView.as_view(), name='purchase-delete'),
    path('purchases/export/', export_ongoing_audits_to_excel, name='purchases-export'),

    # AuditRequest legacy URLs (previously called "sales")
    path('sales/', AuditRequestListView.as_view(), name='saleslist'),
    path('sale/<int:pk>/', AuditRequestDetailView.as_view(), name='sale-detail'),
    path('new-sale/', AuditRequestCreateView, name='sale-create'),
    path('sale/<int:pk>/delete/', AuditRequestDeleteView.as_view(), name='sale-delete'),
    path('sales/export/', export_audit_requests_to_excel, name='sales-export'),
]

# Static media files configuration for development
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
