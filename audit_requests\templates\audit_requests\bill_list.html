{% extends "equipment/base.html" %}
{% load render_table from django_tables2 %}
{% load querystring from django_tables2 %}
{% load static %}
{% load audit_request_filters %}

{% block title %}Audit Requests{% endblock title %}

{% block content %}
<div class="container p-5">
    <style>
        input[type="text"], select {
            box-sizing: border-box;
            border: 1px solid #ccc;
            border-radius: 4px;
            display: inline-block;
            padding: 6px 20px;
            margin: 8px 0;
        }
        button[type="submit"] {
            padding: 6px 20px;
            margin: 8px 0;
        }
        .table th, .table td {
            text-align: center;
            vertical-align: middle;
            padding: 8px;
        }
        .equipment-list {
            max-height: 120px;
            overflow-y: auto;
            padding: 5px;
        }
        .equipment-list .badge {
            display: inline-block;
            margin-right: 5px;
            margin-bottom: 5px;
        }
        /* Make text wrap properly */
        .table td {
            white-space: normal;
            word-wrap: break-word;
        }
        /* Action buttons */
        .action-btn-group {
            display: flex;
            justify-content: center;
            gap: 5px;
        }
        @media (max-width: 992px) {
            .action-btn-group {
                flex-direction: column;
            }
        }
    </style>

    <!-- Search Form (Commented Out) -->
    {% comment %}
    <form method="GET" class="container mb-3">
        {{ filter.form }}
        <button type="submit" class="btn btn-sm btn-success fa fa-search"> Search</button>
    </form>
    {% endcomment %}

    <div class="mb-3 d-flex justify-content-between align-items-center">
        <div>
            <a class="btn btn-primary" href="{% url 'bill_create' %}">
                <i class="fa-solid fa-plus me-2"></i> Add Audit Request
            </a>
        </div>
        <div>
            <a class="btn btn-success btn-sm" href="{% querystring '_export'='xlsx' %}">
                <i class="fa-solid fa-download me-2"></i> Export to Excel
            </a>
        </div>
    </div>

    <div class="m-2">
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead class="thead-light">
                    <tr>
                        <th scope="col"><a href="{% querystring table.prefixed_order_by_field=column.order_by_alias.next %}">ID <i class="fa-solid fa-sort"></i></a></th>
                        <th scope="col">Date <i class="fa-solid fa-sort"></i></th>
                        <th scope="col">Client Name <i class="fa-solid fa-sort"></i></th>
                        <th scope="col">Contact Number <i class="fa-solid fa-sort"></i></th>
                        <th scope="col">Email <i class="fa-solid fa-sort"></i></th>
                        <th scope="col">Description <i class="fa-solid fa-sort"></i></th>
                        <th scope="col">Equipment <i class="fa-solid fa-sort"></i></th>
                        <th scope="col">Status <i class="fa-solid fa-sort"></i></th>
                        <th scope="col">Action</th>
                    </tr>
                </thead>
                <tbody>
                    {% for audit_request in audit_requests %}
                    <tr>
                        <th scope="row">{{ audit_request.id }}</th>
                        <td>{{ audit_request.date|date:"Y-m-d" }}</td>
                        <td>{{ audit_request.institution_name }}</td>
                        <td>{{ audit_request.phone_number }}</td>
                        <td>{{ audit_request.email }}</td>
                        <td>
                            {% if audit_request.description %}
                                {{ audit_request.description|truncatechars:80 }}
                            {% else %}
                                <span class="text-muted">No description</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if "Selected Equipment:" in audit_request.description %}
                                {% with description=audit_request.description %}
                                    {% if "Selected Equipment:" in description %}
                                        {% with equipment_section=description|split:"Selected Equipment:"|last|split:"Category:"|first %}
                                            <div class="equipment-list">
                                                {% for item in equipment_section|split:"," %}
                                                    {% if item|strip %}
                                                        <span class="badge bg-success">{{ item|strip }}</span>
                                                    {% endif %}
                                                {% endfor %}
                                            </div>
                                        {% endwith %}
                                    {% endif %}
                                {% endwith %}
                            {% else %}
                                <span class="text-muted">No equipment specified</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if audit_request.status == 'pending' %}
                                <span class="badge bg-warning text-dark">Pending</span>
                            {% elif audit_request.status == 'approved' %}
                                <span class="badge bg-success">Approved</span>
                            {% elif audit_request.status == 'rejected' %}
                                <span class="badge bg-danger">Rejected</span>
                            {% elif audit_request.status == 'completed' %}
                                <span class="badge bg-info">Completed</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ audit_request.status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="action-btn-group">
                                <a class="btn btn-sm btn-info" href="{% url 'bill_update' audit_request.slug %}">
                                    <i class="fa-solid fa-pen"></i> Edit
                                </a>
                                <a class="btn btn-sm btn-danger" href="{% url 'bill_delete' audit_request.pk %}">
                                    <i class="fa-solid fa-trash"></i> Delete
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    <div class="mt-4">
        {% if is_paginated %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </span>
                </li>
                {% endif %}
                {% for i in paginator.page_range %}
                {% if page_obj.number == i %}
                <li class="page-item active" aria-current="page">
                    <span class="page-link">{{ i }} <span class="visually-hidden">(current)</span></span>
                </li>
                {% else %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ i }}">{{ i }}</a>
                </li>
                {% endif %}
                {% endfor %}
                {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </span>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>
{% endblock content %}
