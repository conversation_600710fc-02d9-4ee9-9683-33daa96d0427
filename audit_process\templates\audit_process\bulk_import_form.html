{% extends "equipment/base.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>{{ title }}</h2>
                <div>
                    <a href="{% url 'manual_compliance_list' audit_id=audit.id %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Compliance Points
                    </a>
                </div>
            </div>
            <p class="text-muted">
                Audit: <strong>{{ audit.item.name }}</strong> | 
                Status: <strong>{{ audit.get_audit_status_display }}</strong> |
                Auditor: <strong>{{ audit.auditor.name }}</strong>
            </p>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Import Compliance Points from CSV</h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="csv_file" class="form-label">CSV File</label>
                            <input type="file" name="csv_file" id="csv_file" class="form-control" required accept=".csv">
                            <div class="form-text">Select a CSV file with compliance points data.</div>
                        </div>
                        
                        <div class="d-flex justify-content-end mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-file-import me-2"></i> Import CSV
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">CSV Format Information</h5>
                </div>
                <div class="card-body">
                    <p class="mb-4">The CSV file must have the following columns:</p>
                    
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead>
                                <tr>
                                    <th>Column Name</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>idPoint</code></td>
                                    <td>Unique identifier for the compliance point</td>
                                </tr>
                                <tr>
                                    <td><code>descriptionPoint</code></td>
                                    <td>Description of what is being checked</td>
                                </tr>
                                <tr>
                                    <td><code>commandExecuted</code></td>
                                    <td>Command used to check compliance</td>
                                </tr>
                                <tr>
                                    <td><code>expectedOutput</code></td>
                                    <td>Expected result for compliance</td>
                                </tr>
                                <tr>
                                    <td><code>actualOutput</code></td>
                                    <td>Actual result found during audit</td>
                                </tr>
                                <tr>
                                    <td><code>statut</code></td>
                                    <td>Status: "conforme" or "non conforme"</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="alert alert-info mt-3">
                        <h6 class="alert-heading">Example CSV Format:</h6>
                        <pre class="mb-0"><code>"idPoint","descriptionPoint","commandExecuted","expectedOutput","actualOutput","statut"
"FW-001","Check firewall status","systemctl status firewalld","active (running)","active (running)","conforme"
"FW-002","Check default zone","firewall-cmd --get-default-zone","public","public","conforme"
"FW-003","Check open ports","firewall-cmd --list-ports","22/tcp","22/tcp 80/tcp","non conforme"</code></pre>
                    </div>
                    
                    <div class="alert alert-warning mt-3">
                        <h6 class="alert-heading">Important Notes:</h6>
                        <ul class="mb-0">
                            <li>The first row must contain the column headers</li>
                            <li>Existing points with the same ID will be updated</li>
                            <li>New points will be added</li>
                            <li>The status column should contain "conforme" or "non conforme"</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
