from django.contrib import admin
from .models import ClientProfile, ClientNotification

@admin.register(ClientProfile)
class ClientProfileAdmin(admin.ModelAdmin):
    """
    Admin configuration for ClientProfile model.
    """
    list_display = ('user', 'company_name', 'phone', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('user__username', 'user__email', 'company_name', 'phone')
    date_hierarchy = 'created_at'

@admin.register(ClientNotification)
class ClientNotificationAdmin(admin.ModelAdmin):
    """
    Admin configuration for ClientNotification model.
    """
    list_display = ('title', 'client', 'notification_type', 'is_read', 'created_at')
    list_filter = ('notification_type', 'is_read', 'created_at')
    search_fields = ('title', 'message', 'client__user__username')
    date_hierarchy = 'created_at'
