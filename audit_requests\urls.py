# Django core imports
from django.urls import path

# Local app imports
from .views import (
    AuditRequestListView,
    AuditRequestCreateView,
    AuditRequestUpdateView,
    AuditRequestDeleteView,
    get_audit_request_details
)

# URL patterns
urlpatterns = [
    # Audit Request URLs
    path(
        'requests/',
        AuditRequestListView.as_view(),
        name='audit_request_list'
    ),
    path('new-request/', AuditRequestCreateView.as_view(), name='audit_request_create'),
    path(
        'request/<slug:slug>/update/',
        AuditRequestUpdateView.as_view(),
        name='audit_request_update'
    ),
    path(
        'request/<int:pk>/delete/',
        AuditRequestDeleteView.as_view(),
        name='audit_request_delete'
    ),

    # Backward compatibility URLs
    path(
        'bills/',
        AuditRequestListView.as_view(),
        name='bill_list'
    ),
    path('new-bill/', AuditRequestCreateView.as_view(), name='bill_create'),
    path(
        'bill/<slug:slug>/update/',
        AuditRequestUpdateView.as_view(),
        name='bill_update'
    ),
    path(
        'bill/<int:pk>/delete/',
        AuditRequestDeleteView.as_view(),
        name='bill_delete'
    ),

    # API endpoint to get audit request details
    path('api/get-request/<int:pk>/', get_audit_request_details, name='get_audit_request_details'),
]
