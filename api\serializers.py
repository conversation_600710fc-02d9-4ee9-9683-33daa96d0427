from rest_framework import serializers
from django.contrib.auth.models import User
from accounts.models import Profile, Auditor, Client
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('id', 'username', 'email', 'first_name', 'last_name')


class ProfileSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = Profile
        fields = ('id', 'user', 'telephone', 'email', 'role', 'status')


class AuditorSerializer(serializers.ModelSerializer):
    class Meta:
        model = Auditor
        fields = ('id', 'name', 'phone_number', 'address', 'email')


class ClientSerializer(serializers.ModelSerializer):
    class Meta:
        model = Client
        fields = ('id', 'first_name', 'last_name', 'address', 'email', 'phone', 'loyalty_points')


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    """
    Custom token serializer that adds user role and other info to the token
    """
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        
        # Add custom claims
        token['username'] = user.username
        token['email'] = user.email
        
        # Add user role if profile exists
        try:
            profile = Profile.objects.get(user=user)
            token['role'] = profile.role
            token['status'] = profile.status
        except Profile.DoesNotExist:
            token['role'] = None
            token['status'] = None
            
        return token
        
    def validate(self, attrs):
        data = super().validate(attrs)
        
        # Add extra response data
        data['user_id'] = self.user.id
        data['username'] = self.user.username
        data['email'] = self.user.email
        
        # Add user role if profile exists
        try:
            profile = Profile.objects.get(user=self.user)
            data['role'] = profile.role
            data['status'] = profile.status
        except Profile.DoesNotExist:
            data['role'] = None
            data['status'] = None
            
        return data


class RegisterSerializer(serializers.ModelSerializer):
    """
    Serializer for user registration
    """
    email = serializers.EmailField(required=True)
    password = serializers.CharField(write_only=True, required=True)
    password2 = serializers.CharField(write_only=True, required=True)
    role = serializers.ChoiceField(choices=['AU', 'CL', 'AD'], required=True)
    
    class Meta:
        model = User
        fields = ('username', 'email', 'password', 'password2', 'first_name', 'last_name', 'role')
        
    def validate(self, attrs):
        if attrs['password'] != attrs['password2']:
            raise serializers.ValidationError({"password": "Password fields didn't match."})
        return attrs
        
    def create(self, validated_data):
        role = validated_data.pop('role')
        password2 = validated_data.pop('password2')
        
        user = User.objects.create(
            username=validated_data['username'],
            email=validated_data['email'],
            first_name=validated_data.get('first_name', ''),
            last_name=validated_data.get('last_name', '')
        )
        
        user.set_password(validated_data['password'])
        user.save()
        
        # Create profile
        Profile.objects.create(
            user=user,
            email=validated_data['email'],
            role=role,
            status='A'  # Active by default
        )
        
        return user
