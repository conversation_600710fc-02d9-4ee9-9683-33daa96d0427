# Generated by Django 5.1 on 2025-04-25 13:08

import django.db.models.deletion
import django_extensions.db.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("equipment", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="AuditReport",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "slug",
                    django_extensions.db.fields.AutoSlugField(
                        blank=True, editable=False, populate_from="date", unique=True
                    ),
                ),
                (
                    "date",
                    models.DateTimeField(auto_now=True, verbose_name="Report Date"),
                ),
                (
                    "client_name",
                    models.CharField(max_length=30, verbose_name="Client Name"),
                ),
                (
                    "contact_number",
                    models.CharField(max_length=13, verbose_name="Contact Number"),
                ),
                (
                    "audit_result",
                    models.BooleanField(
                        default=False, verbose_name="Audit Result (Pass/Fail)"
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, null=True, verbose_name="Audit Description"
                    ),
                ),
                (
                    "findings",
                    models.TextField(
                        blank=True, null=True, verbose_name="Audit Findings"
                    ),
                ),
                ("fee", models.FloatField(default=0.0, verbose_name="Audit Fee")),
                (
                    "additional_fees",
                    models.FloatField(default=0.0, verbose_name="Additional Fees"),
                ),
                (
                    "total_fee",
                    models.FloatField(
                        default=0.0, editable=False, verbose_name="Total Fee"
                    ),
                ),
                (
                    "equipment",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="equipment.item",
                        verbose_name="Audited Equipment",
                    ),
                ),
            ],
            options={
                "verbose_name": "Audit Report",
                "verbose_name_plural": "Audit Reports",
                "ordering": ["-date"],
            },
        ),
    ]
