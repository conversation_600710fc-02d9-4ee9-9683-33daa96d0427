{% extends 'client_portal/base.html' %}

{% block title %}{{ audit_report.report_name }}{% endblock %}
{% block page_title %}{{ audit_report.report_name }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>{{ audit_report.report_name }}</h2>
                <div>
                    <a href="{% url 'client_audit_report_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back to List
                    </a>
                    <a href="javascript:window.print();" class="btn btn-primary ms-2">
                        <i class="fas fa-print me-2"></i> Print Report
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Report Information</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Report ID:</span>
                            <span class="fw-bold">{{ audit_report.id }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Date:</span>
                            <span>{{ audit_report.date|date:"F d, Y" }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Equipment:</span>
                            <span>{{ audit_report.item.name }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Quantity:</span>
                            <span>{{ audit_report.quantity }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Result:</span>
                            <span>
                                {% if audit_report.result %}
                                <span class="badge bg-success">Pass</span>
                                {% else %}
                                <span class="badge bg-danger">Fail</span>
                                {% endif %}
                            </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Audit Description</h5>
                </div>
                <div class="card-body">
                    {% if audit_report.description %}
                    <div class="p-3 bg-light rounded">
                        {{ audit_report.description|linebreaks }}
                    </div>
                    {% else %}
                    <p class="text-muted">No description provided.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Proof Images</h5>
                </div>
                <div class="card-body">
                    {% if audit_report.proof_images.all %}
                    <div class="row">
                        {% for proof in audit_report.proof_images.all %}
                        <div class="col-md-4 mb-4">
                            <div class="card">
                                <img src="{{ proof.image.url }}" class="card-img-top" alt="Proof Image">
                                {% if proof.caption %}
                                <div class="card-body">
                                    <p class="card-text">{{ proof.caption }}</p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted">No proof images available for this audit report.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
