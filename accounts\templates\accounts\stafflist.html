{% extends "equipment/base.html" %}
{% load static %}
{% load render_table from django_tables2 %}
{% load querystring from django_tables2 %}

{% block title %}User Profiles{% endblock title %}

{% block stylesheets %}
<style>
    .avatar {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 16px;
    }

    .bg-primary {
        background-color: #023047 !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container p-5">
    <style>
        .table th, .table td {
            text-align: center;
        }
      </style>
    <div class="d-flex justify-content-between mb-3">
        <a class="btn btn-success btn-sm" href="{% url 'profile-create' %}">
            <i class="fa-solid fa-user-plus me-2"></i>Add User
        </a>
        <a class="btn btn-success btn-sm" href="{% querystring '_export'='xlsx' %}">
            <i class="fa-solid fa-download me-2"></i>Export to Excel
        </a>
    </div>
    <table class="table table-sm table-bordered table-striped">
        <thead class="thead-light">
            <tr>
                <th scope="col">Id <i class="fa-solid fa-sort"></i></th>
                <th scope="col">Profile Image</th>
                <th scope="col">Username <i class="fa-solid fa-sort"></i></th>
                <th scope="col">Phone Number</th>
                <th scope="col">Status <i class="fa-solid fa-sort"></i></th>
                <th scope="col">Role <i class="fa-solid fa-sort"></i></th>
                {% if user.profile.role == 'AD' or user.profile.role == 'CL' %}
                <th scope="col">Action</th>
                {% endif %}
            </tr>
        </thead>
        <tbody>
            {% for profile in profiles %}
            <tr>
                <th scope="row">{{ profile.id }}</th>
                <td>
                    {% if profile.profile_picture %}
                        <img alt="Profile Image" src="{{ profile.profile_picture.url }}?v={% now 'U' %}" class="avatar avatar-sm rounded-circle" onerror="this.src='{% static 'assets/img/team/profile-picture-3.jpg' %}';">
                    {% else %}
                        <img alt="Profile Image" src="{% static 'assets/img/profile_pics/default.jpg' %}" class="avatar avatar-sm rounded-circle">
                    {% endif %}
                </td>
                <td>{{ profile.user.username }}</td>
                <td>{% if profile.telephone and profile.telephone != 'None' %}{{ profile.telephone }}{% else %}Not set{% endif %}</td>
                <td>
                    {% if profile.status == 'A' %}
                    <span class="badge bg-success text-light">Active</span>
                    {% elif profile.status == 'OL' %}
                    <span class="badge bg-warning text-dark">On Leave</span>
                    {% else %}
                    <span class="badge bg-danger text-light">Inactive</span>
                    {% endif %}
                </td>
                <td>
                    {% if profile.role == 'AD' %}
                    <span class="badge bg-primary">Admin</span>
                    {% elif profile.role == 'CL' %}
                    <span class="badge bg-info">Client</span>
                    {% elif profile.role == 'AU' %}
                    <span class="badge bg-secondary">Auditor</span>
                    {% else %}
                    <span class="badge bg-dark">Unknown</span>
                    {% endif %}
                </td>
                <td>
                    {% if user.profile.role == 'AD' or user.profile.role == 'CL' %}
                    <a class="text-info" href="{% url 'profile-update' profile.id %}">
                        <i class="fa-solid fa-pen"></i>
                    </a>
                    {% endif %}
                    {% if user.profile.role == 'AD' %}
                    <a class="text-danger float-end" href="{% url 'profile-delete' profile.id %}">
                        <i class="fa-solid fa-trash"></i>
                    </a>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
