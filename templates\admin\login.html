{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrastyle %}
{{ block.super }}
<link href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600;700;800&display=swap" rel="stylesheet">
<style>
  :root {
    --primary-color: #023047;
    --secondary-color: #219ebc;
    --accent-color: #ffb703;
    --text-color: #fff;
    --error-color: #e63946;
  }

  body {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    font-family: 'Nunito', sans-serif;
    margin: 0;
    padding: 0;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
  }

  /* Add a subtle pattern overlay */
  body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.4;
    z-index: -1;
  }

  #header, #container {
    padding: 0;
    margin: 0;
    border: none;
    box-shadow: none;
    background: transparent;
    width: 100%;
  }

  #content {
    padding: 0;
    margin: 0;
  }

  .login {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
    max-width: 450px;
    width: 90%;
    margin: 0 auto;
    padding: 40px;
    box-sizing: border-box;
    animation: fadeIn 0.8s ease-out, slideUp 0.8s ease-out;
    position: relative;
    z-index: 1;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideUp {
    from { transform: translateY(30px); }
    to { transform: translateY(0); }
  }

  .login h1 {
    display: none;
  }

  .login .form-row {
    padding: 12px 0;
  }

  .login label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    font-size: 16px;
  }

  .login input[type="text"],
  .login input[type="password"] {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 16px;
    transition: border-color 0.3s;
    box-sizing: border-box;
  }

  .login input[type="text"]:focus,
  .login input[type="password"]:focus {
    border-color: var(--secondary-color);
    outline: none;
  }

  .login input[type="submit"] {
    width: 100%;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 14px;
    font-size: 18px;
    font-weight: 700;
    cursor: pointer;
    margin-top: 20px;
    transition: all 0.3s;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .login input[type="submit"]:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .errornote {
    background-color: var(--error-color);
    color: white;
    padding: 12px 15px;
    border-radius: 6px;
    margin-bottom: 25px;
    font-weight: 600;
  }

  .login .submit-row {
    padding: 0;
    text-align: center;
    margin-top: 10px;
  }

  .login .password-reset-link {
    text-align: center;
    margin-top: 20px;
  }

  .login .password-reset-link a {
    color: var(--secondary-color);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s;
  }

  .login .password-reset-link a:hover {
    color: var(--primary-color);
  }

  /* Logo styling */
  .login-logo {
    text-align: center;
    margin-bottom: 25px;
    animation: pulse 2s infinite ease-in-out;
  }

  @keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
  }

  .login-logo img {
    max-width: 90px;
    height: auto;
    filter: brightness(0) invert(1);
    transition: transform 0.3s;
  }

  .login-logo:hover img {
    transform: rotate(5deg);
  }

  /* Custom title */
  .login-title {
    text-align: center;
    margin-bottom: 35px;
    color: white;
    animation: fadeIn 1s ease-out;
  }

  .login-title h2 {
    color: white;
    font-size: 36px;
    font-weight: 800;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 1px;
  }

  .login-title p {
    color: rgba(255, 255, 255, 0.9);
    margin-top: 10px;
    font-size: 18px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
  }

  /* Responsive adjustments */
  @media (max-width: 576px) {
    .login {
      padding: 30px 20px;
    }

    .login-title h2 {
      font-size: 28px;
    }

    .login-title p {
      font-size: 16px;
    }
  }
</style>
{% endblock %}

{% block bodyclass %}login{% endblock %}

{% block usertools %}{% endblock %}

{% block nav-global %}{% endblock %}

{% block nav-sidebar %}{% endblock %}

{% block content_title %}{% endblock %}

{% block breadcrumbs %}{% endblock %}

{% block content %}
<div class="login-logo">
  <img src="{% static 'assets/img/brand/light.svg' %}" alt="Logo">
</div>
<div class="login-title">
  <h2>Audit Management System</h2>
  <p>Admin Portal</p>
</div>

{% if form.errors and not form.non_field_errors %}
<p class="errornote">
  {% if form.errors.items|length == 1 %}{% translate "Please correct the error below." %}{% else %}{% translate "Please correct the errors below." %}{% endif %}
</p>
{% endif %}

{% if form.non_field_errors %}
{% for error in form.non_field_errors %}
<p class="errornote">
  {{ error }}
</p>
{% endfor %}
{% endif %}

<div id="content-main">
  {% if user.is_authenticated %}
  <p class="errornote">
    {% blocktranslate trimmed %}
    You are authenticated as {{ username }}, but are not authorized to
    access this page. Would you like to login to a different account?
    {% endblocktranslate %}
  </p>
  {% endif %}

  <form action="{{ app_path }}" method="post" id="login-form">{% csrf_token %}
    <div class="form-row">
      {{ form.username.errors }}
      {{ form.username.label_tag }} {{ form.username }}
    </div>
    <div class="form-row">
      {{ form.password.errors }}
      {{ form.password.label_tag }} {{ form.password }}
      <input type="hidden" name="next" value="{{ next }}">
    </div>
    {% url 'admin_password_reset' as password_reset_url %}
    {% if password_reset_url %}
    <div class="password-reset-link">
      <a href="{{ password_reset_url }}">{% translate 'Forgotten your password or username?' %}</a>
    </div>
    {% endif %}
    <div class="submit-row">
      <input type="submit" value="{% translate 'Log in' %}">
    </div>
  </form>
</div>
{% endblock %}
