# Django core imports
from django.conf import settings
from django.conf.urls.static import static
from django.urls import path
from django.contrib.auth import views as auth_views

# Local app imports
from accounts import views as user_views
from .views import (
    ProfileListView,
    ProfileCreateView,
    ProfileUpdateView,
    ProfileDeleteView,
    ClientListView,
    ClientCreateView,
    ClientUpdateView,
    ClientDeleteView,
    get_clients,
    AuditorListView,
    AuditorCreateView,
    AuditorUpdateView,
    AuditorDeleteView,
    custom_login,
    debug_roles
)

urlpatterns = [
    # User authentication URLs
    path('register/', user_views.register, name='user-register'),
    path('login/', custom_login, name='user-login'),
    path('jwt-login/', user_views.jwt_login_view, name='jwt-login'),
    path('profile/', user_views.profile, name='user-profile'),
    path('profile/update/', user_views.profile_update,
         name='user-profile-update'),
    path('logout/', auth_views.LogoutView.as_view(
        next_page='user-login'), name='user-logout'),

    # Profile URLs
    path('profiles/', ProfileListView.as_view(), name='profile_list'),
    path('new-profile/', ProfileCreateView.as_view(), name='profile-create'),
    path('profile/<int:pk>/update/', ProfileUpdateView.as_view(),
         name='profile-update'),
    path('profile/<int:pk>/delete/', ProfileDeleteView.as_view(),
         name='profile-delete'),

    # Client URLs
    path('clients/', ClientListView.as_view(), name='client_list'),
    path('clients/create/', ClientCreateView.as_view(),
         name='client_create'),
    path('clients/<int:pk>/update/', ClientUpdateView.as_view(),
         name='client_update'),
    path('clients/<int:pk>/delete/', ClientDeleteView.as_view(),
         name='client_delete'),
    path('get_clients/', get_clients, name='get_clients'),

    # Auditor URLs
    path('auditors/', AuditorListView.as_view(), name='auditor-list'),
    path('auditors/new/', AuditorCreateView.as_view(), name='auditor-create'),
    path('auditors/<int:pk>/update/', AuditorUpdateView.as_view(),
         name='auditor-update'),
    path('auditors/<int:pk>/delete/', AuditorDeleteView.as_view(),
         name='auditor-delete'),

    # Debug URLs
    path('debug/roles/', debug_roles, name='debug_roles'),
]

urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
