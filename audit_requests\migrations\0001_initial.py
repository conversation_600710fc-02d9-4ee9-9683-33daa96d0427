# Generated by Django 5.1 on 2025-04-25 13:08

import autoslug.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AuditRequest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "slug",
                    autoslug.fields.AutoSlugField(
                        editable=False, populate_from="date", unique=True
                    ),
                ),
                (
                    "date",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Request Date"
                    ),
                ),
                (
                    "client_name",
                    models.CharField(max_length=100, verbose_name="Client Name"),
                ),
                (
                    "phone_number",
                    models.PositiveIntegerField(
                        blank=True, help_text="Phone number of the client", null=True
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True,
                        help_text="Email address of the client",
                        max_length=254,
                        null=True,
                    ),
                ),
                (
                    "address",
                    models.Char<PERSON>ield(
                        blank=True,
                        help_text="Address of the client",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Description of the audit request",
                        null=True,
                    ),
                ),
                (
                    "equipment_details",
                    models.TextField(
                        help_text="Details of the equipment to be audited"
                    ),
                ),
                (
                    "estimated_fee",
                    models.FloatField(
                        help_text="Estimated fee for the audit",
                        verbose_name="Estimated Audit Fee",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("completed", "Completed"),
                        ],
                        default="pending",
                        help_text="Current status of the audit request",
                        max_length=20,
                        verbose_name="Request Status",
                    ),
                ),
            ],
            options={
                "verbose_name": "Audit Request",
                "verbose_name_plural": "Audit Requests",
                "ordering": ["-date"],
            },
        ),
    ]
