{% extends "equipment/base.html" %}
{% load static %}

{% block title %}Delete Evidence{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>Delete Evidence</h2>
                <div>
                    <a href="{% url 'manual_compliance_list' audit_id=audit.id %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Compliance Points
                    </a>
                </div>
            </div>
            <p class="text-muted">
                Audit: <strong>{{ audit.item.name }}</strong> | 
                Compliance Point: <strong>{{ compliance_point.point_id }}</strong> - {{ compliance_point.description|truncatechars:50 }}
            </p>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">Confirm Deletion</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h5 class="alert-heading">Warning!</h5>
                        <p>You are about to delete the evidence file <strong>{{ evidence.file.name }}</strong>. This action cannot be undone.</p>
                    </div>
                    
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Evidence Details</h6>
                        </div>
                        <div class="card-body">
                            <dl class="row mb-0">
                                <dt class="col-sm-3">File:</dt>
                                <dd class="col-sm-9">
                                    <a href="{{ evidence.file.url }}" target="_blank">
                                        <i class="fas fa-file me-2"></i>
                                        {{ evidence.file.name }}
                                    </a>
                                </dd>
                                
                                <dt class="col-sm-3">Description:</dt>
                                <dd class="col-sm-9">
                                    {% if evidence.description %}
                                    {{ evidence.description }}
                                    {% else %}
                                    <span class="text-muted">No description</span>
                                    {% endif %}
                                </dd>
                                
                                <dt class="col-sm-3">Date Added:</dt>
                                <dd class="col-sm-9">
                                    {{ evidence.date_added|date:"F j, Y, g:i a" }}
                                </dd>
                                
                                <dt class="col-sm-3">For Point:</dt>
                                <dd class="col-sm-9">
                                    {{ compliance_point.point_id }} - {{ compliance_point.description|truncatechars:50 }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                    
                    <form method="POST">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'manual_compliance_list' audit_id=audit.id %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i> Delete Evidence
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
