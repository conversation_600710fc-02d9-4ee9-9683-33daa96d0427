version: '3.8'

services:
  # Django web application
  web:
    build: .
    command: >
      bash -c "python manage.py migrate &&
               python manage.py runserver 0.0.0.0:8000"
    volumes:
      - .:/app
    ports:
      - "8000:8000"
    environment:
      - IN_DOCKER=True
    depends_on:
      - db
    restart: always

  # PostgreSQL database
  db:
    image: postgres:15
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_PASSWORD=Livealone123@
      - POSTGRES_USER=audituser
      - POSTGRES_DB=auditconfig
    ports:
      - "5433:5432"
    restart: always

  # pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
    ports:
      - "5050:80"
    depends_on:
      - db
    restart: always

volumes:
  postgres_data:
