{% extends 'client_portal/volt_base.html' %}

{% block title %}Audit Report: {{ audit_report.report_name }}{% endblock %}
{% block page_title %}Audit Report: {{ audit_report.report_name }}{% endblock %}

{% block content %}
<div class="py-4">
    <div class="d-flex justify-content-between w-100 flex-wrap mb-4">
        <div class="mb-3 mb-lg-0">
            <h1 class="h4">Audit Report #{{ audit_report.id }}</h1>
            <p class="mb-0">Detailed information about your audit report.</p>
        </div>
        <div>
            <a href="{% url 'client_audit_report_list' %}" class="btn btn-sm btn-gray-200 d-inline-flex align-items-center">
                <i class="fas fa-arrow-left me-2"></i> Back to List
            </a>
            <a href="javascript:window.print()" class="btn btn-sm btn-primary d-inline-flex align-items-center ms-2">
                <i class="fas fa-print me-2"></i> Print Report
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-12 col-xl-4 mb-4">
            <div class="card border-0 shadow">
                <div class="card-header border-bottom d-flex align-items-center justify-content-between">
                    <h2 class="fs-5 fw-bold mb-0">Report Information</h2>
                    <div>
                        {% if audit_report.result %}
                        <span class="badge bg-success">Pass</span>
                        {% else %}
                        <span class="badge bg-danger">Fail</span>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0 pb-0">
                            <span class="fw-bold">Report ID:</span>
                            <span>{{ audit_report.id }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0 pb-0">
                            <span class="fw-bold">Report Name:</span>
                            <span>{{ audit_report.report_name }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0 pb-0">
                            <span class="fw-bold">Date:</span>
                            <span>{{ audit_report.date|date:"F d, Y" }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0 pb-0">
                            <span class="fw-bold">Customer:</span>
                            <span>{{ audit_report.customer_name }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0 pb-0">
                            <span class="fw-bold">Vendor:</span>
                            <span>{{ audit_report.vendor_name }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                            <span class="fw-bold">Result:</span>
                            <span class="fw-extrabold {% if audit_report.result %}text-success{% else %}text-danger{% endif %}">
                                {% if audit_report.result %}Pass{% else %}Fail{% endif %}
                            </span>
                        </li>
                    </ul>
                </div>
            </div>
            
            {% if audit_report.related_audit_request %}
            <div class="card border-0 shadow mt-4">
                <div class="card-header border-bottom">
                    <h2 class="fs-5 fw-bold mb-0">Related Audit Request</h2>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0 pb-0">
                            <span class="fw-bold">Request ID:</span>
                            <span>{{ audit_report.related_audit_request.id }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0 pb-0">
                            <span class="fw-bold">Date:</span>
                            <span>{{ audit_report.related_audit_request.date_added|date:"F d, Y" }}</span>
                        </li>
                    </ul>
                    <div class="mt-3">
                        <a href="{% url 'client_audit_request_detail' audit_report.related_audit_request.id %}" class="btn btn-sm btn-tertiary">
                            <i class="fas fa-eye me-2"></i> View Request
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
        
        <div class="col-12 col-xl-8 mb-4">
            <div class="card border-0 shadow">
                <div class="card-header border-bottom d-flex align-items-center justify-content-between">
                    <h2 class="fs-5 fw-bold mb-0">Audit Description</h2>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        {{ audit_report.description|linebreaks }}
                    </div>
                </div>
            </div>
            
            {% if audit_report.preuves.all %}
            <div class="card border-0 shadow mt-4">
                <div class="card-header border-bottom d-flex align-items-center justify-content-between">
                    <h2 class="fs-5 fw-bold mb-0">Proof Images</h2>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for preuve in audit_report.preuves.all %}
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card border-0 shadow-sm">
                                <img src="{{ preuve.image.url }}" class="card-img-top" alt="Proof Image">
                                <div class="card-body">
                                    <h5 class="card-title h6">{{ preuve.title }}</h5>
                                    <p class="card-text small">{{ preuve.description }}</p>
                                    <a href="{{ preuve.image.url }}" target="_blank" class="btn btn-sm btn-tertiary">
                                        <i class="fas fa-search-plus me-2"></i> View Full Size
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
