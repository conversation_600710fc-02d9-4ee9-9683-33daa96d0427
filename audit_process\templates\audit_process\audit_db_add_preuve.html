{% extends "equipment/base.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>{{ title }}</h2>
                <div>
                    <a href="{% url 'manual_compliance_list' audit_id=audit.id %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Compliance Points
                    </a>
                </div>
            </div>
            <p class="text-muted">
                Audit: <strong>{{ audit.item.name }}</strong> |
                Compliance Point: <strong>{{ compliance_point.point_id }}</strong> - {{ compliance_point.description|truncatechars:50 }}
            </p>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Add Evidence</h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        {% csrf_token %}

                        <div class="mb-3">
                            <label for="actual_result" class="form-label">Actual Result</label>
                            <textarea name="actual_result" id="actual_result" class="form-control" rows="3">{{ preuve.actual_result|default:'' }}</textarea>
                            <div class="form-text">Enter the actual result found during the audit.</div>
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select name="status" id="status" class="form-control">
                                <option value="conforme" {% if preuve.status == 'conforme' %}selected{% endif %}>Compliant</option>
                                <option value="non_conforme" {% if preuve.status == 'non_conforme' %}selected{% endif %}>Non-Compliant</option>
                                <option value="not_checked" {% if preuve.status == 'not_checked' %}selected{% endif %}>Not Checked</option>
                            </select>
                            <div class="form-text">Select the compliance status for this point.</div>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea name="notes" id="notes" class="form-control" rows="3">{{ preuve.notes|default:'' }}</textarea>
                            <div class="form-text">Add any additional notes or observations.</div>
                        </div>

                        <div class="mb-3">
                            <label for="evidence_file" class="form-label">Screenshot Evidence</label>
                            <input type="file" name="evidence_file" id="evidence_file" class="form-control" accept="image/*">
                            <div class="form-text">Upload a screenshot as evidence (PNG, JPG, etc.).</div>

                            {% if preuve.file_path %}
                            <div class="mt-3">
                                <p class="mb-1"><strong>Current Screenshot:</strong></p>
                                <div class="card">
                                    <div class="card-body p-2">
                                        <img src="{{ MEDIA_URL }}{{ preuve.file_path }}" class="img-fluid img-thumbnail" style="max-height: 200px;" alt="Evidence Screenshot">
                                        <div class="mt-2">
                                            <a href="{{ MEDIA_URL }}{{ preuve.file_path }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-external-link-alt me-1"></i> View Full Size
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="preview_image" checked>
                                <label class="form-check-label" for="preview_image">
                                    Preview image before upload
                                </label>
                            </div>
                            <div id="image_preview_container" class="mt-2" style="display: none;">
                                <div class="card">
                                    <div class="card-body p-2">
                                        <img id="image_preview" src="#" class="img-fluid img-thumbnail" style="max-height: 200px;" alt="Image Preview">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> Save Evidence
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Control Point Information</h5>
                </div>
                <div class="card-body">
                    <dl class="row mb-0">
                        <dt class="col-sm-4">ID:</dt>
                        <dd class="col-sm-8">{{ control_point.idpoint }}</dd>

                        <dt class="col-sm-4">Description:</dt>
                        <dd class="col-sm-8">{{ control_point.descriptionpoint }}</dd>

                        <dt class="col-sm-4">Command:</dt>
                        <dd class="col-sm-8">
                            {% if control_point.commandeaudit %}
                            <code>{{ control_point.commandeaudit }}</code>
                            {% else %}
                            <span class="text-muted">N/A</span>
                            {% endif %}
                        </dd>

                        <dt class="col-sm-4">Expected:</dt>
                        <dd class="col-sm-8">
                            {% if control_point.expectedoutput %}
                            {{ control_point.expectedoutput }}
                            {% else %}
                            <span class="text-muted">N/A</span>
                            {% endif %}
                        </dd>
                    </dl>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Evidence Information</h5>
                </div>
                <div class="card-body">
                    <p class="mb-4">Evidence provides proof of compliance or non-compliance for audit points. You can upload screenshots, logs, configuration files, or any other relevant documentation.</p>

                    <div class="d-flex mb-3">
                        <div class="icon-shape icon-shape-sm icon-shape-primary me-3">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">Status Types</h5>
                            <p class="text-muted small mb-0">
                                <span class="badge bg-success">Compliant</span> - Meets requirements<br>
                                <span class="badge bg-danger">Non-Compliant</span> - Does not meet requirements<br>
                                <span class="badge bg-secondary">Not Checked</span> - Not yet verified
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const fileInput = document.getElementById('evidence_file');
        const previewCheckbox = document.getElementById('preview_image');
        const previewContainer = document.getElementById('image_preview_container');
        const imagePreview = document.getElementById('image_preview');

        // Function to handle file selection
        function handleFileSelect(event) {
            if (!previewCheckbox.checked) {
                previewContainer.style.display = 'none';
                return;
            }

            const file = event.target.files[0];
            if (!file) {
                previewContainer.style.display = 'none';
                return;
            }

            // Check if file is an image
            if (!file.type.match('image.*')) {
                previewContainer.style.display = 'none';
                return;
            }

            // Create a FileReader to read the image
            const reader = new FileReader();
            reader.onload = function(e) {
                imagePreview.src = e.target.result;
                previewContainer.style.display = 'block';
            };
            reader.readAsDataURL(file);
        }

        // Add event listeners
        if (fileInput) {
            fileInput.addEventListener('change', handleFileSelect);
        }

        if (previewCheckbox) {
            previewCheckbox.addEventListener('change', function() {
                if (this.checked && fileInput.files.length > 0) {
                    handleFileSelect({ target: fileInput });
                } else {
                    previewContainer.style.display = 'none';
                }
            });
        }
    });
</script>
{% endblock %}

{% endblock content %}