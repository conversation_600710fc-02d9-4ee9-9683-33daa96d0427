{% extends "equipment/base.html" %}{% load static %}{% load render_table from django_tables2 %}{% load querystring from django_tables2 %}{% block title %}Ongoing Audits{%endblock title%}

{% block content %}
<!-- Header Section -->
<div class="container my-4">
    <div class="card shadow-sm rounded p-3">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h4 class="display-6 mb-0 text-success">Ongoing Audits</h4>
            </div>
            <div class="col-md-6 d-flex justify-content-end gap-2">
                <a class="btn btn-success btn-sm rounded-pill shadow-sm" href="{% url 'ongoing_audit_create' %}">
                    <i class="fa-solid fa-plus"></i> Add Ongoing Audit
                </a>
                <a class="btn btn-success btn-sm rounded-pill shadow-sm" href="{% url 'ongoing_audits_export' %}">
                    <i class="fa-solid fa-download"></i> Export to Excel
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <style>
      .table th, .table td {
          text-align: center;
          vertical-align: middle;
          padding: 8px;
      }
      /* Make text wrap properly */
      .table td {
          white-space: normal;
          word-wrap: break-word;
      }
      /* Action buttons */
      .action-btn-group {
          display: flex;
          justify-content: center;
          gap: 5px;
      }
      @media (max-width: 992px) {
          .action-btn-group {
              flex-direction: column;
          }
      }
    </style>
    <div class="table-responsive">
        <table class="table table-striped table-bordered">
            <thead class="thead-light">
                <tr>
                    <th scope="col">ID <i class="fa-solid fa-sort"></i></th>
                    <th scope="col">Equipment <i class="fa-solid fa-sort"></i></th>
                    <th scope="col">Audit Status <i class="fa-solid fa-sort"></i></th>
                    <th scope="col">Completion Date <i class="fa-solid fa-sort"></i></th>
                    <th scope="col">Auditor <i class="fa-solid fa-sort"></i></th>
                    <th scope="col">Script File</th>
                    <th scope="col">Action</th>
                </tr>
            </thead>
            <tbody>
                {% for audit in ongoing_audits %}
                <tr>
                    <th scope="row">{{ audit.id }}</th>
                    <td>{{ audit.item.name }}</td>
                    <td>
                        {% if audit.audit_status == 'P' %}
                            <span class="badge badge-pill bg-soft-warning text-warning">
                                In Progress
                            </span>
                        {% else %}
                            <span class="badge badge-pill bg-soft-success text-success">
                                Completed
                            </span>
                        {% endif %}
                    </td>
                    <td>{{ audit.completion_date }}</td>
                    <td>{{ audit.auditor.name }}</td>
                    <td>
                        {% if audit.script %}
                            <a href="{{ audit.script.script_file.url }}" class="btn btn-sm btn-primary" download>
                                <i class="fa-solid fa-download me-1"></i> {{ audit.script.name }}{% if audit.script.version %} (v{{ audit.script.version }}){% endif %}
                            </a>
                        {% elif audit.audit_type == 'manual' %}
                            <a href="{% url 'manual_audit_compliance_list' %}" class="btn btn-sm btn-info">
                                <i class="fa-solid fa-clipboard-check me-1"></i> Manual Audit
                            </a>
                        {% else %}
                            <span class="text-muted">No script selected</span>
                        {% endif %}
                    </td>
                    <td>
                        <div class="action-btn-group">
                            <a class="btn btn-sm btn-info" href="{% url 'ongoing_audit_update' audit.id %}">
                                <i class="fa-solid fa-pen"></i>
                            </a>
                            <a class="btn btn-sm btn-danger" href="{% url 'ongoing_audit_delete' audit.id %}">
                                <i class="fa-solid fa-trash"></i>
                            </a>
                        </div>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <p class="text-sm mb-0">No ongoing audits found.</p>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    <div class="mt-4">
        {% if is_paginated %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1" aria-label="First">
                        <span aria-hidden="true">&laquo;&laquo;</span>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active"><a class="page-link" href="?page={{ num }}">{{ num }}</a></li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item"><a class="page-link" href="?page={{ num }}">{{ num }}</a></li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}" aria-label="Last">
                        <span aria-hidden="true">&raquo;&raquo;</span>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>
{% endblock content %}
