/**
 * Auditor Dashboard - Optimized Styling
 */

:root {
    --sidebar-width: 240px;
    --primary-color: #023047;
    --secondary-color: #219ebc;
    --light-color: #f0f5fa;
    --text-color: #333;
}

body {
    font-family: 'Nuni<PERSON>', 'Inter', sans-serif;
    background-color: var(--light-color);
    color: var(--text-color);
    font-size: 0.95rem;
    line-height: 1.5;
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width) !important;
    background-color: var(--primary-color);
    color: #fff;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    display: flex;
    flex-direction: column;
    box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.15);
    overflow-y: auto;
    overflow-x: hidden;
}

.sidebar-inner {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.user-card {
    padding: 1rem;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
    margin: 0.75rem;
    margin-bottom: 1rem;
}

.avatar-lg img, .avatar-sm img {
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2);
}

.avatar-sm {
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-container {
    flex: 1;
    overflow-y: auto;
    padding: 0.5rem 0.75rem;
}

.nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    padding: 0.75rem 1rem !important;
    font-weight: 600 !important;
    border-radius: 0.5rem !important;
    margin: 0.25rem 0.5rem !important;
    transition: all 0.3s ease !important;
    font-size: 0.95rem !important;
    position: relative !important;
    border-left: 3px solid transparent !important;
    letter-spacing: 0.01em !important;
    box-shadow: 0 0.1rem 0.2rem rgba(0, 0, 0, 0.1) !important;
}

.nav-link:hover {
    color: #fff !important;
    background-color: rgba(255, 255, 255, 0.15) !important;
    border-left: 3px solid rgba(255, 255, 255, 0.7) !important;
    transform: translateX(3px) !important;
}

.nav-link.active {
    color: #fff !important;
    background-color: rgba(255, 255, 255, 0.2) !important;
    border-left: 3px solid #fff !important;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15) !important;
    transform: translateX(3px) !important;
}

.dropdown-menu {
    background-color: var(--primary-color) !important;
    border: none !important;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15) !important;
    border-radius: 0.5rem !important;
    padding: 0.5rem 0 !important;
    margin-top: 0.25rem !important;
    min-width: 200px !important;
}

.dropdown-item {
    color: rgba(255, 255, 255, 0.9) !important;
    padding: 0.6rem 1rem !important;
    font-weight: 500 !important;
    font-size: 0.9rem !important;
    transition: all 0.3s ease !important;
}

.dropdown-item:hover, .dropdown-item.active {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: #fff !important;
    transform: translateX(3px) !important;
}

.sidebar-icon {
    width: 1.1rem;
    height: 1.1rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
}

.sidebar-text {
    font-size: 0.95rem;
}

/* Content */
.content {
    margin-left: var(--sidebar-width) !important;
    padding: 1.5rem !important;
    transition: all 0.3s ease-in-out;
}

/* Cards */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
    border: none;
}

.card-header {
    padding: 1rem 1.25rem;
    background-color: rgba(0, 0, 0, 0.02);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-weight: 600;
}

.card-body {
    padding: 1.25rem;
}

/* Tables */
.table th {
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.01em;
}

.table td {
    font-size: 0.9rem;
    vertical-align: middle;
}

/* Buttons */
.btn {
    font-size: 0.85rem;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 0.35rem;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #01253a;
    border-color: #01253a;
}

/* Responsive */
@media (max-width: 991.98px) {
    .content {
        margin-left: 0 !important;
    }

    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }
}

/* Typography */
h1, .h1 {
    font-size: 1.75rem;
    font-weight: 700;
}

h2, .h2 {
    font-size: 1.5rem;
    font-weight: 700;
}

h3, .h3 {
    font-size: 1.25rem;
    font-weight: 600;
}

h4, .h4 {
    font-size: 1.1rem;
    font-weight: 600;
}

h5, .h5 {
    font-size: 1rem;
    font-weight: 600;
}

h6, .h6 {
    font-size: 0.9rem;
    font-weight: 600;
}

/* Forms */
.form-control {
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.35rem;
}

.form-label {
    font-weight: 600;
    font-size: 0.9rem;
}

/* Badges */
.badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.35em 0.65em;
}

/* Alerts */
.alert {
    font-size: 0.9rem;
    padding: 0.75rem 1rem;
    border-radius: 0.35rem;
}

/* Pagination */
.pagination {
    font-size: 0.9rem;
}

.page-link {
    padding: 0.5rem 0.75rem;
}

/* Dropdown menu */
.dropdown-menu {
    font-size: 0.9rem;
}

/* Navbar */
.navbar {
    padding: 0.5rem 1rem;
}

.navbar-brand {
    font-size: 1.25rem;
    font-weight: 700;
}

/* Breadcrumb */
.breadcrumb {
    font-size: 0.85rem;
    padding: 0.5rem 0;
}

/* Modal */
.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
}

.modal-body {
    font-size: 0.9rem;
}

/* Tooltips */
.tooltip {
    font-size: 0.85rem;
}

/* Popovers */
.popover {
    font-size: 0.9rem;
}

/* Progress */
.progress {
    height: 0.75rem;
    font-size: 0.75rem;
}

/* List group */
.list-group-item {
    padding: 0.75rem 1.25rem;
    font-size: 0.9rem;
}

/* Accordion */
.accordion-button {
    font-size: 0.9rem;
    font-weight: 600;
    padding: 1rem 1.25rem;
}

/* Tabs */
.nav-tabs .nav-link {
    font-size: 0.9rem;
    font-weight: 600;
    padding: 0.5rem 1rem;
}

/* Pills */
.nav-pills .nav-link {
    font-size: 0.9rem;
    font-weight: 600;
    padding: 0.5rem 1rem;
}

/* Spinners */
.spinner-border, .spinner-grow {
    width: 1.5rem;
    height: 1.5rem;
}

/* Toasts */
.toast {
    font-size: 0.9rem;
}

/* Utilities */
.fs-7 {
    font-size: 0.85rem !important;
}

.fs-8 {
    font-size: 0.75rem !important;
}

/* Dashboard Stats */
.card .h6.font-bold.text-dark {
    font-size: 0.95rem !important;
    font-weight: 700 !important;
    color: #023047 !important;
    letter-spacing: 0.01em;
}

.w-sidebar {
    width: var(--sidebar-width) !important;
}

.min-w-sidebar {
    min-width: var(--sidebar-width) !important;
}

.max-w-sidebar {
    max-width: var(--sidebar-width) !important;
}
