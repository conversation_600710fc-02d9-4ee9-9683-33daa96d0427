{% extends 'client_portal/volt_base.html' %}
{% load crispy_forms_tags %}
{% load static %}

{% block title %}Create Audit Request{% endblock %}
{% block page_title %}Create Audit Request{% endblock %}

{% block content %}
<div class="py-4">
    <div class="d-flex justify-content-between w-100 flex-wrap mb-4">
        <div class="mb-3 mb-lg-0">
            <h1 class="h4">Create New Audit Request</h1>
            <p class="mb-0">Request an audit for your equipment.</p>
        </div>
        <div>
            <a href="{% url 'client_audit_request_list' %}" class="btn btn-sm btn-gray-200 d-inline-flex align-items-center">
                <i class="fas fa-arrow-left me-2"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-12 col-xl-8 mb-4">
            <form method="post" id="audit-request-form" enctype="multipart/form-data">
                {% csrf_token %}
                <input type="hidden" name="items_json" id="items-json" value="[]">

                <!-- Audit Configuration Card -->
                {% if form.errors %}
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <h4 class="alert-heading">Form Errors</h4>
                    <p>Please correct the following errors:</p>
                    <ul>
                        {% for field in form %}
                            {% for error in field.errors %}
                                <li><strong>{{ field.label }}:</strong> {{ error }}</li>
                            {% endfor %}
                        {% endfor %}
                        {% for error in form.non_field_errors %}
                            <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                {% endif %}

                <div class="card border-0 shadow mb-4">
                    <div class="card-header border-bottom d-flex align-items-center justify-content-between">
                        <h2 class="fs-5 fw-bold mb-0">Audit Configuration</h2>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                {{ form.company_name|as_crispy_field }}
                            </div>
                            <div class="col-md-12 mb-3">
                                {{ form.title|as_crispy_field }}
                            </div>
                            <div class="col-md-12 mb-3">
                                {{ form.description|as_crispy_field }}
                            </div>
                            <div class="col-md-12 mb-3">
                                {{ form.categories|as_crispy_field }}
                            </div>
                            <div class="col-md-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <label for="{{ form.equipment.id_for_label }}" class="form-label">{{ form.equipment.label }}</label>
                                    <button type="button" class="btn btn-sm btn-primary" id="openAddEquipmentBtn">
                                        <i class="fas fa-plus me-1"></i> Add New Equipment
                                    </button>
                                </div>
                                {{ form.equipment }}
                                {% if form.equipment.help_text %}
                                <div class="form-text text-muted">{{ form.equipment.help_text }}</div>
                                {% endif %}
                                {% if form.equipment.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.equipment.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            <!-- Script field removed for clients -->
                            <div class="col-md-12 mb-3">
                                {{ form.config_file|as_crispy_field }}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-end mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-2"></i> Submit Audit Request
                    </button>
                </div>
            </form>
        </div>

        <div class="col-12 col-xl-4 mb-4">
            <div class="card border-0 shadow">
                <div class="card-header border-bottom d-flex align-items-center justify-content-between">
                    <h2 class="fs-5 fw-bold mb-0">Audit Request Information</h2>
                </div>
                <div class="card-body">
                    <p class="mb-4">An audit request is the first step in the auditing process. Here's what you need to know:</p>

                    <div class="d-flex mb-3">
                        <div class="icon-shape icon-shape-sm icon-shape-primary me-3">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">Select Items</h5>
                            <p class="text-muted small mb-0">Choose the items you want to have audited.</p>
                        </div>
                    </div>

                    <div class="d-flex mb-3">
                        <div class="icon-shape icon-shape-sm icon-shape-secondary me-3">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">Review Process</h5>
                            <p class="text-muted small mb-0">Your request will be reviewed by our auditors.</p>
                        </div>
                    </div>

                    <div class="d-flex mb-3">
                        <div class="icon-shape icon-shape-sm icon-shape-tertiary me-3">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">Acceptance</h5>
                            <p class="text-muted small mb-0">If accepted, the audit will be scheduled and performed.</p>
                        </div>
                    </div>

                    <div class="d-flex">
                        <div class="icon-shape icon-shape-sm icon-shape-info me-3">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">Report Generation</h5>
                            <p class="text-muted small mb-0">After the audit, you'll receive a detailed report.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<!-- Select2 Bootstrap 5 Theme CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" />
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<style>
    /* Custom styles for Select2 search boxes */
    .select2-container--bootstrap-5 .select2-selection {
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 0.375rem 0.75rem;
        height: auto;
        min-height: 38px;
    }

    .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__rendered {
        display: flex;
        flex-wrap: wrap;
    }

    .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice {
        background-color: #e9ecef;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        padding: 0.25rem 0.5rem;
        margin-right: 0.5rem;
        margin-top: 0.25rem;
    }

    .select2-container--bootstrap-5 .select2-search--inline .select2-search__field {
        margin-top: 0.25rem;
    }

    .select2-container--bootstrap-5 .select2-dropdown {
        border-color: #dee2e6;
        border-radius: 0.5rem;
    }

    .select2-container--bootstrap-5 .select2-search--dropdown .select2-search__field {
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        padding: 0.375rem 0.75rem;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set empty array for items JSON (we'll handle this differently in the backend)
        document.getElementById('items-json').value = JSON.stringify([]);

        // Initialize Select2 for categories selection with search
        $('#id_categories').select2({
            placeholder: 'Select a category',
            allowClear: false,
            width: '100%',
            theme: 'bootstrap-5',
            dropdownParent: $('#id_categories').parent(),
            minimumResultsForSearch: 0 // Always show search box
        });

        // Initialize Select2 for equipment selection with search
        $('#id_equipment').select2({
            placeholder: 'Search and select equipment for audit',
            allowClear: false,
            width: '100%',
            theme: 'bootstrap-5',
            dropdownParent: $('#id_equipment').parent(),
            minimumResultsForSearch: 0, // Always show search box
            templateResult: formatEquipment
        });

        // Script selection initialization removed for clients

        // Function to format equipment options with category
        function formatEquipment(equipment) {
            if (!equipment.id) {
                return equipment.text;
            }

            var $equipment = $(
                '<span><strong>' + equipment.text.split(' (')[0] + '</strong> <small class="text-muted">(' +
                equipment.text.split(' (')[1] + '</small></span>'
            );

            return $equipment;
        }

        // Add validation for Select2
        $('#audit-request-form').on('submit', function(e) {
            var equipmentSelected = $('#id_equipment').val();
            if (!equipmentSelected || equipmentSelected.length === 0) {
                e.preventDefault();
                alert('Please select at least one equipment item');
                return false;
            }

            var categorySelected = $('#id_categories').val();
            if (!categorySelected) {
                e.preventDefault();
                alert('Please select a category');
                return false;
            }

            // Script validation removed for clients

            return true;
        });
    });
</script>
<!-- Equipment Form Dialog -->
<div id="equipmentFormDialog" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 9999;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background-color: white; padding: 20px; border-radius: 5px; width: 500px; max-width: 90%;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h5 style="margin: 0;">Add New Equipment</h5>
            <button type="button" id="closeEquipmentDialog" style="background: none; border: none; font-size: 1.5rem; cursor: pointer;">&times;</button>
        </div>

        <form id="new-equipment-form">
            {% csrf_token %}
            <div style="margin-bottom: 15px;">
                <label for="equipment-name" style="display: block; margin-bottom: 5px;">Equipment Name</label>
                <input type="text" id="equipment-name" name="name" required style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;">
            </div>

            <div style="margin-bottom: 15px;">
                <label for="equipment-description" style="display: block; margin-bottom: 5px;">Description</label>
                <textarea id="equipment-description" name="description" rows="3" required style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;"></textarea>
            </div>

            <div style="margin-bottom: 15px;">
                <label for="equipment-category" style="display: block; margin-bottom: 5px;">Category</label>
                <select id="equipment-category" name="category" required style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;">
                    <option value="">Select a category</option>
                    <!-- Categories will be populated via JavaScript -->
                </select>
            </div>

            <div style="margin-bottom: 15px;">
                <label for="equipment-version" style="display: block; margin-bottom: 5px;">Version</label>
                <input type="text" id="equipment-version" name="version" required style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;">
            </div>

            <!-- Compliance Points field removed as it will be determined by the auditor after CSV upload -->

            <div style="display: flex; justify-content: flex-end; gap: 10px; margin-top: 20px;">
                <button type="button" id="cancelEquipmentBtn" style="padding: 8px 16px; background-color: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">Cancel</button>
                <button type="button" id="save-equipment-btn" style="padding: 8px 16px; background-color: #0d6efd; color: white; border: none; border-radius: 4px; cursor: pointer;">Save Equipment</button>
            </div>
        </form>
    </div>
</div>

<!-- Notification container -->
<div id="notificationContainer" style="position: fixed; top: 20px; right: 20px; z-index: 10000;"></div>

<script>
    // Additional JavaScript for handling equipment creation
    document.addEventListener('DOMContentLoaded', function() {
        // Load categories for the dropdown
        loadCategories();

        // Get dialog elements
        const dialog = document.getElementById('equipmentFormDialog');
        const openBtn = document.getElementById('openAddEquipmentBtn');
        const closeBtn = document.getElementById('closeEquipmentDialog');
        const cancelBtn = document.getElementById('cancelEquipmentBtn');
        const saveBtn = document.getElementById('save-equipment-btn');
        const form = document.getElementById('new-equipment-form');

        // Open dialog
        openBtn.addEventListener('click', function() {
            dialog.style.display = 'block';
            document.getElementById('equipment-name').focus();
            // Prevent scrolling of the background
            document.body.style.overflow = 'hidden';
        });

        // Close dialog functions
        function closeDialog() {
            dialog.style.display = 'none';
            // Re-enable scrolling
            document.body.style.overflow = 'auto';
        }

        // Close dialog events
        closeBtn.addEventListener('click', closeDialog);
        cancelBtn.addEventListener('click', closeDialog);

        // Close when clicking outside the dialog
        dialog.addEventListener('click', function(e) {
            if (e.target === dialog) {
                closeDialog();
            }
        });

        // Handle save button click
        saveBtn.addEventListener('click', function() {
            saveNewEquipment();
        });

        // Handle form submission (when pressing Enter)
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            saveNewEquipment();
        });

        // Handle escape key to close dialog
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && dialog.style.display === 'block') {
                closeDialog();
            }
        });
    });

    // Function to load categories
    function loadCategories() {
        fetch('/api/categories/')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                const categorySelect = document.getElementById('equipment-category');
                // Clear existing options except the first one
                while (categorySelect.options.length > 1) {
                    categorySelect.remove(1);
                }

                // Add new options
                data.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    categorySelect.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading categories:', error);
                alert('Error loading categories. Please try again.');
            });
    }

    // Function to show notification
    function showNotification(message, type = 'success') {
        // Create notification element
        const notification = document.createElement('div');
        notification.style.padding = '15px 20px';
        notification.style.marginBottom = '10px';
        notification.style.borderRadius = '4px';
        notification.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
        notification.style.display = 'flex';
        notification.style.justifyContent = 'space-between';
        notification.style.alignItems = 'center';

        // Set color based on type
        if (type === 'success') {
            notification.style.backgroundColor = '#d4edda';
            notification.style.color = '#155724';
            notification.style.borderLeft = '4px solid #28a745';
        } else if (type === 'danger') {
            notification.style.backgroundColor = '#f8d7da';
            notification.style.color = '#721c24';
            notification.style.borderLeft = '4px solid #dc3545';
        } else if (type === 'warning') {
            notification.style.backgroundColor = '#fff3cd';
            notification.style.color = '#856404';
            notification.style.borderLeft = '4px solid #ffc107';
        } else {
            notification.style.backgroundColor = '#d1ecf1';
            notification.style.color = '#0c5460';
            notification.style.borderLeft = '4px solid #17a2b8';
        }

        // Add message and close button
        notification.innerHTML = `
            <div>${message}</div>
            <button style="background: none; border: none; font-size: 1.2rem; cursor: pointer; margin-left: 15px;">&times;</button>
        `;

        // Get container and add notification
        const container = document.getElementById('notificationContainer');
        container.appendChild(notification);

        // Add click event to close button
        notification.querySelector('button').addEventListener('click', function() {
            container.removeChild(notification);
        });

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (container.contains(notification)) {
                container.removeChild(notification);
            }
        }, 5000);
    }

    // Function to save new equipment
    function saveNewEquipment() {
        const form = document.getElementById('new-equipment-form');
        const formData = new FormData(form);

        // Show loading state
        const saveButton = document.getElementById('save-equipment-btn');
        const originalText = saveButton.textContent;
        saveButton.disabled = true;
        saveButton.textContent = 'Saving...';

        // Validate form
        if (!form.checkValidity()) {
            form.reportValidity();
            saveButton.disabled = false;
            saveButton.textContent = originalText;
            return;
        }

        // Get CSRF token from the page
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

        // Prepare data for API
        const equipmentData = {
            name: formData.get('name'),
            description: formData.get('description'),
            category: formData.get('category'),
            version: formData.get('version'),
            compliance_points: "0" // Default value, will be updated by auditor after CSV upload
        };

        // Send AJAX request
        fetch('/api/items/create/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': csrfToken,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(equipmentData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            // Add the new equipment to the select dropdown
            const equipmentSelect = document.getElementById('id_equipment');
            const newOption = document.createElement('option');
            newOption.value = data.id;
            newOption.text = `${data.name} (${data.category_name})`;
            newOption.selected = true;
            equipmentSelect.appendChild(newOption);

            // Close the dialog
            document.getElementById('equipmentFormDialog').style.display = 'none';
            document.body.style.overflow = 'auto';

            // Reset the form
            form.reset();

            // Show success message
            showNotification(`Equipment "${data.name}" added successfully!`);
        })
        .catch(error => {
            console.error('Error saving equipment:', error);
            showNotification('Error saving equipment. Please try again.', 'danger');
        })
        .finally(() => {
            // Reset button state
            saveButton.disabled = false;
            saveButton.textContent = originalText;
        });
    }
</script>
{% endblock %}
{% endblock %}
