{% extends "equipment/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}Create Audit Request (Auditor){% endblock title %}

{% block extra_css %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@1.5.2/dist/select2-bootstrap4.min.css">
<style>
    /* Custom styles for Select2 search boxes */
    .select2-container--bootstrap4 .select2-selection {
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 0.375rem 0.75rem;
        height: auto;
        min-height: 38px;
    }

    .select2-container--bootstrap4 .select2-selection--multiple .select2-selection__rendered {
        display: flex;
        flex-wrap: wrap;
    }

    .select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice {
        background-color: #e9ecef;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        padding: 0.25rem 0.5rem;
        margin-right: 0.5rem;
        margin-top: 0.25rem;
    }

    .select2-container--bootstrap4 .select2-search--inline .select2-search__field {
        margin-top: 0.25rem;
    }

    .select2-container--bootstrap4 .select2-dropdown {
        border-color: #dee2e6;
        border-radius: 0.5rem;
    }

    .select2-container--bootstrap4 .select2-search--dropdown .select2-search__field {
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        padding: 0.375rem 0.75rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>Create Audit Request (Auditor)</h2>
                <a href="{% url 'audit_request_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to List
                </a>
            </div>
            <p class="text-muted">As an auditor, you can create audit requests for clients or for new institutions.</p>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Audit Request Details</h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data" id="audit-request-form">
                        {% csrf_token %}

                        {% if form.errors %}
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <h4 class="alert-heading">Form Errors</h4>
                            <p>Please correct the following errors:</p>
                            <ul>
                                {% for field in form %}
                                    {% for error in field.errors %}
                                        <li><strong>{{ field.label }}:</strong> {{ error }}</li>
                                    {% endfor %}
                                {% endfor %}
                                {% for error in form.non_field_errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {% endif %}

                        <!-- Client Selection (Auditor only) -->
                        {% if form.client_profile %}
                        <div class="mb-3">
                            {{ form.client_profile|as_crispy_field }}
                        </div>
                        <hr>
                        {% endif %}

                        <!-- Basic Information -->
                        <div class="mb-3">
                            {{ form.company_name|as_crispy_field }}
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.phone_number|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.email|as_crispy_field }}
                            </div>
                        </div>
                        <div class="mb-3">
                            {{ form.address|as_crispy_field }}
                        </div>
                        <div class="mb-3">
                            {{ form.description|as_crispy_field }}
                        </div>

                        <!-- Equipment Selection -->
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label for="{{ form.equipment.id_for_label }}" class="form-label">{{ form.equipment.label }}</label>
                                <button type="button" class="btn btn-sm btn-primary" id="openAddEquipmentBtn">
                                    <i class="fas fa-plus me-1"></i> Add New Equipment
                                </button>
                            </div>
                            {{ form.equipment }}
                            {% if form.equipment.help_text %}
                            <div class="form-text text-muted">{{ form.equipment.help_text }}</div>
                            {% endif %}
                        </div>

                        <!-- Script Selection -->
                        <div class="mb-3">
                            {{ form.script|as_crispy_field }}
                        </div>

                        <!-- Status (Auditor only) -->
                        {% if form.status %}
                        <div class="mb-3">
                            {{ form.status|as_crispy_field }}
                        </div>
                        {% endif %}

                        <!-- Amount field removed as per user request -->

                        <div class="d-flex justify-content-end mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i> Submit Audit Request
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Audit Request Information</h5>
                </div>
                <div class="card-body">
                    <p class="mb-4">As an auditor, you can create audit requests for clients. Here's what you need to know:</p>

                    <div class="d-flex mb-3">
                        <div class="icon-shape icon-shape-sm icon-shape-primary me-3">
                            <i class="fas fa-user"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">Client Selection</h5>
                            <p class="text-muted small mb-0">You can select an existing client or create a request for a new institution.</p>
                        </div>
                    </div>

                    <div class="d-flex mb-3">
                        <div class="icon-shape icon-shape-sm icon-shape-secondary me-3">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">Equipment Selection</h5>
                            <p class="text-muted small mb-0">Choose the equipment items that will be audited.</p>
                        </div>
                    </div>

                    <div class="d-flex mb-3">
                        <div class="icon-shape icon-shape-sm icon-shape-tertiary me-3">
                            <i class="fas fa-code"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">Script Selection</h5>
                            <p class="text-muted small mb-0">Select the appropriate audit script for this request.</p>
                        </div>
                    </div>

                    <div class="d-flex">
                        <div class="icon-shape icon-shape-sm icon-shape-info me-3">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">Status</h5>
                            <p class="text-muted small mb-0">You can set the initial status of the audit request.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
    $(document).ready(function() {
        // Initialize Select2 for equipment selection
        $('#id_equipment').select2({
            placeholder: 'Search and select equipment for audit',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4',
            minimumResultsForSearch: 0 // Always show search box
        });

        // Initialize Select2 for script selection
        $('#id_script').select2({
            placeholder: 'Select a script for the audit',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4',
            minimumResultsForSearch: 0 // Always show search box
        });

        // Initialize Select2 for client selection (if available)
        if ($('#id_client_profile').length) {
            $('#id_client_profile').select2({
                placeholder: 'Select a client (optional)',
                allowClear: true,
                width: '100%',
                theme: 'bootstrap4',
                minimumResultsForSearch: 0 // Always show search box
            });

            // When client is selected, populate other fields
            $('#id_client_profile').on('change', function() {
                const clientId = $(this).val();
                if (clientId) {
                    // Make AJAX call to get client details
                    $.ajax({
                        url: `/api/clients/${clientId}/`,
                        type: 'GET',
                        success: function(data) {
                            // Populate form fields with client data
                            $('#id_company_name').val(data.company_name || '');
                            $('#id_phone_number').val(data.phone || '');
                            $('#id_email').val(data.email || '');
                            $('#id_address').val(data.address || '');
                        },
                        error: function(error) {
                            console.error('Error fetching client details:', error);
                        }
                    });
                }
            });
        }

        // Form validation
        $('#audit-request-form').on('submit', function(e) {
            let isValid = true;

            // Check required fields
            if (!$('#id_company_name').val()) {
                alert('Please enter a company/institution name');
                isValid = false;
            }

            if (!$('#id_equipment').val() || $('#id_equipment').val().length === 0) {
                alert('Please select at least one equipment item');
                isValid = false;
            }

            if (!$('#id_script').val()) {
                alert('Please select a script');
                isValid = false;
            }

            return isValid;
        });
    });
</script>
{% endblock %}
