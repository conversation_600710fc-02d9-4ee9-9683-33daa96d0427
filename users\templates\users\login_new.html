{% load static %}
{% load crispy_forms_tags %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Audit System</title>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/auth.css' %}">
</head>
<body>
    <div class="bg-pattern"></div>
    
    <div class="auth-wrapper">
        <div class="auth-sidebar">
            <div class="auth-sidebar-content">
                <div class="auth-logo">
                    <img src="{% static 'assets/img/brand/light.svg' %}" alt="Logo">
                </div>
                <h1>Welcome Back!</h1>
                <p>Log in to access your account and manage your audits with our comprehensive audit management system.</p>
                
                <ul class="auth-features">
                    <li><i class="fas fa-check"></i> Manage audit requests and reports</li>
                    <li><i class="fas fa-check"></i> Track audit progress in real-time</li>
                    <li><i class="fas fa-check"></i> Secure and compliant data handling</li>
                    <li><i class="fas fa-check"></i> Comprehensive analytics and insights</li>
                </ul>
            </div>
        </div>
        
        <div class="auth-content">
            <div class="auth-header">
                <h2>Sign In</h2>
                <p>Enter your credentials to access your account</p>
            </div>
            
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
            
            <form method="POST" class="auth-form">
                {% csrf_token %}
                
                <div class="form-group">
                    <label for="id_username">Username</label>
                    <input type="text" name="username" class="form-control" id="id_username" placeholder="Enter your username" required>
                </div>
                
                <div class="form-group">
                    <label for="id_password">Password</label>
                    <input type="password" name="password" class="form-control" id="id_password" placeholder="Enter your password" required>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <label class="custom-checkbox">
                        Remember me
                        <input type="checkbox" name="remember_me">
                        <span class="checkmark"></span>
                    </label>
                    
                    <a href="{% url 'password_reset' %}" class="text-decoration-none">Forgot password?</a>
                </div>
                
                <button type="submit" class="btn btn-primary w-100">Sign In</button>
            </form>
            
            <div class="auth-footer">
                <p>Don't have an account? <a href="{% url 'register' %}">Sign Up</a></p>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
