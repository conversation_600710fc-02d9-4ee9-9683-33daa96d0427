# Audit Platform

A comprehensive Django-based platform for managing equipment audits and compliance checks with both automated and manual audit capabilities.

## Features

- **Equipment Management**: Track and manage auditable equipment with categories and technical solutions
- **Audit Process**: Streamlined audit request and execution workflow with both automated and manual audit options
- **Client Portal**: Dedicated interface for clients to submit and track audit requests
- **Staff Dashboard**: Administrative interface for managing audits and reports with interactive charts
- **Report Generation**: Detailed audit report generation with compliance statistics
- **User Management**: Role-based access control (Admin, Auditor, Client)
- **API Integration**: JWT-based authentication for secure API access
- **Multi-Database Support**: Separate databases for application data and audit control points

## Tech Stack

- **Backend**: Django 4.1 with Django REST Framework
- **Frontend**: Bootstrap 5, React 18
- **Database**: PostgreSQL (dual database setup)
- **Authentication**: Session-based and JWT authentication
- **Containerization**: Docker and Docker Compose
- **Additional Packages**:
  - django-crispy-forms & crispy-bootstrap5
  - django-imagekit
  - django-filter
  - django-tables2
  - django-phonenumber-field
  - djangorestframework-simplejwt
  - django-cors-headers
  - pandas & openpyxl for data processing
  - react, react-dom, axios for frontend components

## Installation

### Option 1: Using Docker (Recommended)

The application is fully dockerized for both development and production environments.

#### Prerequisites

- Docker
- Docker Compose

#### Development Environment

1. Clone the repository:

   ```bash
   git clone https://github.com/laminiabdellah1/Audit_Platfrome.git
   cd Audit_Platfrome
   ```

2. Create a `.env` file based on `.env.example`:

   ```bash
   cp .env.example .env
   ```

3. Start the development environment:

   ```bash
   docker-compose up -d
   ```

4. Access the application at http://localhost:8000

5. Create a superuser:
   ```bash
   docker-compose exec web python manage.py createsuperuser
   ```

#### Docker Commands

- Start containers: `docker-compose up -d`
- Stop containers: `docker-compose down`
- View logs: `docker-compose logs -f`
- Rebuild containers: `docker-compose build`
- Run migrations: `docker-compose exec web python manage.py migrate`
- Create superuser: `docker-compose exec web python manage.py createsuperuser`
- Collect static files: `docker-compose exec web python manage.py collectstatic --noinput`

### Option 2: Manual Installation

1. Clone the repository:

```bash
git clone https://github.com/laminiabdellah1/Audit_Platfrome.git
cd Audit_Platfrome
```

2. Create and activate virtual environment:

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install Python dependencies:

```bash
pip install -r requirements.txt
```

4. Set up PostgreSQL databases:

   - Create two databases: `auditconfig` and `audit_db`
   - Update database settings in `AuditPlatform/settings.py` if needed

5. Install Node.js dependencies and build React components:

```bash
npm install
npm run build
```

6. Apply migrations:

```bash
python manage.py migrate
```

7. Create superuser:

```bash
python manage.py createsuperuser
```

8. Run development server:

```bash
python manage.py runserver
```

## Project Structure

```
AuditPlatform/
├── accounts/          # User authentication and profiles
├── api/               # REST API with JWT authentication
├── audit_process/     # Core audit workflow and compliance checks
├── audit_reports/     # Report generation and statistics
├── audit_requests/    # Audit request handling
├── client_portal/     # Client-specific features and dashboard
├── equipment/         # Equipment and category management
├── static/            # Static files (CSS, JS, images)
│   ├── js/            # JavaScript files
│   │   ├── react/     # React source files
│   │   └── dist/      # Compiled React bundles
└── templates/         # HTML templates
```

## Key Features

### User Roles and Access Control

- **Administrators**: Full access to all features and settings
- **Auditors**: Create and manage audits, upload compliance data, generate reports
- **Clients**: Submit audit requests, view audit reports, manage equipment

### Audit Types

- **Automated Audits**: Script-based audits with CSV output processing
- **Manual Audits**: Interface for auditors to manually check compliance points and upload evidence

### Compliance Reporting

- Detailed compliance statistics with pass/fail metrics
- Visual charts showing compliance status by category
- CSV upload functionality for automated audit results

### React Components

The project includes several React components that enhance the user interface:

- **Dashboard Widgets**: Interactive dashboard widgets that display key metrics
- **Notification Center**: Real-time notification system in the navigation bar
- **Category Selector**: Dynamic equipment category selection interface
- **JWT Authentication**: React-based JWT authentication interface

## API Endpoints

### Authentication Endpoints

- `/api/auth/token/`: Obtain JWT token pair
- `/api/auth/token/refresh/`: Refresh JWT token
- `/api/auth/register/`: Register new user
- `/api/auth/logout/`: Logout and blacklist token

### User Data Endpoints

- `/api/auth/user/profile/`: Get current user profile
- `/api/auth/user/data/`: Get current user data
- `/api/auth/users/`: List all users
- `/api/auth/profiles/`: List all profiles
- `/api/auth/auditors/`: List all auditors
- `/api/auth/clients/`: List all clients

### Equipment Endpoints

- `/api/categories/`: Get all equipment categories
- `/api/items/create/`: Create new equipment item

### Dashboard Endpoints

- `/api/dashboard/stats/`: Get dashboard statistics
- `/api/notifications/`: Get user notifications
- `/api/notifications/<id>/read/`: Mark a notification as read
- `/api/audit-categories/`: Get audit categories

## Development

### React Development

To work on the React components:

1. Make changes to the React files in `static/js/react/`
2. Run the development build to see changes in real-time:

```bash
npm run dev
```

### Building for Production

To build the React components for production:

```bash
npm run build
```
