{% extends 'client_portal/base.html' %}

{% block title %}My Audit Requests{% endblock %}
{% block page_title %}My Audit Requests{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>My Audit Requests</h2>
                <a href="{% url 'client_create_audit_request' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i> Create New Audit Request
                </a>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Audit Requests</h5>
                </div>
                <div class="card-body">
                    {% if audit_requests %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Date</th>
                                    <th>Items</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for request in audit_requests %}
                                <tr>
                                    <td>{{ request.id }}</td>
                                    <td>{{ request.date|date:"M d, Y" }}</td>
                                    <td>{{ request.details.count }}</td>
                                    <td>
                                        {% if request.status == 'P' %}
                                        <span class="badge bg-warning">Pending</span>
                                        {% elif request.status == 'A' %}
                                        <span class="badge bg-success">Accepted</span>
                                        {% elif request.status == 'R' %}
                                        <span class="badge bg-danger">Rejected</span>
                                        {% else %}
                                        <span class="badge bg-secondary">Unknown</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'client_audit_request_detail' request.id %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if is_paginated %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </span>
                            </li>
                            {% endif %}
                            
                            {% for i in paginator.page_range %}
                            {% if page_obj.number == i %}
                            <li class="page-item active">
                                <span class="page-link">{{ i }}</span>
                            </li>
                            {% else %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ i }}">{{ i }}</a>
                            </li>
                            {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </span>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    
                    {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted">You haven't made any audit requests yet.</p>
                        <a href="{% url 'client_create_audit_request' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i> Create Your First Audit Request
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
