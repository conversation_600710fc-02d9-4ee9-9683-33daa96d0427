# Generated by Django 4.2.9 on 2025-04-29 15:11

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0004_alter_profile_telephone"),
    ]

    operations = [
        migrations.CreateModel(
            name="AuditorNotification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("audit_request", "Audit Request"),
                            ("audit_report", "Audit Report"),
                            ("system", "System Notification"),
                        ],
                        max_length=20,
                    ),
                ),
                ("title", models.Char<PERSON>ield(max_length=100)),
                ("message", models.TextField()),
                (
                    "related_id",
                    models.IntegerField(
                        blank=True,
                        help_text="ID of the related object (e.g., audit request ID)",
                        null=True,
                    ),
                ),
                ("is_read", models.<PERSON><PERSON><PERSON><PERSON><PERSON>(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "auditor",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to="accounts.profile",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
    ]
