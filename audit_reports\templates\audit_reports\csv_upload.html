{% extends 'equipment/base.html' %}
{% load static %}
{% load report_filters %}

{% block title %}Upload CSV Data{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Upload CSV Data for Compliance Analysis</h3>
                </div>
                <div class="card-body">
                    <p class="text-muted">
                        Select a report and upload the corresponding CSV file to update the compliance data.
                    </p>

                    {% if selected_report %}
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> Selected Report</h5>
                        <p><strong>Title:</strong> {{ selected_report.title }}</p>
                        <p><strong>Equipment:</strong> {{ selected_report.equipment.name }}</p>
                        <p><strong>Client:</strong> {{ selected_report.client.user.get_full_name|default:"Not specified" }}</p>
                        <p><strong>Current Compliance Score:</strong> {{ selected_report.compliance_score|floatformat:1 }}%</p>
                        <p><strong>Status:</strong>
                            {% if selected_report.status == 'pending' %}
                                <span class="badge bg-warning">Pending Review</span>
                            {% elif selected_report.status == 'approved' %}
                                <span class="badge bg-success">Approved</span>
                            {% elif selected_report.status == 'rejected' %}
                                <span class="badge bg-danger">Rejected</span>
                            {% elif selected_report.status == 'archived' %}
                                <span class="badge bg-secondary">Archived</span>
                            {% endif %}
                        </p>
                    </div>
                    {% endif %}



                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}

                        <div class="form-group mb-3">
                            <label for="{{ form.report.id_for_label }}">{{ form.report.label }}</label>

                            <!-- Custom select element with all available reports -->
                            <select name="report" id="{{ form.report.id_for_label }}" class="form-control">
                                <option value="">-- Select a Report --</option>

                                {% if script_reports %}
                                <optgroup label="Script-Generated Reports">
                                    {% for report in script_reports %}
                                        <option value="script_{{ report.id }}" {% if selected_report and selected_report.id == report.id and selected_report|get_class_name == 'ScriptGeneratedReport' %}selected{% endif %}>
                                            {{ report.title }} ({{ report.equipment.name }})
                                            {% if report.client %} - Client: {{ report.client.user.get_full_name }}{% endif %}
                                            {% if report.auditor %} - Auditor: {{ report.auditor }}{% endif %}
                                        </option>
                                    {% endfor %}
                                </optgroup>
                                {% endif %}

                                {% if audit_reports %}
                                <optgroup label="Standard Audit Reports">
                                    {% for report in audit_reports %}
                                        <option value="audit_{{ report.id }}" {% if selected_report and selected_report.id == report.id and selected_report|get_class_name == 'AuditReport' %}selected{% endif %}>
                                            Audit Report #{{ report.id }} - {{ report.equipment.name }}
                                            {% if report.client_name %} - Client: {{ report.client_name }}{% endif %}
                                        </option>
                                    {% endfor %}
                                </optgroup>
                                {% endif %}

                                {% if not script_reports and not audit_reports %}
                                    <option value="" disabled>No reports available</option>
                                {% endif %}
                            </select>



                            {% if form.report.help_text %}
                                <small class="form-text text-muted">{{ form.report.help_text }}</small>
                            {% endif %}
                            {% if form.report.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.report.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group mb-3">
                            <label for="{{ form.csv_file.id_for_label }}">{{ form.csv_file.label }}</label>
                            {{ form.csv_file }}
                            {% if form.csv_file.help_text %}
                                <small class="form-text text-muted">{{ form.csv_file.help_text }}</small>
                            {% endif %}
                            {% if form.csv_file.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.csv_file.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-check mb-3">
                            {{ form.process_immediately }}
                            <label class="form-check-label" for="{{ form.process_immediately.id_for_label }}">
                                {{ form.process_immediately.label }}
                            </label>
                            {% if form.process_immediately.help_text %}
                                <small class="form-text text-muted d-block">{{ form.process_immediately.help_text }}</small>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload mr-1"></i> Upload CSV
                            </button>
                            <a href="{% url 'script_reports_dashboard' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left mr-1"></i> Back to Dashboard
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    {% if recent_data %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Recent Compliance Data</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Equipment</th>
                                    <th>Report</th>
                                    <th>Date</th>
                                    <th>Total Checks</th>
                                    <th>Passed</th>
                                    <th>Failed</th>
                                    <th>Compliance Score</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for data in recent_data %}
                                <tr>
                                    <td>{{ data.equipment.name }}</td>
                                    <td>{{ data.report.title }}</td>
                                    <td>{{ data.date_checked|date:"M d, Y H:i" }}</td>
                                    <td>{{ data.total_checks }}</td>
                                    <td>{{ data.passed_checks }}</td>
                                    <td>{{ data.failed_checks }}</td>
                                    <td>
                                        <div class="progress" style="height: 8px; width: 120px;">
                                            <div class="progress-bar {% if data.compliance_score >= 70 %}bg-success{% elif data.compliance_score >= 50 %}bg-warning{% else %}bg-danger{% endif %}"
                                                 role="progressbar"
                                                 style="width: {{ data.compliance_score }}%;"
                                                 aria-valuenow="{{ data.compliance_score }}"
                                                 aria-valuemin="0"
                                                 aria-valuemax="100">
                                            </div>
                                        </div>
                                        <span class="ml-2">{{ data.compliance_score|floatformat:1 }}%</span>
                                    </td>
                                    <td>
                                        <a href="{% url 'script_report_detail' data.report.slug %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> View Report
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add file name display when a file is selected
    document.addEventListener('DOMContentLoaded', function() {
        const fileInput = document.getElementById('{{ form.csv_file.id_for_label }}');
        fileInput.addEventListener('change', function(e) {
            const fileName = e.target.files[0]?.name || 'No file selected';
            const fileLabel = document.querySelector('label[for="{{ form.csv_file.id_for_label }}"]');
            fileLabel.textContent = `CSV File: ${fileName}`;
        });
    });
</script>
{% endblock %}
