# Generated by Django 5.1 on 2025-05-06 23:25

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("audit_process", "0004_ongoingaudit_script_file"),
        ("equipment", "0002_delete_delivery"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="ongoingaudit",
            name="script_file",
        ),
        migrations.CreateModel(
            name="AuditScript",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="Script Name")),
                (
                    "description",
                    models.TextField(
                        blank=True, null=True, verbose_name="Script Description"
                    ),
                ),
                (
                    "script_file",
                    models.FileField(
                        upload_to="audit_scripts/", verbose_name="Script File"
                    ),
                ),
                (
                    "version",
                    models.Char<PERSON>ield(
                        blank=True, max_length=20, null=True, verbose_name="Version"
                    ),
                ),
                (
                    "date_added",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="Date Added"
                    ),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="Active")),
                (
                    "category",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="audit_scripts",
                        to="equipment.category",
                        verbose_name="Equipment Category",
                    ),
                ),
            ],
            options={
                "verbose_name": "Audit Script",
                "verbose_name_plural": "Audit Scripts",
                "ordering": ["-date_added"],
            },
        ),
        migrations.AddField(
            model_name="ongoingaudit",
            name="script",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="ongoing_audits",
                to="audit_process.auditscript",
                verbose_name="Audit Script",
            ),
        ),
    ]
