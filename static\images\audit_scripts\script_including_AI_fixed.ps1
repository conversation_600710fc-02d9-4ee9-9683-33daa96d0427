# --------------------------------------------------------------------------- #
#      Audit de points de conformité Windows – PowerShell 5.x                 #
# --------------------------------------------------------------------------- #

$dbhost = "localhost"
$port = 5432
$db = "audit_db"
$user = "postgres"
$password = "Livealone123@"

$apiKey = "AIzaSyB9sFFaszS4vmVQ5MJlSrzQMM1zzuy_yGk"
$uri = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=$apiKey"

Add-Type -Path "C:\Users\<USER>\Downloads\Npgsql.dll"

$osCaption = (Get-CimInstance Win32_OperatingSystem).Caption
Write-Host "`n  Système détecté : $osCaption"

switch -Wildcard ($osCaption) {
    "*Windows 10*" { $osName = "Windows 10" }
    "*Windows 11*" { $osName = "Windows 11" }
    "*Windows 7*" { $osName = "Windows 7" }
    "*Server 2008*" { $osName = "Windows Server 2008" }
    "*Server 2012*" { $osName = "Windows Server 2012" }
    "*Server 2016*" { $osName = "Windows Server 2016" }
    "*Server 2019*" { $osName = "Windows Server 2019" }
    "*Server 2022*" { $osName = "Windows Server 2022" }
    default { Write‑Error "OS non pris en charge." ; exit 1 }
}

# --------------------------- Requête SQL ------------------------------------
$query = @"
SELECT pc.idpoint,
       pc.descriptionpoint,
       pc.commandeaudit,
       pc.expectedoutput
FROM   point_controle pc
JOIN   version_point vp ON pc.idpoint = vp.idpoint
JOIN   version_os    vo ON vp.idversion = vo.idversion
WHERE  vo.nomversion = '$osName'
ORDER  BY pc.idpoint;
"@

# ------------- Normalisation (fr/en/es + ajouts) ----------------------------
function Convert-AccountNames {
    param([string]$textInput)
    
    # Return the input as is for now - can be enhanced with actual normalization logic
    return $textInput
}

# -------------------- Export secpol (une seule fois) ------------------------
$tmp = $env:TEMP
$raw = Join-Path $tmp 'secpol_raw.cfg'
$utf = Join-Path $tmp 'secpol_utf8.cfg'

& "$env:SystemRoot\System32\secedit.exe" /export /cfg $raw | Out-Null
Get-Content $raw | Set-Content $utf -Encoding UTF8

# --------------------------- Connexion PGSQL --------------------------------
$conn = [Npgsql.NpgsqlConnection]::new(
    "Host=$dbhost;Port=$port;Username=$user;Password=$password;Database=$db")

$missing = @()
$results = @()

try {
    $conn.Open()
    Write-Host " Connecté à PostgreSQL.`n"
    $cmd = [Npgsql.NpgsqlCommand]::new($query, $conn)
    $rd = $cmd.ExecuteReader()

    while ($rd.Read()) {
        $id = $rd['idpoint']
        $desc = $rd['descriptionpoint']
        $cmdAud = $rd['commandeaudit']
        $exp = $rd['expectedoutput']

        Write-Host " [$id] $desc"

        # Process the audit command
        $act = ""
        try {
            # SecEdit case (command = findstr ...)
            if ($cmdAud -match '^\s*findstr\s+(?<right>Se\w+(Privilege|Right))') {
                $right = $Matches['right']
                if ($right -ieq 'SeSystemTimePrivilege') { 
                    $right = 'SeSystemtimePrivilege' 
                }

                # Exact pattern, insensitive to whitespace
                $pattern = '^\s*' + [regex]::Escape($right) + '\s*='
                $match = Select-String -Path $utf -Pattern $pattern

                if ($match) {
                    $sids = ($match.Line -split '=')[1].Trim() -split ','
                    # SID translation; ignore those we can't convert
                    $names = @()
                    foreach ($sid in $sids) {
                        $s = $sid.Trim(' *')
                        try {
                            $name = ([System.Security.Principal.SecurityIdentifier]$s).Translate([System.Security.Principal.NTAccount]).Value
                            $names += $name
                        }
                        catch {
                            # Skip if translation fails
                        }
                    }
                    
                    if ($names.Count -gt 0) {
                        $act = $names -join ', '
                    }
                    else {
                        $missing += [pscustomobject]@{ idPoint = $id; Right = $right }
                        $act = 'Not Found'
                    }
                }
                else {
                    $missing += [pscustomobject]@{ idPoint = $id; Right = $right }
                    $act = 'Not Found'
                }
            }
            # Other commands (reg query, etc.)
            else {
                $act = (Invoke-Expression $cmdAud 2>&1 | Out-String).Trim()
            }
        }
        catch {
            $act = "Erreur : $($_.Exception.Message)"
        }

        # Comparison
        $expN = Convert-AccountNames $exp
        $actN = Convert-AccountNames $act

        $expS = (($expN -split ',') | ForEach-Object { $_.Trim() } | Sort-Object -Unique) -join ', '
        $actS = (($actN -split ',') | ForEach-Object { $_.Trim() } | Sort-Object -Unique) -join ', '

        # The request body for Gemini API
        $body = @"
        {
        "contents": [
            {
            "parts": [
                {
                "text": "in configuration audit context, tell me if these two upcoming outputs match regardless of language difference or not,check general meaning only if they do return only conforme if not return non conforme :'$exp' and '$act' "
                }
            ]
            }
        ]
        }
"@

        # Set the headers for the request
        $headers = @{
            "Content-Type" = "application/json"
        }

        $apiResponse = ""
        try {
            # Send the POST request to the Gemini API
            $response = Invoke-RestMethod -Uri $uri -Method Post -Headers $headers -Body $body

            # Extract and display the generated text
            $apiResponse = $response.candidates[0].content.parts[0].text
            Write-Host $apiResponse
        }
        catch {
            # Handle any errors that occur during the API call
            $apiResponse = "Error: API call failed"
            Write-Error "Error communicating with the Gemini API: $($_.Exception.Message)"
        }

        # Add to results
        $results += [pscustomobject]@{
            idPoint          = $id
            descriptionPoint = $desc
            commandExecuted  = $cmdAud
            expectedOutput   = $expS
            actualOutput     = $actS
            statut           = $apiResponse
        }
    }
    
    $rd.Close()
}
catch {
    Write-Error "Database error: $($_.Exception.Message)"
}
finally {
    if ($conn.State -eq 'Open') {
        $conn.Close()
    }
    Remove-Item $raw, $utf -EA SilentlyContinue
}

# -------------------- Export CSV --------------------------------------------
$csvMain = ".\audit_result_$($osName -replace ' ','_').csv"
$csvMiss = ".\missing_privileges_$($osName -replace ' ','_').csv"

$results | Export-Csv $csvMain -NoTypeInformation -Encoding UTF8
$missing | Export-Csv $csvMiss -NoTypeInformation -Encoding UTF8

Write-Host "`n Résultats            : $csvMain"
Write-Host " Droits introuvables  : $csvMiss"
