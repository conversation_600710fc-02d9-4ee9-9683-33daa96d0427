{% extends "equipment/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}{% if form.instance.pk %}Edit{% else %}Upload{% endif %} Script Report{% endblock title %}

{% block content %}
<div class="container p-5">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'script_reports_dashboard' %}">Script Reports</a></li>
                    <li class="breadcrumb-item active" aria-current="page">
                        {% if form.instance.pk %}Edit{% else %}Upload{% endif %} Report
                    </li>
                </ol>
            </nav>
            <h2 class="text-success">{% if form.instance.pk %}Edit{% else %}Upload{% endif %} Script Report</h2>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}

                        <div class="row">
                            <div class="col-md-6">
                                <h4 class="mb-3">Report Information</h4>

                                <div class="mb-3">
                                    {{ form.title|as_crispy_field }}
                                </div>

                                <div class="mb-3">
                                    {{ form.date_created|as_crispy_field }}
                                </div>

                                <div class="mb-3">
                                    {{ form.ongoing_audit|as_crispy_field }}
                                </div>

                                <div class="mb-3">
                                    {{ form.client|as_crispy_field }}
                                </div>

                                <div class="mb-3">
                                    {{ form.equipment|as_crispy_field }}
                                </div>

                                <div class="mb-3">
                                    {{ form.category|as_crispy_field }}
                                </div>

                                <div class="mb-3">
                                    {{ form.report_file|as_crispy_field }}
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h4 class="mb-3">Compliance Information</h4>

                                <div class="mb-3">
                                    {{ form.compliance_score|as_crispy_field }}
                                </div>

                                <div class="mb-3">
                                    {{ form.is_compliant|as_crispy_field }}
                                </div>

                                <div class="mb-3">
                                    {{ form.status|as_crispy_field }}
                                </div>

                                <h4 class="mb-3">Script Information</h4>

                                <div class="mb-3">
                                    {{ form.script_name|as_crispy_field }}
                                </div>

                                <div class="mb-3">
                                    {{ form.script_version|as_crispy_field }}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <h4 class="mb-3">Report Content</h4>

                                <div class="mb-3">
                                    {{ form.description|as_crispy_field }}
                                </div>

                                <div class="mb-3">
                                    {{ form.findings|as_crispy_field }}
                                </div>

                                <div class="mb-3">
                                    {{ form.recommendations|as_crispy_field }}
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end mt-4">
                            <a href="{% url 'script_reports_dashboard' %}" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-1"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-1"></i> Save
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get the ongoing audit select element
        const ongoingAuditSelect = document.getElementById('id_ongoing_audit');
        const equipmentSelect = document.getElementById('id_equipment');
        const categorySelect = document.getElementById('id_category');

        // Add event listener to update equipment when ongoing audit changes
        ongoingAuditSelect.addEventListener('change', function() {
            const ongoingAuditId = this.value;

            if (ongoingAuditId) {
                // Fetch the ongoing audit details
                fetch(`/audit-process/api/ongoing-audit/${ongoingAuditId}/`)
                    .then(response => response.json())
                    .then(data => {
                        // Set the equipment value
                        if (data.item_id) {
                            equipmentSelect.value = data.item_id;

                            // Now fetch the equipment details to get the category
                            fetch(`/equipment/api/item/${data.item_id}/`)
                                .then(response => response.json())
                                .then(itemData => {
                                    if (itemData.category_id) {
                                        categorySelect.value = itemData.category_id;
                                    }
                                });
                        }
                    });
            }
        });
    });
</script>
{% endblock content %}
