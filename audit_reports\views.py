# Django core imports
from django.urls import reverse
from django.db import models
from django.db.models import Count, Q
from django.db.models.functions import TruncMonth
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.contrib.auth.decorators import login_required
from django.shortcuts import redirect, get_object_or_404, render
from django.contrib import messages
from django.views import View
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView, TemplateView

# Models
from client_portal.models import ClientProfile
import json
import csv
import io
import pandas as pd
from datetime import datetime, timedelta
import re

# Authentication and permissions
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin

# Class-based views
from django.views.generic import (
    DetailView, CreateView, UpdateView, DeleteView, TemplateView
)

# Third-party packages
from django_tables2 import SingleTableView
from django_tables2.export.views import ExportMixin

# Local app imports
from .models import AuditReport, ScriptGeneratedReport, ComplianceData
from .tables import AuditReportTable
from .forms import ScriptGeneratedReportForm, CSVProcessingForm, AuditReportForm, WindowsAuditCSVForm


class AuditReportListView(LoginRequiredMixin, ExportMixin, SingleTableView):
    """
    View for listing audit reports with table export functionality.
    """
    model = AuditReport
    table_class = AuditReportTable
    template_name = 'audit_reports/invoicelist.html'
    context_object_name = 'invoices'  # Changed to match the template
    paginate_by = 10
    table_pagination = False  # Disable table pagination


class AuditReportDetailView(DetailView):
    """
    View for displaying audit report details.
    """
    model = AuditReport
    template_name = 'audit_reports/invoicedetail.html'

    def get_success_url(self):
        """
        Return the URL to redirect to after a successful action.
        """
        return reverse('auditreport-detail', kwargs={'slug': self.object.pk})


class AuditReportCreateView(LoginRequiredMixin, CreateView):
    """
    View for creating a new audit report.
    """
    model = AuditReport
    template_name = 'audit_reports/invoicecreate.html'
    form_class = AuditReportForm

    def get_initial(self):
        """Set initial values for the form."""
        initial = super().get_initial()

        # If client_id is in GET parameters, pre-select the client
        if 'client_id' in self.request.GET:
            try:
                client_id = int(self.request.GET.get('client_id'))
                initial['client'] = client_id
            except (ValueError, TypeError):
                pass

        return initial

    def get_success_url(self):
        """Return the URL to redirect to after a successful creation."""
        return reverse('auditreportlist')

    def form_valid(self, form):
        """Process the form if it's valid."""
        # Get form data
        client = form.cleaned_data.get('client')
        equipment = form.cleaned_data.get('equipment')

        # Save the form
        response = super().form_valid(form)

        # Show success message
        messages.success(
            self.request,
            f"Audit report for {equipment.name} created successfully."
        )

        # Send notification to client
        if client:
            from client_portal.models import ClientNotification
            ClientNotification.objects.create(
                client=client,
                notification_type='audit_report',
                title=f"New Audit Report: {equipment.name}",
                message=f"A new audit report has been created for your {equipment.name}. "
                        f"Result: {'Pass' if form.cleaned_data.get('audit_result') else 'Fail'}."
            )

        return response


class AuditReportUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    """
    View for updating an existing audit report.
    """
    model = AuditReport
    template_name = 'audit_reports/invoiceupdate.html'
    form_class = AuditReportForm

    def get_success_url(self):
        """
        Return the URL to redirect to after a successful update.
        """
        return reverse('auditreportlist')

    def test_func(self):
        """
        Determine if the user has permission to update the audit report.
        """
        return self.request.user.is_superuser

    def form_valid(self, form):
        """Handle notifications when the report is updated."""
        # Check if audit_result has changed
        old_result = self.get_object().audit_result
        new_result = form.cleaned_data.get('audit_result')

        # Save the form
        response = super().form_valid(form)

        # Send notification to client if result has changed
        if old_result != new_result:
            client = form.cleaned_data.get('client')
            if client:
                from client_portal.models import ClientNotification
                ClientNotification.objects.create(
                    client=client,
                    notification_type='audit_report',
                    title=f"Audit Report Updated: {self.object.equipment.name}",
                    message=f"Your audit report for {self.object.equipment.name} has been updated. "
                            f"New result: {'Pass' if new_result else 'Fail'}."
                )

        return response


class AuditReportDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    """
    View for deleting an audit report.
    """
    model = AuditReport
    template_name = 'audit_reports/invoicedelete.html'
    success_url = '/equipment'  # Can be overridden in get_success_url()

    def get_success_url(self):
        """
        Return the URL to redirect to after a successful deletion.
        """
        return reverse('auditreportlist')

    def test_func(self):
        """
        Determine if the user has permission to delete the audit report.
        """
        return self.request.user.is_superuser


class ReportsDashboardView(LoginRequiredMixin, TemplateView):
    """
    View for displaying the equipment reports dashboard with statistics and charts.
    """
    template_name = 'audit_reports/reports_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get all audit reports
        reports = AuditReport.objects.all()
        total_reports = reports.count()

        # Get passed and failed reports
        passed_reports = reports.filter(audit_result=True).count()
        failed_reports = reports.filter(audit_result=False).count()

        # Calculate percentages
        passed_percentage = round((passed_reports / total_reports) * 100) if total_reports > 0 else 0
        failed_percentage = round((failed_reports / total_reports) * 100) if total_reports > 0 else 0

        # Get equipment categories
        from equipment.models import Category
        categories = Category.objects.annotate(report_count=Count('item__auditreport'))
        category_count = categories.count()

        # Prepare data for equipment categories chart
        category_names = json.dumps([cat.name for cat in categories])
        category_counts = json.dumps([cat.report_count for cat in categories])

        # Get reports by month for the last 12 months
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)

        monthly_reports = (
            reports.filter(date__range=[start_date, end_date])
            .annotate(month=TruncMonth('date'))
            .values('month', 'audit_result')
            .annotate(count=Count('id'))
            .order_by('month')
        )

        # Prepare data for monthly chart
        months = {}
        for report in monthly_reports:
            month_str = report['month'].strftime('%Y-%m')
            if month_str not in months:
                months[month_str] = {'passed': 0, 'failed': 0}

            if report['audit_result']:
                months[month_str]['passed'] = report['count']
            else:
                months[month_str]['failed'] = report['count']

        # Sort months chronologically
        sorted_months = sorted(months.items())
        monthly_labels = json.dumps([month[0] for month in sorted_months])
        monthly_passed = json.dumps([month[1]['passed'] for month in sorted_months])
        monthly_failed = json.dumps([month[1]['failed'] for month in sorted_months])

        # Get recent reports
        recent_reports = reports.order_by('-date')[:10]

        # Add all data to context
        context.update({
            'total_reports': total_reports,
            'passed_reports': passed_reports,
            'failed_reports': failed_reports,
            'passed_percentage': passed_percentage,
            'failed_percentage': failed_percentage,
            'category_count': category_count,
            'category_names': category_names,
            'category_counts': category_counts,
            'monthly_labels': monthly_labels,
            'monthly_passed': monthly_passed,
            'monthly_failed': monthly_failed,
            'recent_reports': recent_reports,
        })

        return context


class ScriptReportsDashboardView(LoginRequiredMixin, TemplateView):
    """
    View for displaying the script-generated reports dashboard with statistics and charts.
    """
    template_name = 'audit_reports/script_reports_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get all script-generated reports
        reports = ScriptGeneratedReport.objects.all()
        total_reports = reports.count()

        # Get compliant and non-compliant reports
        compliant_reports = reports.filter(is_compliant=True).count()
        non_compliant_reports = reports.filter(is_compliant=False).count()

        # Calculate percentages
        compliant_percentage = round((compliant_reports / total_reports) * 100) if total_reports > 0 else 0
        non_compliant_percentage = round((non_compliant_reports / total_reports) * 100) if total_reports > 0 else 0

        # Calculate average compliance score
        avg_compliance_score = reports.aggregate(avg_score=models.Avg('compliance_score'))['avg_score']
        avg_compliance_score = round(avg_compliance_score, 1) if avg_compliance_score is not None else 0

        # Get reports by category
        from equipment.models import Category
        categories = Category.objects.annotate(report_count=Count('script_reports'))

        # Prepare data for category chart
        category_names = json.dumps([cat.name for cat in categories if cat.report_count > 0])
        category_counts = json.dumps([cat.report_count for cat in categories if cat.report_count > 0])

        # Add all data to context
        context.update({
            'reports': reports,
            'total_reports': total_reports,
            'compliant_reports': compliant_reports,
            'non_compliant_reports': non_compliant_reports,
            'compliant_percentage': compliant_percentage,
            'non_compliant_percentage': non_compliant_percentage,
            'avg_compliance_score': avg_compliance_score,
            'category_names': category_names,
            'category_counts': category_counts,
        })

        return context


class ScriptReportDetailView(LoginRequiredMixin, DetailView):
    """
    View for displaying script-generated report details.
    """
    model = ScriptGeneratedReport
    template_name = 'audit_reports/script_report_detail.html'
    context_object_name = 'report'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get the report
        report = self.get_object()

        # Get compliance data for this report
        try:
            compliance_data = ComplianceData.objects.filter(report=report).order_by('-date_checked').first()
            context['compliance_data'] = compliance_data

            # If detailed data is available, add it to context
            if compliance_data and compliance_data.detailed_data:
                context['detailed_data'] = compliance_data.detailed_data

            # If categories are available, add them to context
            if compliance_data and compliance_data.categories:
                context['categories'] = compliance_data.categories
        except Exception as e:
            print(f"Error getting compliance data: {str(e)}")

        return context


class ScriptReportCreateView(LoginRequiredMixin, CreateView):
    """
    View for creating a new script-generated report.
    """
    model = ScriptGeneratedReport
    form_class = ScriptGeneratedReportForm
    template_name = 'audit_reports/script_report_form.html'

    def form_valid(self, form):
        """Set the auditor to the current user before saving."""
        form.instance.auditor = self.request.user.profile

        # If an ongoing audit is selected, try to set the client
        ongoing_audit = form.cleaned_data.get('ongoing_audit')
        if ongoing_audit and hasattr(ongoing_audit, 'client_audit_request'):
            # Try to get the client from the audit request
            audit_request = ongoing_audit.client_audit_request
            if audit_request:
                try:
                    # Find the client profile associated with this email
                    from client_portal.models import ClientProfile
                    client_profile = ClientProfile.objects.filter(
                        user__email=audit_request.email
                    ).first()

                    if client_profile:
                        form.instance.client = client_profile
                except Exception as e:
                    print(f"Error setting client: {str(e)}")

        return super().form_valid(form)

    def get_success_url(self):
        """Return the URL to redirect to after successful creation."""
        return reverse('script_report_detail', kwargs={'slug': self.object.slug})


class ScriptReportUpdateView(LoginRequiredMixin, UpdateView):
    """
    View for updating an existing script-generated report.
    """
    model = ScriptGeneratedReport
    form_class = ScriptGeneratedReportForm
    template_name = 'audit_reports/script_report_form.html'

    def get_initial(self):
        """Store the initial status for comparison after form submission."""
        self.initial_status = self.get_object().status
        return super().get_initial()

    def form_valid(self, form):
        """Check if status has changed and handle notifications."""
        # Store the new status for comparison
        new_status = form.cleaned_data.get('status')

        # If status has changed, we'll handle it in the signal handler
        if hasattr(self, 'initial_status') and self.initial_status != new_status:
            # We'll let the signal handler take care of sending notifications
            pass

        return super().form_valid(form)

    def get_success_url(self):
        """Return the URL to redirect to after successful update."""
        return reverse('script_report_detail', kwargs={'slug': self.object.slug})


class AuditProcessInfoView(LoginRequiredMixin, TemplateView):
    """
    View for displaying information about the audit process.
    """
    template_name = 'audit_reports/audit_process_info.html'


class CSVUploadView(LoginRequiredMixin, TemplateView):
    """
    View for uploading and processing CSV files to extract compliance data.
    """
    template_name = 'audit_reports/csv_upload.html'

    def convert_audit_to_script(self, audit_report, user):
        """
        Convert an AuditReport to a ScriptGeneratedReport.

        This method is similar to the convert_audit_report_to_script view function,
        but it's adapted for use within the CSVUploadView class.
        """
        # Check if a script report already exists for this audit report
        existing_report = ScriptGeneratedReport.objects.filter(
            title=f"Converted from Audit Report #{audit_report.id}",
            equipment=audit_report.equipment
        ).first()

        if existing_report:
            return existing_report

        # Get or create an ongoing audit for this equipment
        from audit_process.models import OngoingAudit, Auditor

        # Get or create an auditor for this user
        try:
            # Try to find an auditor with a similar name
            auditor = Auditor.objects.filter(
                name__icontains=user.get_full_name() or user.username
            ).first()

            if not auditor:
                # Create a new auditor if none exists
                auditor = Auditor.objects.create(
                    name=user.get_full_name() or user.username,
                    phone_number=None,
                    address=None
                )
        except Exception as e:
            # Create a default auditor
            auditor = Auditor.objects.create(
                name=user.get_full_name() or user.username
            )

        # Create an ongoing audit
        ongoing_audit = OngoingAudit.objects.create(
            item=audit_report.equipment,
            description=audit_report.description or "Converted from standard audit report",
            auditor=auditor,
            audit_status="P",  # In Progress
            notes=audit_report.findings or "",
            findings=audit_report.findings or ""
        )

        # Get the client profile
        from client_portal.models import ClientProfile
        client_profile = None
        try:
            # Try to find the client by name
            client_profile = ClientProfile.objects.filter(
                user__first_name__icontains=audit_report.client_name.split()[0]
            ).first()
        except Exception:
            pass

        # Create a temporary CSV file for the report
        import tempfile
        import csv
        import os
        from django.core.files import File

        # Create a temporary CSV file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.csv')
        try:
            with open(temp_file.name, 'w', newline='') as f:
                writer = csv.writer(f)
                # Write header
                writer.writerow(['Category', 'Check', 'Result', 'Details'])
                # Write a single row based on audit result
                writer.writerow([
                    'General',
                    'Overall Audit',
                    'Pass' if audit_report.audit_result else 'Fail',
                    audit_report.findings or 'No details provided'
                ])

            # Create the script report
            with open(temp_file.name, 'rb') as f:
                # Get the equipment category
                category = audit_report.equipment.category

                # Create the script report
                script_report = ScriptGeneratedReport.objects.create(
                    title=f"Converted from Audit Report #{audit_report.id}",
                    ongoing_audit=ongoing_audit,
                    client=client_profile,
                    auditor=user.profile,  # Make sure the current user is the auditor
                    equipment=audit_report.equipment,
                    category=category,
                    report_file=File(f, name=f"audit_report_{audit_report.id}.csv"),
                    description=audit_report.description or "Converted from standard audit report",
                    findings=audit_report.findings or "",
                    recommendations="",
                    compliance_score=100 if audit_report.audit_result else 0,
                    is_compliant=audit_report.audit_result,
                    status='approved',  # Set status to approved so it appears in the client dashboard
                    script_name="Manual Audit",
                    script_version="1.0"
                )

                # Create compliance data
                ComplianceData.objects.create(
                    report=script_report,
                    equipment=audit_report.equipment,
                    total_checks=1,
                    passed_checks=1 if audit_report.audit_result else 0,
                    failed_checks=0 if audit_report.audit_result else 1,
                    detailed_data=[{
                        'Category': 'General',
                        'Check': 'Overall Audit',
                        'Result': 'Pass' if audit_report.audit_result else 'Fail',
                        'Details': audit_report.findings or 'No details provided'
                    }],
                    categories={
                        'General': {
                            'total': 1,
                            'passed': 1 if audit_report.audit_result else 0,
                            'score': 100 if audit_report.audit_result else 0
                        }
                    }
                )

                return script_report

        finally:
            # Clean up the temporary file
            if os.path.exists(temp_file.name):
                os.unlink(temp_file.name)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Check if a report_id is provided in the URL
        report_id = self.request.GET.get('report_id')
        initial_data = {}

        if report_id:
            try:
                # Try to get the report
                report = ScriptGeneratedReport.objects.get(id=report_id)
                # Set initial data for the form
                initial_data = {'report': report.id}

                # Add report to context for display
                context['selected_report'] = report
            except (ScriptGeneratedReport.DoesNotExist, ValueError):
                pass

        # Add form to context with initial data if available
        context['form'] = CSVProcessingForm(
            auditor=self.request.user.profile,
            initial=initial_data
        )

        # Get recent compliance data for this auditor's reports
        recent_data = ComplianceData.objects.filter(
            report__auditor=self.request.user.profile
        ).order_by('-date_checked')[:5]

        context['recent_data'] = recent_data

        # Add all available reports to context for the custom select element
        # First, get script-generated reports
        script_reports = ScriptGeneratedReport.objects.filter(
            auditor=self.request.user.profile
        ).order_by('-date_created')

        # If no script reports are found, try to find reports without an auditor
        if not script_reports.exists():
            script_reports = ScriptGeneratedReport.objects.filter(
                auditor__isnull=True
            ).order_by('-date_created')

        # If still no script reports are found and user is superuser, show all script reports
        if not script_reports.exists() and self.request.user.is_superuser:
            script_reports = ScriptGeneratedReport.objects.all().order_by('-date_created')

        # Now, get standard audit reports
        audit_reports = AuditReport.objects.all().order_by('-date')

        # Combine both types of reports
        context['script_reports'] = script_reports
        context['audit_reports'] = audit_reports
        context['all_reports'] = list(script_reports) + list(audit_reports)

        return context

    def post(self, request, *args, **kwargs):
        form = CSVProcessingForm(request.POST, request.FILES, auditor=request.user.profile)

        if form.is_valid():
            report_value = request.POST.get('report', '')
            csv_file = form.cleaned_data['csv_file']
            process_immediately = form.cleaned_data['process_immediately']

            # Check if the report is a standard audit report or a script-generated report
            if report_value.startswith('audit_'):
                # This is a standard audit report, convert it to a script-generated report
                try:
                    audit_report_id = int(report_value.replace('audit_', ''))
                    audit_report = AuditReport.objects.get(id=audit_report_id)

                    # Convert the audit report to a script-generated report
                    script_report = self.convert_audit_to_script(audit_report, request.user)
                    report = script_report

                    messages.success(
                        request,
                        f'Audit report successfully converted to script report. Processing CSV data for it.'
                    )
                except (ValueError, AuditReport.DoesNotExist) as e:
                    messages.error(request, f'Error converting audit report: {str(e)}')
                    return self.render_to_response(self.get_context_data(form=form))
            elif report_value.startswith('script_'):
                # This is a script-generated report
                try:
                    script_report_id = int(report_value.replace('script_', ''))
                    report = ScriptGeneratedReport.objects.get(id=script_report_id)
                except (ValueError, ScriptGeneratedReport.DoesNotExist) as e:
                    messages.error(request, f'Error finding script report: {str(e)}')
                    return self.render_to_response(self.get_context_data(form=form))
            else:
                # Use the report from the form (backward compatibility)
                report = form.cleaned_data['report']

            # Check if the user has permission to process this report
            if not request.user.is_superuser and request.user.profile != report.auditor:
                messages.error(request, 'You do not have permission to process this report.')
                return self.render_to_response(self.get_context_data(form=form))

            # Process the CSV file if requested
            if process_immediately:
                try:
                    # Use pandas to read the CSV file
                    df = pd.read_csv(csv_file)

                    # Basic validation
                    if df.empty:
                        messages.error(request, 'The CSV file is empty.')
                        return self.render_to_response(self.get_context_data(form=form))

                    # Extract compliance data
                    total_checks = len(df)
                    passed_checks = df[df['Result'] == 'Pass'].shape[0] if 'Result' in df.columns else 0
                    failed_checks = df[df['Result'] == 'Fail'].shape[0] if 'Result' in df.columns else 0

                    # Extract categories if available
                    categories = {}
                    if 'Category' in df.columns:
                        for category in df['Category'].unique():
                            category_df = df[df['Category'] == category]
                            category_total = len(category_df)
                            category_passed = category_df[category_df['Result'] == 'Pass'].shape[0]
                            categories[category] = {
                                'total': category_total,
                                'passed': category_passed,
                                'score': (category_passed / category_total * 100) if category_total > 0 else 0
                            }

                    # Handle NaN values in the DataFrame
                    df = df.fillna('N/A')  # Replace all NaN values with 'N/A'

                    # Create or update compliance data
                    compliance_data, created = ComplianceData.objects.update_or_create(
                        report=report,
                        equipment=report.equipment,
                        defaults={
                            'total_checks': total_checks,
                            'passed_checks': passed_checks,
                            'failed_checks': failed_checks,
                            'detailed_data': df.to_dict(orient='records'),
                            'categories': categories
                        }
                    )

                    # Calculate compliance score
                    compliance_score = compliance_data.calculate_compliance_score()

                    # Show success message
                    if created:
                        messages.success(
                            request,
                            f'CSV file processed successfully. Compliance score: {compliance_score:.1f}%'
                        )
                    else:
                        messages.success(
                            request,
                            f'Compliance data updated successfully. New compliance score: {compliance_score:.1f}%'
                        )

                    # Send notification to client
                    if report.client:
                        from client_portal.models import ClientNotification

                        # Create a more detailed message
                        message = (
                            f"Your compliance data for {report.equipment.name} has been updated.\n\n"
                            f"New compliance score: {compliance_score:.1f}%\n"
                            f"Total checks: {total_checks}\n"
                            f"Passed checks: {passed_checks}\n"
                            f"Failed checks: {failed_checks}\n\n"
                        )

                        # Add category information if available
                        if categories:
                            message += "Category breakdown:\n"
                            for category, data in categories.items():
                                message += f"- {category}: {data['score']:.1f}% ({data['passed']}/{data['total']})\n"

                        # Add compliance status
                        if compliance_score >= 70:
                            status = "Compliant"
                        elif compliance_score >= 50:
                            status = "Needs Improvement"
                        else:
                            status = "Non-Compliant"

                        message += f"\nOverall status: {status}"

                        # Create the notification
                        ClientNotification.objects.create(
                            client=report.client,
                            notification_type='audit_report',
                            title=f"Compliance Data Updated: {report.title}",
                            message=message
                        )

                        # Update the report status to ensure it appears in the client dashboard
                        if report.status != 'completed':
                            report.status = 'completed'
                            report.save(update_fields=['status'])

                        # Show a more detailed success message to the auditor
                        messages.success(
                            request,
                            f'CSV file processed successfully. Compliance score: {compliance_score:.1f}%. '
                            f'A notification has been sent to the client ({report.client.user.email}).'
                        )

                    # Redirect to report detail page
                    return redirect('script_report_detail', slug=report.slug)

                except Exception as e:
                    messages.error(request, f'Error processing CSV file: {str(e)}')
                    return self.render_to_response(self.get_context_data(form=form))

            # If not processing immediately, just save the file and redirect
            messages.success(request, 'CSV file uploaded successfully.')
            return redirect('script_report_detail', slug=report.slug)

        # If form is invalid, show errors
        return self.render_to_response(self.get_context_data(form=form))


@login_required
def get_client_equipment(request, client_id):
    """
    API endpoint to get equipment for a specific client.

    This view returns a JSON response with equipment that belongs to the client
    or is associated with the client's audit requests.
    """
    try:
        # Get the client
        client = ClientProfile.objects.get(id=client_id)

        # Get equipment from ongoing audits
        from audit_process.models import OngoingAudit

        # Get equipment from ongoing audits
        equipment_ids = []

        # Get ongoing audits for this client
        ongoing_audits = OngoingAudit.objects.filter(
            client_audit_request__email=client.user.email
        )

        # Add equipment from these ongoing audits
        for audit in ongoing_audits:
            equipment_ids.append(audit.item.id)

        # Also try to find ongoing audits by client name
        client_name = client.user.get_full_name()
        # Get audit requests for this client by name
        from audit_requests.models import AuditRequest
        client_requests = AuditRequest.objects.filter(
            institution_name__icontains=client_name
        )

        # Get ongoing audits for these requests
        for request in client_requests:
            request_audits = OngoingAudit.objects.filter(client_audit_request=request)
            for audit in request_audits:
                equipment_ids.append(audit.item.id)

        # Use equipment IDs from ongoing audits
        all_equipment_ids = list(set(equipment_ids))  # Remove duplicates

        # If no equipment found in ongoing audits, try to find some
        if not all_equipment_ids:
            # Get all ongoing audits
            all_ongoing_audits = OngoingAudit.objects.all()

            # Add equipment from all ongoing audits
            for audit in all_ongoing_audits:
                all_equipment_ids.append(audit.item.id)

        # Get equipment details
        from equipment.models import Item
        equipment = Item.objects.filter(id__in=all_equipment_ids).distinct()

        # Prepare response data
        equipment_data = [
            {
                'id': item.id,
                'name': item.name,
                'category': item.category.name if item.category else 'Uncategorized'
            }
            for item in equipment
        ]

        # Add contact number if available
        contact_number = client.phone or ''

        return JsonResponse({
            'success': True,
            'equipment': equipment_data,
            'contact_number': contact_number
        })

    except ClientProfile.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'Client not found.'
        }, status=404)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error: {str(e)}'
        }, status=500)


@login_required
def convert_audit_report_to_script_report(request, report_id):
    """
    Convert an AuditReport to a ScriptGeneratedReport.

    This view takes an existing AuditReport and creates a new ScriptGeneratedReport
    based on its data, allowing CSV uploads for compliance data.
    """
    try:
        # Get the audit report
        audit_report = AuditReport.objects.get(id=report_id)

        # Check if a script report already exists for this audit report
        existing_report = ScriptGeneratedReport.objects.filter(
            title=f"Converted from Audit Report #{audit_report.id}",
            equipment=audit_report.equipment
        ).first()

        if existing_report:
            messages.info(request, f"A script report already exists for this audit report. You can upload CSV data for it.")
            return redirect(f'/audit-reports/csv-upload/?report_id={existing_report.id}')

        # Get or create an ongoing audit for this equipment
        from audit_process.models import OngoingAudit, Auditor
        from accounts.models import Profile

        # Get the user's profile
        user_profile = request.user.profile

        # Get or create an auditor for this user
        try:
            # Try to find an auditor with a similar name
            auditor = Auditor.objects.filter(
                name__icontains=request.user.get_full_name() or request.user.username
            ).first()

            if not auditor:
                # Create a new auditor if none exists
                auditor = Auditor.objects.create(
                    name=request.user.get_full_name() or request.user.username,
                    phone_number=None,
                    address=None
                )

        except Exception as e:
            # Create a default auditor
            auditor = Auditor.objects.create(
                name=request.user.get_full_name() or request.user.username
            )

        # Check if an ongoing audit already exists for this equipment
        ongoing_audit = OngoingAudit.objects.filter(
            item=audit_report.equipment,
            audit_status="P"  # In Progress
        ).first()

        # Only create a new ongoing audit if one doesn't already exist
        if not ongoing_audit:
            ongoing_audit = OngoingAudit.objects.create(
                item=audit_report.equipment,
                description=audit_report.description or "Converted from standard audit report",
                auditor=auditor,
                audit_status="P",  # In Progress
                notes=audit_report.findings or "",
                findings=audit_report.findings or ""
            )


        # Get the client profile
        from client_portal.models import ClientProfile
        client_profile = None
        try:
            # Try to find the client by name
            # Use client_name field which is the correct field name in the AuditReport model
            client_profile = ClientProfile.objects.filter(
                user__first_name__icontains=audit_report.client_name.split()[0]
            ).first()
        except Exception:
            pass

        # Create a temporary CSV file for the report
        import tempfile
        import csv
        import os
        from django.core.files import File

        # Create a temporary CSV file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.csv')
        try:
            with open(temp_file.name, 'w', newline='') as f:
                writer = csv.writer(f)
                # Write header
                writer.writerow(['Category', 'Check', 'Result', 'Details'])
                # Write a single row based on audit result
                writer.writerow([
                    'General',
                    'Overall Audit',
                    'Pass' if audit_report.audit_result else 'Fail',
                    audit_report.findings or 'No details provided'
                ])

            # Create the script report
            with open(temp_file.name, 'rb') as f:
                # Get the equipment category
                category = audit_report.equipment.category

                # Check if a script report already exists for this equipment
                existing_report = ScriptGeneratedReport.objects.filter(
                    equipment=audit_report.equipment,
                    title=f"Converted from Audit Report #{audit_report.id}"
                ).first()

                if existing_report:
                    # Update the existing report
                    existing_report.report_file = File(f, name=f"audit_report_{audit_report.id}.csv")
                    existing_report.description = audit_report.description or "Converted from standard audit report"
                    existing_report.findings = audit_report.findings or ""
                    existing_report.compliance_score = 100 if audit_report.audit_result else 0
                    existing_report.is_compliant = audit_report.audit_result
                    existing_report.status = 'approved'  # Set status to approved so it appears in the client dashboard
                    existing_report.save()

                    script_report = existing_report
                else:
                    # Create a new script report
                    script_report = ScriptGeneratedReport.objects.create(
                        title=f"Converted from Audit Report #{audit_report.id}",
                        ongoing_audit=ongoing_audit,
                        client=client_profile,
                        auditor=request.user.profile,  # Make sure the current user is the auditor
                        equipment=audit_report.equipment,
                        category=category,
                        report_file=File(f, name=f"audit_report_{audit_report.id}.csv"),
                        description=audit_report.description or "Converted from standard audit report",
                        findings=audit_report.findings or "",
                        recommendations="",
                        compliance_score=100 if audit_report.audit_result else 0,
                        is_compliant=audit_report.audit_result,
                        status='approved',  # Set status to approved so it appears in the client dashboard
                        script_name="Manual Audit",
                        script_version="1.0"
                    )



                # Check if compliance data already exists for this report
                existing_compliance_data = ComplianceData.objects.filter(
                    report=script_report
                ).first()

                if existing_compliance_data:
                    # Update the existing compliance data
                    existing_compliance_data.total_checks = 1
                    existing_compliance_data.passed_checks = 1 if audit_report.audit_result else 0
                    existing_compliance_data.failed_checks = 0 if audit_report.audit_result else 1
                    existing_compliance_data.detailed_data = [{
                        'Category': 'General',
                        'Check': 'Overall Audit',
                        'Result': 'Pass' if audit_report.audit_result else 'Fail',
                        'Details': audit_report.findings or 'No details provided'
                    }]
                    existing_compliance_data.categories = {
                        'General': {
                            'total': 1,
                            'passed': 1 if audit_report.audit_result else 0,
                            'score': 100 if audit_report.audit_result else 0
                        }
                    }
                    existing_compliance_data.save()
                else:
                    # Create new compliance data
                    ComplianceData.objects.create(
                        report=script_report,
                        equipment=audit_report.equipment,
                        total_checks=1,
                        passed_checks=1 if audit_report.audit_result else 0,
                        failed_checks=0 if audit_report.audit_result else 1,
                        detailed_data=[{
                            'Category': 'General',
                            'Check': 'Overall Audit',
                            'Result': 'Pass' if audit_report.audit_result else 'Fail',
                            'Details': audit_report.findings or 'No details provided'
                        }],
                        categories={
                            'General': {
                                'total': 1,
                                'passed': 1 if audit_report.audit_result else 0,
                                'score': 100 if audit_report.audit_result else 0
                            }
                        }
                    )

                messages.success(
                    request,
                    f'Audit report successfully converted to script report. You can now upload CSV data for it.'
                )

                # Redirect to CSV upload page with the new report pre-selected
                # Use the reverse function to get the URL and add the query parameter
                from django.urls import reverse
                csv_upload_url = reverse('csv_upload')
                return redirect(f"{csv_upload_url}?report_id={script_report.id}")

        finally:
            # Clean up the temporary file
            if os.path.exists(temp_file.name):
                os.unlink(temp_file.name)

    except AuditReport.DoesNotExist:
        messages.error(request, 'Audit report not found.')

        return redirect('auditreportlist')
    except Exception as e:
        messages.error(request, f'Error converting audit report: {str(e)}')

        return redirect('auditreportlist')


@login_required
@require_POST
def process_csv_report(request, report_id):
    """
    Process a CSV report file and extract compliance data.

    This view takes a CSV file from a script-generated report,
    processes it to extract compliance data, and stores the results
    in the ComplianceData model.
    """
    try:
        # Get the report
        report = ScriptGeneratedReport.objects.get(id=report_id)

        # Check if the user has permission to process this report
        if not request.user.is_superuser and request.user.profile != report.auditor:
            return JsonResponse({
                'success': False,
                'message': 'You do not have permission to process this report.'
            }, status=403)

        # Get the CSV file
        csv_file = report.report_file

        # Read the CSV file
        try:
            # Use pandas to read the CSV file
            df = pd.read_csv(csv_file.path)

            # Basic validation
            if df.empty:
                return JsonResponse({
                    'success': False,
                    'message': 'The CSV file is empty.'
                }, status=400)

            # Extract compliance data
            # This is a simplified example - adjust based on your actual CSV structure
            total_checks = len(df)
            passed_checks = df[df['Result'] == 'Pass'].shape[0] if 'Result' in df.columns else 0
            failed_checks = df[df['Result'] == 'Fail'].shape[0] if 'Result' in df.columns else 0

            # Extract categories if available
            categories = {}
            if 'Category' in df.columns:
                for category in df['Category'].unique():
                    category_df = df[df['Category'] == category]
                    category_total = len(category_df)
                    category_passed = category_df[category_df['Result'] == 'Pass'].shape[0]
                    categories[category] = {
                        'total': category_total,
                        'passed': category_passed,
                        'score': (category_passed / category_total * 100) if category_total > 0 else 0
                    }

            # Handle NaN values in the DataFrame
            df = df.fillna('N/A')  # Replace all NaN values with 'N/A'

            # Create or update compliance data
            compliance_data, created = ComplianceData.objects.update_or_create(
                report=report,
                equipment=report.equipment,
                defaults={
                    'total_checks': total_checks,
                    'passed_checks': passed_checks,
                    'failed_checks': failed_checks,
                    'detailed_data': df.to_dict(orient='records'),
                    'categories': categories
                }
            )

            # Calculate compliance score
            compliance_score = compliance_data.calculate_compliance_score()

            # Update the report status to ensure it appears in the client dashboard
            if report.status != 'completed':
                report.status = 'completed'
                report.save(update_fields=['status'])

            # Send notification to client
            if report.client:
                from client_portal.models import ClientNotification

                # Create a more detailed message
                message = (
                    f"Your compliance data for {report.equipment.name} has been updated.\n\n"
                    f"New compliance score: {compliance_score:.1f}%\n"
                    f"Total checks: {total_checks}\n"
                    f"Passed checks: {passed_checks}\n"
                    f"Failed checks: {failed_checks}\n\n"
                )

                # Add category information if available
                if categories:
                    message += "Category breakdown:\n"
                    for category, data in categories.items():
                        message += f"- {category}: {data['score']:.1f}% ({data['passed']}/{data['total']})\n"

                # Add compliance status
                if compliance_score >= 70:
                    status = "Compliant"
                elif compliance_score >= 50:
                    status = "Needs Improvement"
                else:
                    status = "Non-Compliant"

                message += f"\nOverall status: {status}"

                # Create the notification
                ClientNotification.objects.create(
                    client=report.client,
                    notification_type='audit_report',
                    title=f"Compliance Data Updated: {report.title}",
                    message=message
                )

            return JsonResponse({
                'success': True,
                'message': 'CSV file processed successfully.',
                'data': {
                    'total_checks': total_checks,
                    'passed_checks': passed_checks,
                    'failed_checks': failed_checks,
                    'compliance_score': compliance_score,
                    'categories': categories
                }
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'Error processing CSV file: {str(e)}'
            }, status=400)

    except ScriptGeneratedReport.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'Report not found.'
        }, status=404)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error: {str(e)}'
        }, status=500)


class WindowsAuditCSVUploadView(LoginRequiredMixin, TemplateView):
    """
    View for uploading and processing Windows audit CSV files.
    """
    template_name = 'audit_reports/windows_audit_csv_upload.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Check if a report_id is provided in the URL
        report_id = self.request.GET.get('report_id')
        initial_data = {}

        if report_id:
            try:
                # Try to get the report
                report = ScriptGeneratedReport.objects.get(id=report_id)
                # Set initial data for the form
                initial_data = {'report': report.id}

                # Add report to context for display
                context['selected_report'] = report
            except (ScriptGeneratedReport.DoesNotExist, ValueError):
                pass

        # Add form to context with initial data if available
        context['form'] = WindowsAuditCSVForm(
            auditor=self.request.user.profile,
            initial=initial_data
        )

        # Get recent compliance data for this auditor's reports
        recent_data = ComplianceData.objects.filter(
            report__auditor=self.request.user.profile
        ).order_by('-date_checked')[:5]

        context['recent_data'] = recent_data

        # Add all available reports to context for the custom select element
        script_reports = ScriptGeneratedReport.objects.filter(
            auditor=self.request.user.profile
        ).order_by('-date_created')

        # If no script reports are found, try to find reports without an auditor
        if not script_reports.exists():
            script_reports = ScriptGeneratedReport.objects.filter(
                auditor__isnull=True
            ).order_by('-date_created')

        # If still no script reports are found and user is superuser, show all script reports
        if not script_reports.exists() and self.request.user.is_superuser:
            script_reports = ScriptGeneratedReport.objects.all().order_by('-date_created')

        context['script_reports'] = script_reports

        return context

    def post(self, request, *args, **kwargs):
        form = WindowsAuditCSVForm(request.POST, request.FILES, auditor=request.user.profile)

        if form.is_valid():
            report_value = request.POST.get('report', '')
            csv_file = form.cleaned_data['csv_file']

            # Get the report
            try:
                if report_value.startswith('script_'):
                    # This is a script-generated report
                    script_report_id = int(report_value.replace('script_', ''))
                    report = ScriptGeneratedReport.objects.get(id=script_report_id)
                else:
                    # Use the report from the form (backward compatibility)
                    report = ScriptGeneratedReport.objects.get(id=int(report_value))
            except (ValueError, ScriptGeneratedReport.DoesNotExist) as e:
                messages.error(request, f'Error finding script report: {str(e)}')
                return self.render_to_response(self.get_context_data(form=form))

            # Check if the user has permission to process this report
            if not request.user.is_superuser and request.user.profile != report.auditor:
                messages.error(request, 'You do not have permission to process this report.')
                return self.render_to_response(self.get_context_data(form=form))

            try:
                # Process the Windows audit CSV file
                # Save the original CSV file to the report
                # First, delete any existing file to ensure it's replaced
                if report.report_file:
                    try:
                        # Get the old file path
                        old_file_path = report.report_file.path
                        print(f"Deleting old file: {old_file_path}")

                        # Delete the file from storage
                        import os
                        if os.path.exists(old_file_path):
                            os.remove(old_file_path)
                    except Exception as e:
                        print(f"Error deleting old file: {str(e)}")

                # Now save the new file
                report.report_file = csv_file
                report.save()
                print(f"New file saved: {report.report_file.path}")

                # Read the CSV data
                csv_file.seek(0)  # Reset file pointer
                csv_data = csv_file.read().decode('utf-8')
                csv_file.seek(0)  # Reset file pointer again

                # Debug output
                print(f"CSV data first 200 chars: {csv_data[:200]}")

                # Parse CSV
                csv_reader = csv.reader(io.StringIO(csv_data))

                # Skip header
                header = next(csv_reader)
                print(f"CSV header: {header}")

                # Process rows
                rows = list(csv_reader)
                total_checks = len(rows)
                print(f"Total rows in CSV: {total_checks}")

                # Count passed and failed checks
                passed_checks = 0
                failed_checks = 0

                # Simple approach to count passed and failed checks
                # We'll look for "conforme" and "non conforme" in any column
                for row in rows:
                    row_str = ' '.join(row).lower()
                    if 'conforme' in row_str and 'non' not in row_str:
                        passed_checks += 1
                    elif 'non conforme' in row_str or 'non-conforme' in row_str:
                        failed_checks += 1

                # If no checks were counted, try a more direct approach
                if passed_checks == 0 and failed_checks == 0 and total_checks > 0:
                    # Try direct string search in the CSV data
                    passed_checks = csv_data.lower().count('"conforme"') + csv_data.lower().count(',conforme,')
                    failed_checks = csv_data.lower().count('"non conforme"') + csv_data.lower().count('"non-conforme"') + csv_data.lower().count(',non conforme,') + csv_data.lower().count(',non-conforme,')

                print(f"Final counts: Total={total_checks}, Passed={passed_checks}, Failed={failed_checks}")

                # Create a simple categories structure
                categories = {
                    "General": {
                        "total": total_checks,
                        "passed": passed_checks,
                        "score": (passed_checks / total_checks * 100) if total_checks > 0 else 0
                    }
                }

                # Create a simple detailed data structure
                detailed_data = []
                for i, row in enumerate(rows):
                    if len(row) >= 2:  # At least ID and description
                        detailed_data.append({
                            'ID': f'Item{i+1}',
                            'Category': 'General',
                            'Check': row[1] if len(row) > 1 else f'Check {i+1}',
                            'Command': row[2] if len(row) > 2 else '',
                            'Expected': row[3] if len(row) > 3 else '',
                            'Actual': row[4] if len(row) > 4 else '',
                            'Result': 'Pass' if i < passed_checks else 'Fail'
                        })

                # Create or update compliance data
                compliance_data, created = ComplianceData.objects.update_or_create(
                    report=report,
                    equipment=report.equipment,
                    defaults={
                        'total_checks': total_checks,
                        'passed_checks': passed_checks,
                        'failed_checks': failed_checks,
                        'detailed_data': detailed_data,
                        'categories': categories
                    }
                )

                # Calculate compliance score
                compliance_score = compliance_data.calculate_compliance_score()

                # Show success message
                messages.success(
                    request,
                    f'Windows audit CSV file processed successfully. Compliance score: {compliance_score:.1f}%'
                )

                # Send notification to client
                if report.client:
                    from client_portal.models import ClientNotification

                    # Create a more detailed message
                    message = (
                        f"Your Windows audit compliance data for {report.equipment.name} has been updated.\n\n"
                        f"New compliance score: {compliance_score:.1f}%\n"
                        f"Total checks: {total_checks}\n"
                        f"Passed checks: {passed_checks}\n"
                        f"Failed checks: {failed_checks}\n\n"
                    )

                    # Add category information
                    if categories:
                        message += "Category breakdown:\n"
                        for category, data in categories.items():
                            message += f"- {category}: {data['score']:.1f}% ({data['passed']}/{data['total']})\n"

                    # Add compliance status
                    if compliance_score >= 70:
                        status = "Compliant"
                    elif compliance_score >= 50:
                        status = "Needs Improvement"
                    else:
                        status = "Non-Compliant"

                    message += f"\nOverall status: {status}"

                    # Create the notification
                    ClientNotification.objects.create(
                        client=report.client,
                        notification_type='audit_report',
                        title=f"Windows Audit Compliance Data Updated: {report.title}",
                        message=message
                    )

                    # Update the report status
                    if report.status != 'completed':
                        report.status = 'completed'
                        report.save(update_fields=['status'])

                    # Show a more detailed success message
                    messages.success(
                        request,
                        f'Windows audit CSV file processed successfully. Compliance score: {compliance_score:.1f}%. '
                        f'A notification has been sent to the client ({report.client.user.email}).'
                    )

                # Redirect to report detail page
                return redirect('script_report_detail', slug=report.slug)

            except Exception as e:
                messages.error(request, f'Error processing Windows audit CSV file: {str(e)}')
                return self.render_to_response(self.get_context_data(form=form))

        # If form is invalid, show errors
        return self.render_to_response(self.get_context_data(form=form))