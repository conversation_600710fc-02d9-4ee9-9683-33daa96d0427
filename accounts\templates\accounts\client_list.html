{% extends 'equipment/base.html' %}

{% block content %}
<!-- Header Section -->
<div class="container my-4">
    <div class="card shadow-sm rounded p-3">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h4 class="display-6 mb-0 text-success">Clients</h4>
            </div>
            <div class="col-md-6 d-flex justify-content-end gap-2">
                <a class="btn btn-success btn-sm rounded-pill shadow-sm" href="{% url 'client_create' %}">
                    <i class="fa-solid fa-plus"></i> Add New Client
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
  <style>
    .table th, .table td {
        text-align: center;
    }
  </style>
    <table class="table table-striped table-bordered">
        <thead class="thead-light">
            <tr>
                <th scope="col">#</th>
                <th scope="col">First Name</th>
                <th scope="col">Last Name</th>
                <th scope="col">Address</th>
                <th scope="col">Email</th>
                <th scope="col">Phone</th>
                <th scope="col">Loyalty Points</th>
                <th scope="col">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for client in clients %}
            <tr>
                <th scope="row">{{ forloop.counter }}</th>
                <td>{{ client.first_name }}</td>
                <td>{{ client.last_name }}</td>
                <td>{{ client.address }}</td>
                <td>{{ client.email }}</td>
                <td>{{ client.phone }}</td>
                <td>{{ client.loyalty_points }}</td>
                <td>
                    <a href="{% url 'client_update' client.pk %}" class="btn btn-outline-primary btn-sm me-2">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <a href="{% url 'client_delete' client.pk %}" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-trash-alt"></i> Delete
                    </a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
