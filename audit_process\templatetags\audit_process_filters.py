from django import template

register = template.Library()

@register.filter
def endswith(value, arg):
    """
    Check if a string ends with the specified suffix.
    """
    if not value:
        return False
    return value.endswith(arg)

@register.filter
def startswith(value, arg):
    """
    Check if a string starts with the specified prefix.
    """
    if not value:
        return False
    return value.startswith(arg)

@register.filter
def lower(value):
    """
    Convert a string to lowercase.
    """
    if not value:
        return value
    return value.lower()
