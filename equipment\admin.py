"""
Module: admin.py

Django admin configurations for managing equipment categories and items.

This module defines the following admin classes:
- CategoryAdmin: Configuration for the equipment Category model in the admin interface.
- ItemAdmin: Configuration for the equipment Item model in the admin interface.
"""

from django.contrib import admin
from .models import Category, Item


class CategoryAdmin(admin.ModelAdmin):
    """
    Admin configuration for the Category model.
    """
    list_display = ('name', 'slug')
    search_fields = ('name',)
    ordering = ('name',)


class ItemAdmin(admin.ModelAdmin):
    """
    Admin configuration for the Item model.
    """
    list_display = (
        'name', 'category', 'expiring_date'
    )
    search_fields = ('name', 'category__name', 'auditor__name')
    list_filter = ('category',)
    ordering = ('name',)


# DeliveryAdmin class removed as the Delivery model is no longer needed


admin.site.register(Category, CategoryAdmin)
admin.site.register(Item, ItemAdmin)
