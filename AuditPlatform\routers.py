"""
Database routers for the AuditPlatform project.
"""


class AuditDBRouter:
    """
    A router to control all database operations on models in the audit_db database.
    """
    
    def db_for_read(self, model, **hints):
        """
        Attempts to read audit_db models go to audit_db.
        """
        if model._meta.app_label == 'audit_process' and hasattr(model._meta, 'db_table'):
            if model._meta.db_table in ['version_os', 'version_point', 'point_controle', 'preuve']:
                return 'audit_db'
        return None

    def db_for_write(self, model, **hints):
        """
        Attempts to write audit_db models go to audit_db.
        """
        if model._meta.app_label == 'audit_process' and hasattr(model._meta, 'db_table'):
            if model._meta.db_table in ['version_os', 'version_point', 'point_controle', 'preuve']:
                return 'audit_db'
        return None

    def allow_relation(self, obj1, obj2, **hints):
        """
        Allow relations if a model in the audit_process app is involved.
        """
        if obj1._meta.app_label == 'audit_process' or obj2._meta.app_label == 'audit_process':
            return True
        return None

    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """
        Make sure the audit_db models only appear in the 'audit_db' database.
        """
        if app_label == 'audit_process' and model_name in ['versionos', 'versionpoint', 'pointcontrole', 'preuve']:
            return db == 'audit_db'
        elif db == 'audit_db':
            return app_label == 'audit_process' and model_name in ['versionos', 'versionpoint', 'pointcontrole', 'preuve']
        return None
