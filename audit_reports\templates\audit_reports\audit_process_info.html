{% extends "equipment/base.html" %}
{% load static %}

{% block title %}Audit Process Information{% endblock title %}

{% block content %}
<div class="container p-5">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="text-success">Audit Process Information</h2>
            <p class="text-muted">Learn about the audit process and how to use audit scripts.</p>
        </div>
    </div>

    <!-- Process Overview -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Audit Process Overview</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <p>The audit process for Windows-based systems follows these steps:</p>
                            <ol class="mb-4">
                                <li class="mb-2">
                                    <strong>Download the appropriate audit script</strong> for the equipment being audited
                                </li>
                                <li class="mb-2">
                                    <strong>Run the script on the target system</strong> to collect configuration and security data
                                </li>
                                <li class="mb-2">
                                    <strong>The script generates a conformity report</strong> in Excel/CSV format with detailed findings
                                </li>
                                <li class="mb-2">
                                    <strong>Upload the generated report</strong> to the platform for analysis and client access
                                </li>
                                <li class="mb-2">
                                    <strong>Review the compliance score</strong> and address any identified issues
                                </li>
                            </ol>
                            <p>
                                This automated approach ensures consistent, thorough audits across all systems and provides
                                clients with detailed, actionable information about their equipment's security posture.
                            </p>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="fas fa-laptop-code fa-5x text-success mb-3"></i>
                                <div class="d-flex justify-content-center">
                                    <div class="process-step">
                                        <i class="fas fa-download"></i>
                                        <span>Download</span>
                                    </div>
                                    <div class="process-arrow">→</div>
                                    <div class="process-step">
                                        <i class="fas fa-play"></i>
                                        <span>Run</span>
                                    </div>
                                    <div class="process-arrow">→</div>
                                    <div class="process-step">
                                        <i class="fas fa-upload"></i>
                                        <span>Upload</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Available Scripts -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Available Audit Scripts</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Script Name</th>
                                    <th>Version</th>
                                    <th>Target System</th>
                                    <th>Description</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Windows Server 2019 Audit</td>
                                    <td>1.2.0</td>
                                    <td>Windows Server 2019</td>
                                    <td>Comprehensive security audit for Windows Server 2019 environments</td>
                                    <td>
                                        <a href="#" class="btn btn-sm btn-primary">
                                            <i class="fas fa-download me-1"></i> Download
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Windows 10 Workstation Audit</td>
                                    <td>1.1.5</td>
                                    <td>Windows 10</td>
                                    <td>Security compliance audit for Windows 10 workstations</td>
                                    <td>
                                        <a href="#" class="btn btn-sm btn-primary">
                                            <i class="fas fa-download me-1"></i> Download
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td>SQL Server 2019 Audit</td>
                                    <td>1.0.3</td>
                                    <td>SQL Server 2019</td>
                                    <td>Database security and configuration audit for SQL Server 2019</td>
                                    <td>
                                        <a href="#" class="btn btn-sm btn-primary">
                                            <i class="fas fa-download me-1"></i> Download
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Active Directory Audit</td>
                                    <td>1.3.1</td>
                                    <td>Windows Server AD</td>
                                    <td>Active Directory security and configuration assessment</td>
                                    <td>
                                        <a href="#" class="btn btn-sm btn-primary">
                                            <i class="fas fa-download me-1"></i> Download
                                        </a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- How to Run Scripts -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">How to Run Audit Scripts</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="mb-3">Step 1: Download the Script</h6>
                            <p>
                                Download the appropriate audit script for the equipment you're auditing from the table above.
                                Save it to a location on the target system or a USB drive.
                            </p>
                            
                            <h6 class="mb-3 mt-4">Step 2: Run the Script</h6>
                            <p>
                                Open PowerShell as Administrator on the target system and run the script using the following command:
                            </p>
                            <div class="bg-dark text-light p-3 rounded mb-3">
                                <code>.\AuditScript.ps1 -OutputPath "C:\Audit\Results"</code>
                            </div>
                            <p>
                                The script will collect configuration data, analyze security settings, and generate a report.
                                This process may take several minutes depending on the system.
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="mb-3">Step 3: Upload the Report</h6>
                            <p>
                                Once the script completes, it will generate an Excel/CSV report in the specified output directory.
                                Upload this report to the platform using the "Upload New Script Report" button on the Script Reports dashboard.
                            </p>
                            
                            <h6 class="mb-3 mt-4">Step 4: Review Results</h6>
                            <p>
                                After uploading, the system will analyze the report and calculate a compliance score.
                                Review the findings and recommendations to identify security issues that need to be addressed.
                            </p>
                            <p>
                                The report will be available to clients through their portal, providing them with detailed
                                information about their equipment's security posture.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- FAQ Section -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Frequently Asked Questions</h5>
                </div>
                <div class="card-body">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faqOne">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                    Do I need administrative privileges to run the audit scripts?
                                </button>
                            </h2>
                            <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="faqOne" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Yes, the audit scripts require administrative privileges to access system configuration settings, security policies, and other protected information. Run PowerShell as Administrator before executing the scripts.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faqTwo">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                    Are the audit scripts safe to run in production environments?
                                </button>
                            </h2>
                            <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="faqTwo" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Yes, the audit scripts are designed to be read-only and do not make any changes to system configurations. They only collect and analyze data without modifying any settings. However, as a best practice, consider running audits during off-peak hours.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faqThree">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                    What information is collected by the audit scripts?
                                </button>
                            </h2>
                            <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="faqThree" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    The audit scripts collect information about system configurations, security settings, installed software, patch levels, user accounts, group policies, firewall rules, and other security-relevant information. No personal data or content of files is collected.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faqFour">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                                    How often should I run these audits?
                                </button>
                            </h2>
                            <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="faqFour" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    We recommend running audits quarterly or after significant system changes, such as major updates, new software installations, or configuration changes. Regular audits help ensure ongoing compliance and security.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .process-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 10px;
        border-radius: 5px;
        background-color: #f8f9fa;
        width: 80px;
    }
    
    .process-step i {
        font-size: 24px;
        margin-bottom: 5px;
        color: #28a745;
    }
    
    .process-arrow {
        display: flex;
        align-items: center;
        padding: 0 10px;
        font-size: 20px;
        color: #6c757d;
    }
</style>
{% endblock content %}
