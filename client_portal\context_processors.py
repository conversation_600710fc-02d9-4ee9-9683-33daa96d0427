from .models import ClientProfile, ClientNotification

def client_notifications(request):
    """
    Context processor to add unread notifications count to all templates.
    """
    context = {
        'unread_notifications_count': 0
    }
    
    if request.user.is_authenticated:
        try:
            client_profile = request.user.client_profile
            context['unread_notifications_count'] = ClientNotification.objects.filter(
                client=client_profile,
                is_read=False
            ).count()
        except (ClientProfile.DoesNotExist, AttributeError):
            pass
    
    return context
