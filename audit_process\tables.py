import django_tables2 as tables
from .models import AuditRequest, OngoingAudit


class AuditRequestTable(tables.Table):
    class Meta:
        model = AuditRequest
        template_name = "django_tables2/semantic.html"
        fields = (
            'id',
            'customer',
            'date_added',
            'total'
        )
        order_by_field = 'sort'


class OngoingAuditTable(tables.Table):
    class Meta:
        model = OngoingAudit
        template_name = "django_tables2/semantic.html"
        fields = (
            'item',
            'auditor',  # Renamed from vendor for clarity
            'start_date',
            'completion_date',
            'audit_status'
        )
        order_by_field = 'sort'
