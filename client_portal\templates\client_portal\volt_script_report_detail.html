{% extends "client_portal/volt_base.html" %}
{% load static %}

{% block title %}{{ report.title }} - Compliance Report{% endblock title %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'client_dashboard' %}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'client_audit_report_list' %}">Audit Reports</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ report.title }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">{{ report.title }}</h4>
                    <div>
                        <span class="badge {% if report.is_compliant %}bg-success{% else %}bg-danger{% endif %} fs-6">
                            {% if report.is_compliant %}Compliant{% else %}Non-Compliant{% endif %}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Compliance Score</h5>
                            <div class="progress" style="height: 25px;">
                                <div class="progress-bar {% if report.compliance_score >= 70 %}bg-success{% elif report.compliance_score >= 50 %}bg-warning{% else %}bg-danger{% endif %}"
                                     role="progressbar"
                                     style="width: {{ report.compliance_score }}%;"
                                     aria-valuenow="{{ report.compliance_score }}"
                                     aria-valuemin="0"
                                     aria-valuemax="100">
                                    {{ report.compliance_score }}%
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>Report Date</h5>
                            <p class="fs-5">{{ report.date_created|date:"F d, Y" }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Compliance Data -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Compliance Data</h5>
                </div>
                {% if compliance_data %}
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Total Checks</h5>
                                    <h2 class="display-4">{{ compliance_data.total_checks }}</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Passed Checks</h5>
                                    <h2 class="display-4 text-success">{{ compliance_data.passed_checks }}</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Failed Checks</h5>
                                    <h2 class="display-4 text-danger">{{ compliance_data.failed_checks }}</h2>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Download CSV Data Button -->
                    <div class="row mb-4">
                        <div class="col-12 text-center">
                            <a href="{{ report.report_file.url }}" download class="btn btn-primary">
                                <i class="fas fa-download me-2"></i> Download CSV Data
                            </a>
                            <p class="text-muted mt-2">Download the raw CSV data used to generate this compliance report.</p>
                        </div>
                    </div>

                    <!-- Category Breakdown -->
                    {% if categories %}
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>Category Breakdown</h5>
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Category</th>
                                        <th>Total Checks</th>
                                        <th>Passed Checks</th>
                                        <th>Compliance Score</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for category, data in categories.items %}
                                    <tr>
                                        <td>{{ category }}</td>
                                        <td>{{ data.total }}</td>
                                        <td>{{ data.passed }}</td>
                                        <td>
                                            <div class="progress" style="height: 8px; width: 120px;">
                                                <div class="progress-bar {% if data.score >= 70 %}bg-success{% elif data.score >= 50 %}bg-warning{% else %}bg-danger{% endif %}"
                                                     role="progressbar"
                                                     style="width: {{ data.score }}%;"
                                                     aria-valuenow="{{ data.score }}"
                                                     aria-valuemin="0"
                                                     aria-valuemax="100">
                                                </div>
                                            </div>
                                            <span class="ml-2">{{ data.score|floatformat:1 }}%</span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Detailed Data -->
                    {% if detailed_data %}
                    <div class="row">
                        <div class="col-12">
                            <h5>Detailed Compliance Data</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Description</th>
                                            <th>Command Executed</th>
                                            <th>Expected Output</th>
                                            <th>Actual Output</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in detailed_data %}
                                        <tr>
                                            <td>{{ item.ID }}</td>
                                            <td>{{ item.Check }}</td>
                                            <td>{{ item.Command }}</td>
                                            <td>{{ item.Expected }}</td>
                                            <td>{{ item.Actual }}</td>
                                            <td>
                                                {% if item.Result == 'Pass' %}
                                                    <span class="badge bg-success">conforme</span>
                                                {% elif item.Result == 'Fail' %}
                                                    <span class="badge bg-danger">non conforme</span>
                                                {% else %}
                                                    {{ item.Result }}
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
                {% else %}
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> No Compliance Data Available</h5>
                        <p>No detailed compliance data is available for this report yet. Compliance data will be available once the auditor uploads the CSV file.</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock content %}
