{% extends "equipment/base.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% block title %}Create Audit Report{%endblock title%}

{% block content %}
<div class="col container p-5">
    <div class="row">
        <div class="col-md-3 col-lg-3"></div>
        <div class="col-md-6 col-lg-6">
            <form method="POST" enctype="multipart/form-data" id="audit-report-form">
                {% csrf_token %}
                <section class="section-bg" id="plans">
                    <fieldset class="form-group">
                        <header class="section-header">
                            <h1 class="text-success">Create Audit Report</h1>
                            <hr>
                        </header>

                        <!-- Client Selection -->
                        <div class="form-group mb-3">
                            <label for="{{ form.client.id_for_label }}">{{ form.client.label }}</label>
                            {{ form.client }}
                            {% if form.client.help_text %}
                                <small class="form-text text-muted">{{ form.client.help_text }}</small>
                            {% endif %}
                            {% if form.client.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.client.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Contact Number -->
                        <div class="form-group mb-3">
                            <label for="{{ form.contact_number.id_for_label }}">{{ form.contact_number.label }}</label>
                            {{ form.contact_number }}
                            {% if form.contact_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.contact_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Equipment Selection -->
                        <div class="form-group mb-3">
                            <label for="{{ form.equipment.id_for_label }}">{{ form.equipment.label }}</label>
                            {{ form.equipment }}
                            <small class="form-text text-muted">
                                {{ form.equipment.help_text }}
                            </small>
                            {% if form.equipment.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.equipment.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Audit Result -->
                        <div class="form-check mb-3">
                            {{ form.audit_result }}
                            <label class="form-check-label" for="{{ form.audit_result.id_for_label }}">
                                {{ form.audit_result.label }}
                            </label>
                            {% if form.audit_result.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.audit_result.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Description -->
                        <div class="form-group mb-3">
                            <label for="{{ form.description.id_for_label }}">{{ form.description.label }}</label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.description.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Findings -->
                        <div class="form-group mb-3">
                            <label for="{{ form.findings.id_for_label }}">{{ form.findings.label }}</label>
                            {{ form.findings }}
                            {% if form.findings.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.findings.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </fieldset>
                </section>
                <div class="form-group mt-4 text-center">
                    <button class="btn btn-success" type="submit">Submit</button>
                    <a href="{% url 'auditreportlist' %}" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
        <div class="col-md-3 col-lg-3"></div>
    </div>
</div>
{% endblock content %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const clientSelect = document.getElementById('client-select');
        const contactNumberInput = document.querySelector('input[name="contact_number"]');

        // Function to update contact number based on selected client
        function updateContactNumber() {
            const clientId = clientSelect.value;

            if (!clientId) {
                return;
            }

            // Fetch client data
            fetch(`/audit-reports/api/client/${clientId}/equipment/`)
                .then(response => response.json())
                .then(data => {
                    // Update contact number if available
                    if (data.contact_number && !contactNumberInput.value) {
                        contactNumberInput.value = data.contact_number;
                    }
                })
                .catch(error => {
                    console.error('Error fetching client data:', error);
                });
        }

        // Update contact number when client changes
        if (clientSelect) {
            clientSelect.addEventListener('change', updateContactNumber);

            // If client is already selected, update contact number
            if (clientSelect.value) {
                updateContactNumber();
            }
        }
    });
</script>
{% endblock %}