{% extends "admin/base.html" %}
{% load static %}

{% block title %}{{ title }} | Audit Management System{% endblock %}

{% block extrastyle %}
{{ block.super }}
<link href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600;700;800&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
  :root {
    --primary-color: #023047;
    --primary-dark: #01273a;
    --primary-light: #034066;
    --secondary-color: #219ebc;
    --secondary-dark: #1a7e96;
    --accent-color: #ffb703;
    --accent-dark: #e6a503;
    --text-color: #fff;
    --text-muted: rgba(255, 255, 255, 0.7);
    --link-color: #219ebc;
    --link-hover-color: #ffb703;
    --border-color: #e0e0e0;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --error-color: #e74c3c;
  }

  body {
    font-family: 'Nunito', sans-serif;
    color: #333;
    background-color: #f5f7fa;
  }

  /* Header styling */
  #header {
    background: var(--primary-color);
    color: var(--text-color);
    height: auto;
    padding: 15px 40px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  #branding h1 {
    font-weight: 800;
    font-size: 22px;
    letter-spacing: 0.5px;
  }

  #branding h1 a:link, #branding h1 a:visited {
    color: var(--text-color);
    transition: color 0.3s;
  }

  #branding h1 a:hover {
    color: var(--accent-color);
    text-decoration: none;
  }

  #user-tools {
    font-size: 14px;
    font-weight: 600;
    padding-top: 5px;
  }

  #user-tools a {
    color: var(--text-muted);
    border-bottom: 1px solid transparent;
    transition: all 0.3s;
    padding: 5px 8px;
    border-radius: 4px;
  }

  #user-tools a:hover {
    color: var(--accent-color);
    background-color: rgba(255, 255, 255, 0.1);
    border-bottom-color: var(--accent-color);
    text-decoration: none;
  }

  /* Module styling */
  .module {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: none;
    margin-bottom: 24px;
    overflow: hidden;
  }

  .module h2, .module caption, .inline-group h2 {
    background: var(--primary-color);
    color: var(--text-color);
    font-weight: 700;
    font-size: 16px;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
  }

  /* Breadcrumbs */
  div.breadcrumbs {
    background: var(--secondary-color);
    color: var(--text-color);
    padding: 15px 40px;
    font-size: 15px;
    font-weight: 600;
  }

  div.breadcrumbs a {
    color: var(--text-color);
    transition: color 0.3s;
  }

  div.breadcrumbs a:hover {
    color: var(--accent-color);
    text-decoration: none;
  }

  /* Buttons */
  .button, input[type=submit], input[type=button], .submit-row input, a.button {
    background: var(--primary-color);
    color: var(--text-color);
    font-weight: 700;
    border-radius: 6px;
    padding: 10px 15px;
    transition: all 0.3s;
  }

  .button:hover, input[type=submit]:hover, input[type=button]:hover, .submit-row input:hover, a.button:hover {
    background: var(--primary-light);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .button.default, input[type=submit].default, .submit-row input.default {
    background: var(--secondary-color);
    font-weight: 700;
  }

  .button.default:hover, input[type=submit].default:hover, .submit-row input.default:hover {
    background: var(--secondary-dark);
  }

  /* Links */
  a:link, a:visited {
    color: var(--link-color);
    transition: color 0.3s;
  }

  a:hover {
    color: var(--link-hover-color);
  }

  /* Object tools */
  .object-tools {
    margin-top: 15px;
  }

  .object-tools a:link, .object-tools a:visited {
    background: var(--secondary-color);
    font-weight: 600;
    border-radius: 6px;
    padding: 8px 12px;
    transition: all 0.3s;
  }

  .object-tools a:hover {
    background: var(--secondary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  /* Changelist filter */
  #changelist-filter {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--border-color);
  }

  #changelist-filter h2 {
    background: var(--primary-color);
    color: var(--text-color);
    font-weight: 700;
    font-size: 16px;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
  }

  #changelist-filter li.selected a {
    color: var(--secondary-color);
    font-weight: 700;
  }

  /* Paginator */
  .paginator {
    margin: 20px 0;
    font-weight: 600;
  }

  .paginator a:link, .paginator a:visited {
    background: var(--primary-color);
    color: var(--text-color);
    border-radius: 4px;
    padding: 6px 12px;
    transition: all 0.3s;
  }

  .paginator a:hover {
    background: var(--primary-light);
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }

  /* Form controls */
  input[type=text], input[type=password], input[type=email], input[type=url], input[type=number], input[type=tel], textarea, select, .vTextField {
    border: 2px solid var(--border-color);
    border-radius: 6px;
    padding: 10px 12px;
    font-size: 14px;
    transition: border-color 0.3s;
  }

  input[type=text]:focus, input[type=password]:focus, input[type=email]:focus, input[type=url]:focus, input[type=number]:focus, input[type=tel]:focus, textarea:focus, select:focus, .vTextField:focus {
    border-color: var(--secondary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(33, 158, 188, 0.2);
  }

  /* Custom logo styling */
  .brand-logo {
    display: inline-block;
    vertical-align: middle;
    margin-right: 12px;
  }

  .brand-logo img {
    height: 32px;
    width: auto;
    filter: brightness(0) invert(1);
  }

  /* Dashboard styling */
  .dashboard .module table th {
    background: var(--primary-color);
    color: var(--text-color);
    font-weight: 700;
    padding: 12px 15px;
  }

  .dashboard .module table td {
    padding: 12px 15px;
  }

  .dashboard .module table td a:link, .dashboard .module table td a:visited {
    color: var(--link-color);
    font-weight: 600;
  }

  .dashboard .module table td a:hover {
    color: var(--link-hover-color);
  }

  /* App list styling */
  .app-list {
    margin-top: 20px;
  }

  .app-list-item {
    margin-bottom: 15px;
  }

  .app-list-item h2 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 10px;
  }

  .app-list-item h2 a {
    color: var(--primary-color);
  }

  .app-list-item h2 a:hover {
    color: var(--secondary-color);
  }

  /* Messages */
  .messagelist {
    margin: 0 0 20px 0;
    padding: 0;
  }

  .messagelist li {
    padding: 12px 15px;
    margin: 0 0 10px 0;
    border-radius: 6px;
    font-weight: 600;
  }

  .messagelist li.success {
    background: var(--success-color);
    color: white;
  }

  .messagelist li.warning {
    background: var(--warning-color);
    color: white;
  }

  .messagelist li.error {
    background: var(--error-color);
    color: white;
  }
</style>
{% endblock %}

{% block branding %}
<div class="brand-logo">
  <img src="{% static 'assets/img/brand/light.svg' %}" alt="Logo">
</div>
<h1 id="site-name">
  <a href="{% url 'admin:index' %}">Audit Management System</a>
  <span style="font-size: 14px; opacity: 0.8; margin-left: 10px; font-weight: 600;">Admin Portal</span>
</h1>
{% endblock %}

{% block nav-global %}{% endblock %}
