<!-- client_form.html -->
{% extends 'equipment/base.html' %}
{% block content %}
  <div class="container m-5">
    <h1 class="mb-4">{% if form.instance.pk %}Edit{% else %}Create{% endif %} Client</h1>
    <form method="post">
      {% csrf_token %}
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="form-group">
            {{ form.first_name.label_tag }}
            {{ form.first_name }}
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            {{ form.last_name.label_tag }}
            {{ form.last_name }}
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-12">
          <div class="form-group">
            {{ form.address.label_tag }}
            {{ form.address }}
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="form-group">
            {{ form.email.label_tag }}
            {{ form.email }}
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            {{ form.phone.label_tag }}
            {{ form.phone }}
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="form-group">
            {{ form.loyalty_points.label_tag }}
            {{ form.loyalty_points }}
          </div>
        </div>
      </div>
      <button type="submit" class="btn btn-primary">
        <i class="fas fa-save"></i> Save
      </button>
    </form>
  </div>
{% endblock %}
