from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from equipment.views import get_categories_api, create_equipment_api

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('equipment.urls')),
    path('staff/', include('accounts.urls')),
    path('audit-process/', include('audit_process.urls')),
    path('accounts/', include('accounts.urls')),
    path('audit-reports/', include('audit_reports.urls')),
    path('audit-requests/', include('audit_requests.urls')),
    path('client/', include('client_portal.urls')),

    # API endpoints for equipment creation
    path('api/categories/', get_categories_api, name='api-categories'),
    path('api/items/create/', create_equipment_api, name='api-create-equipment'),

    # JWT API endpoints
    path('api/auth/', include('api.urls')),
]

# Add media URL pattern
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
