def find_input():
    # Constants
    INITIAL_HASH = 0x84222325
    MULTIPLIER = 0x100000001b3
    TARGET_HASH = 0x80808080
    MOD = 2**64

    # Compute the modular inverse of MULTIPLIER mod 2^64
    inv_multiplier = pow(MULTIPLIER, -1, MOD)

    # We need to reverse the hash computation.
    # Start from TARGET_HASH and work backwards.
    # The input can be up to 16 bytes (since the hash is 64-bit and each byte affects it).

    # Let's try to find a 4-byte input first (since 0x80808080 is 4 bytes).
    # We'll brute-force possible byte sequences.

    from itertools import product

    # Try input lengths from 1 to 8 bytes (arbitrary limit)
    for length in range(1, 9):
        print(f"Trying input length: {length} bytes")
        # Generate all possible byte sequences of this length
        for bytes_seq in product(range(256), repeat=length):
            # Compute the hash
            h = INITIAL_HASH
            for b in bytes_seq:
                h = (h * MULTIPLIER) ^ b
                h &= 0xFFFFFFFFFFFFFFFF  # Ensure 64-bit

            if h == TARGET_HASH:
                # Convert bytes to hex string (2 chars per byte)
                hex_str = ''.join(f"{b:02x}" for b in bytes_seq)
                print(f"Found valid input: {hex_str}")
                return hex_str

    print("No valid input found.")
    return None

# Run the solver
find_input()