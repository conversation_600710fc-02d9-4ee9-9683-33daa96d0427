# Generated by Django 5.1 on 2025-04-24 08:26

import phonenumber_field.modelfields
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0003_alter_auditor_options"),
    ]

    operations = [
        migrations.AlterField(
            model_name="profile",
            name="telephone",
            field=phonenumber_field.modelfields.PhoneNumberField(
                blank=True,
                error_messages={
                    "invalid": "Enter a valid phone number (e.g. +212 *********)."
                },
                max_length=128,
                null=True,
                region=None,
                verbose_name="Telephone",
            ),
        ),
    ]
