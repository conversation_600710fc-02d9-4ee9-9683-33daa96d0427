{% extends 'client_portal/base.html' %}

{% block title %}My Notifications{% endblock %}
{% block page_title %}My Notifications{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h2>My Notifications</h2>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Notifications</h5>
                </div>
                <div class="card-body">
                    {% if notifications %}
                    <div class="list-group">
                        {% for notification in notifications %}
                        <a href="{% url 'client_mark_notification_read' notification.id %}" class="list-group-item list-group-item-action {% if not notification.is_read %}list-group-item-primary{% endif %}">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">{{ notification.title }}</h5>
                                <small>{{ notification.created_at|date:"M d, Y" }}</small>
                            </div>
                            <p class="mb-1">{{ notification.message }}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small>
                                    {% if notification.notification_type == 'audit_request' %}
                                    <span class="badge bg-primary">Audit Request</span>
                                    {% elif notification.notification_type == 'audit_report' %}
                                    <span class="badge bg-info">Audit Report</span>
                                    {% else %}
                                    <span class="badge bg-secondary">System</span>
                                    {% endif %}
                                </small>
                                <small>
                                    {% if notification.is_read %}
                                    <span class="text-muted"><i class="fas fa-check-double me-1"></i> Read</span>
                                    {% else %}
                                    <span class="text-primary"><i class="fas fa-envelope me-1"></i> Unread</span>
                                    {% endif %}
                                </small>
                            </div>
                        </a>
                        {% endfor %}
                    </div>
                    
                    <!-- Pagination -->
                    {% if is_paginated %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </span>
                            </li>
                            {% endif %}
                            
                            {% for i in paginator.page_range %}
                            {% if page_obj.number == i %}
                            <li class="page-item active">
                                <span class="page-link">{{ i }}</span>
                            </li>
                            {% else %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ i }}">{{ i }}</a>
                            </li>
                            {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </span>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    
                    {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted">You don't have any notifications yet.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
