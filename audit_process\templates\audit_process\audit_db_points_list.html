{% extends "equipment/base.html" %}
{% load static %}

{% block title %}Control Points{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>Control Points</h2>
                <div>
                    <a href="{% url 'audit_db_os_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back to OS Versions
                    </a>
                </div>
            </div>
            <p class="text-muted">
                {% if selected_os %}
                OS: <strong>{{ selected_os.nomversion }}</strong>
                {% if selected_version %}
                | Version: <strong>Version Point {{ selected_version.id }}</strong>
                {% endif %}
                {% else %}
                Select an OS version to view available point versions.
                {% endif %}
            </p>
        </div>
    </div>

    <div class="row">
        <!-- OS Versions Sidebar -->
        <div class="col-md-3 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">OS Versions</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% for os in os_versions %}
                        <a href="{% url 'audit_db_points_list_os' os_id=os.idversion %}" class="list-group-item list-group-item-action {% if selected_os.idversion == os.idversion %}active{% endif %}">
                            {{ os.nomversion }}
                        </a>
                        {% endfor %}
                    </div>
                </div>
            </div>

            {% if selected_os and point_versions %}
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Point Versions</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% for version in point_versions %}
                        <a href="{% url 'audit_db_points_list_version' os_id=selected_os.idversion version_id=version.id %}" class="list-group-item list-group-item-action {% if selected_version.id == version.id %}active{% endif %}">
                            Version Point {{ version.id }} - {{ version.idpoint }}
                        </a>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Control Points -->
        <div class="col-md-9">
            {% if selected_version %}
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Control Points for Version Point {{ selected_version.id }}</h5>
                    {% if request.user.is_authenticated and request.user.profile.role == 'AU' %}
                    <div class="dropdown">
                        <button class="btn btn-primary dropdown-toggle" type="button" id="importDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-file-import me-2"></i> Import to Audit
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="importDropdown">
                            {% for audit in request.user.profile.ongoing_audits.all %}
                            {% if audit.audit_type == 'manual' %}
                            <li><a class="dropdown-item" href="{% url 'audit_db_import_points' audit_id=audit.id version_id=selected_version.id %}">{{ audit.item.name }}</a></li>
                            {% endif %}
                            {% empty %}
                            <li><span class="dropdown-item text-muted">No manual audits available</span></li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if control_points %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Description</th>
                                    <th>Command</th>
                                    <th>Expected Result</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for point in control_points %}
                                <tr>
                                    <td><strong>{{ point.idpoint }}</strong></td>
                                    <td>{{ point.descriptionpoint }}</td>
                                    <td>
                                        {% if point.commandeaudit %}
                                        <code>{{ point.commandeaudit }}</code>
                                        {% else %}
                                        <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ point.expectedoutput|default:"N/A" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <p class="mb-0">No control points found for this version. Please contact the administrator to add control points.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% elif selected_os %}
            <div class="alert alert-info">
                <p class="mb-0">Please select a point version from the sidebar to view control points.</p>
            </div>
            {% else %}
            <div class="alert alert-info">
                <p class="mb-0">Please select an OS version from the sidebar to view available point versions.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
