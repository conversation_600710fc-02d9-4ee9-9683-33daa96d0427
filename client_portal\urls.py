from django.urls import path
from . import views

urlpatterns = [
    # Client authentication and profile
    path('register/', views.client_register, name='client_register'),
    path('dashboard/', views.client_dashboard, name='client_dashboard'),
    path('profile/', views.client_profile, name='client_profile'),
    path('password-change/', views.client_password_change, name='client_password_change'),
    path('login/', views.client_login_redirect, name='client_login'),

    # Audit requests
    path('audit-requests/', views.ClientAuditRequestListView.as_view(), name='client_audit_request_list'),
    path('audit-request/<int:pk>/', views.ClientAuditRequestDetailView.as_view(), name='client_audit_request_detail'),
    path('audit-request/create/', views.create_audit_request, name='client_create_audit_request'),

    # Audit reports
    path('audit-reports/', views.ClientAuditReportListView.as_view(), name='client_audit_report_list'),
    path('audit-report/<slug:slug>/', views.ClientAuditReportDetailView.as_view(), name='client_audit_report_detail'),
    path('script-report/<slug:slug>/', views.ClientScriptReportDetailView.as_view(), name='client_script_report_detail'),

    # Notifications
    path('notifications/', views.ClientNotificationListView.as_view(), name='client_notifications'),
    path('notification/<int:pk>/read/', views.mark_notification_as_read, name='client_mark_notification_read'),
]
