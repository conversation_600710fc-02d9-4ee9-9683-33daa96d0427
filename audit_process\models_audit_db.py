"""
Models for the audit_db database.
These models map to existing tables in the audit_db database.
"""

from django.db import models


class VersionOS(models.Model):
    """
    Model representing the version_os table in the audit_db database.
    """
    idversion = models.AutoField(primary_key=True)
    nomversion = models.CharField(max_length=100)

    class Meta:
        managed = False  # Django won't manage this table
        db_table = 'version_os'
        app_label = 'audit_process'
        verbose_name = 'OS Version'
        verbose_name_plural = 'OS Versions'

    def __str__(self):
        return self.nomversion


class VersionPoint(models.Model):
    """
    Model representing the version_point table in the audit_db database.
    """
    id = models.AutoField(primary_key=True)
    idversion = models.ForeignKey(VersionOS, on_delete=models.CASCADE, related_name='version_points', db_column='idversion')
    idpoint = models.CharField(max_length=10)

    class Meta:
        managed = False  # Django won't manage this table
        db_table = 'version_point'
        app_label = 'audit_process'
        verbose_name = 'Point Version'
        verbose_name_plural = 'Point Versions'

    def __str__(self):
        return f"Version Point {self.id} - {self.idpoint}"


class PointControle(models.Model):
    """
    Model representing the point_controle table in the audit_db database.
    """
    idpoint = models.CharField(max_length=255, primary_key=True)
    descriptionpoint = models.TextField()
    niveau = models.CharField(max_length=50, blank=True, null=True)
    commandeaudit = models.TextField(blank=True, null=True)
    remediation = models.TextField(blank=True, null=True)
    remarque = models.TextField(blank=True, null=True)
    expectedoutput = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False  # Django won't manage this table
        db_table = 'point_controle'
        app_label = 'audit_process'
        verbose_name = 'Control Point'
        verbose_name_plural = 'Control Points'

    def __str__(self):
        return f"{self.idpoint} - {self.descriptionpoint[:30]}"


class Preuve(models.Model):
    """
    Model representing the preuve table in the audit_db database.
    """
    id_preuve = models.AutoField(primary_key=True)
    idpoint = models.ForeignKey(PointControle, on_delete=models.CASCADE, related_name='preuves', db_column='idpoint')
    audit_id = models.IntegerField()
    actualoutput = models.TextField(blank=True, null=True)
    statut = models.CharField(
        max_length=20,
        choices=[
            ('conforme', 'Conforme'),
            ('non_conforme', 'Non Conforme'),
            ('not_checked', 'Not Checked')
        ],
        default='not_checked'
    )
    screenshot_path = models.CharField(max_length=255, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    date_creation = models.DateTimeField(auto_now_add=True)

    class Meta:
        managed = False  # Django won't manage this table
        db_table = 'preuve'
        app_label = 'audit_process'
        verbose_name = 'Evidence'
        verbose_name_plural = 'Evidence'

    def __str__(self):
        return f"Evidence for {self.idpoint.idpoint}"
