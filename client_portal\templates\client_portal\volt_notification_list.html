{% extends 'client_portal/volt_base.html' %}

{% block title %}My Notifications{% endblock %}
{% block page_title %}My Notifications{% endblock %}

{% block content %}
<div class="py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow">
                <div class="card-header d-flex flex-row align-items-center flex-0 border-bottom">
                    <div class="d-block">
                        <h2 class="h5 mb-0">Notifications</h2>
                    </div>
                    <div class="d-block ms-auto">
                        <a href="javascript:markAllAsRead()" class="btn btn-sm btn-tertiary">
                            <i class="fas fa-check-double me-2"></i> Mark All as Read
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if notifications %}
                    <div class="list-group list-group-flush">
                        {% for notification in notifications %}
                        <a href="{% url 'client_mark_notification_read' notification.id %}" class="list-group-item list-group-item-action border-bottom {% if not notification.is_read %}bg-light{% endif %}">
                            <div class="row align-items-center">
                                <div class="col-auto">
                                    <div class="icon-shape icon-sm 
                                        {% if notification.notification_type == 'audit_request' %}icon-shape-primary
                                        {% elif notification.notification_type == 'audit_report' %}icon-shape-info
                                        {% else %}icon-shape-secondary{% endif %} 
                                        text-white rounded-circle">
                                        {% if notification.notification_type == 'audit_request' %}
                                        <i class="fas fa-clipboard-list"></i>
                                        {% elif notification.notification_type == 'audit_report' %}
                                        <i class="fas fa-file-alt"></i>
                                        {% else %}
                                        <i class="fas fa-bell"></i>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col ms-n2">
                                    <h4 class="h6 mb-0">
                                        <span class="text-dark">{{ notification.title }}</span>
                                    </h4>
                                    <p class="text-muted small mb-0">{{ notification.message }}</p>
                                    <span class="text-muted small">{{ notification.created_at|date:"M d, Y H:i" }}</span>
                                </div>
                                <div class="col-auto">
                                    {% if not notification.is_read %}
                                    <span class="badge bg-danger">New</span>
                                    {% endif %}
                                </div>
                            </div>
                        </a>
                        {% endfor %}
                    </div>
                    
                    <!-- Pagination -->
                    {% if is_paginated %}
                    <div class="card-footer px-3 border-0 d-flex align-items-center justify-content-center">
                        <nav aria-label="Page navigation">
                            <ul class="pagination mb-0">
                                {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
                                </li>
                                {% endif %}
                                
                                {% for i in paginator.page_range %}
                                {% if page_obj.number == i %}
                                <li class="page-item active">
                                    <a class="page-link" href="#">{{ i }}</a>
                                </li>
                                {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ i }}">{{ i }}</a>
                                </li>
                                {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Next</a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}
                    
                    {% else %}
                    <div class="text-center py-4">
                        <div class="icon icon-shape icon-shape-primary rounded-circle mb-4">
                            <i class="fas fa-bell-slash"></i>
                        </div>
                        <h3 class="fw-extrabold">No Notifications</h3>
                        <p class="text-gray-500 mb-4">You don't have any notifications at the moment.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow">
                <div class="card-header border-bottom d-flex align-items-center justify-content-between">
                    <h2 class="fs-5 fw-bold mb-0">Notification Types</h2>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 col-md-4 mb-4 mb-lg-0">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="icon-shape icon-shape-primary rounded-circle mb-3">
                                        <i class="fas fa-clipboard-list"></i>
                                    </div>
                                    <h5 class="fw-bold">Audit Request</h5>
                                    <p class="text-gray-500">Notifications related to your audit requests, such as status updates.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-4 mb-4 mb-lg-0">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="icon-shape icon-shape-info rounded-circle mb-3">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                    <h5 class="fw-bold">Audit Report</h5>
                                    <p class="text-gray-500">Notifications about new audit reports that have been generated.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-4">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="icon-shape icon-shape-secondary rounded-circle mb-3">
                                        <i class="fas fa-bell"></i>
                                    </div>
                                    <h5 class="fw-bold">System</h5>
                                    <p class="text-gray-500">General system notifications and important announcements.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    function markAllAsRead() {
        // This is a placeholder function
        // In a real implementation, you would make an AJAX call to mark all notifications as read
        alert('This feature is not yet implemented.');
    }
</script>
{% endblock %}
{% endblock %}
