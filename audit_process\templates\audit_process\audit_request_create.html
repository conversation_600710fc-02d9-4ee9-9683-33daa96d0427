{% extends "equipment/base.html" %}
{% load static %}
<!-- Page title  -->
{% block title %}Create Audit Request{% endblock title %}

<!-- Specific Page CSS goes HERE  -->
{% block stylesheets %}
<!--Select2 CSS-->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@1.5.2/dist/select2-bootstrap4.min.css">
{% endblock stylesheets %}

<!-- Page content  -->
{% block content %}
<div class="container py-5">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-3">Create New Audit Request</h2>
            <p class="text-muted">Request an audit for your equipment. Select the items you need audited and specify quantities.</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'audit_request_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>
                Back to Audit Requests
            </a>
        </div>
    </div>

    <!-- Audit Request items and details -->
    <form id="form_audit_request" action="{% url 'audit_request_create' %}" class="auditRequestForm" method="post">
        <div class="row">
            <!-- Left column -->
            <div class="col-lg-8 mb-4">
                <div class="card border-light shadow-sm">
                    <div class="card-header" style="background-color: #023047; color: white;">
                        <h5 class="mb-0">Audit Configuration</h5>
                    </div>
                    <div class="card-body">
                        <h6 class="mb-3">Basic Information</h6>
                        <div class="row mb-4">
                            <div class="col-md-12 mb-3">
                                <label for="audit_title" class="form-label">Audit Title</label>
                                <input type="text" class="form-control" id="audit_title" name="audit_title" placeholder="Enter a title for this audit request">
                            </div>
                            <div class="col-md-12 mb-3">
                                <label for="audit_description" class="form-label">Description</label>
                                <textarea class="form-control" id="audit_description" name="audit_description" rows="3" placeholder="Describe what you need audited"></textarea>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label for="audit_categories" class="form-label">Audit Categories</label>
                                <select class="form-select select2" name="audit_categories" id="audit_categories" multiple aria-label="Select categories">
                                    {% for category in categories %}
                                    <option value="{{ category.id }}">{{ category.name }}</option>
                                    {% endfor %}
                                </select>
                                <small class="text-muted">Select one or more categories that apply to this audit</small>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label for="config_file" class="form-label">Configuration File (Optional)</label>
                                <input type="file" class="form-control" id="config_file" name="config_file">
                                <small class="text-muted">Upload any configuration files needed for the audit</small>
                            </div>
                        </div>

                    </div>
                </div>

                <!-- Equipment Selection Card -->
                <div class="card border-light shadow-sm mt-4">
                    <div class="card-header" style="background-color: #023047; color: white;">
                        <h5 class="mb-0">Equipment to Audit</h5>
                    </div>
                    <div class="card-body">
                        <h6 class="mb-3">Add Equipment</h6>
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="searchbox_items" class="form-label">Item</label>
                                <select class="form-select select2" name="searchbox_items" id="searchbox_items" aria-label="Search items" placeholder="Select an item"></select>
                            </div>
                            <div class="col-md-3">
                                <label for="item_quantity" class="form-label">Quantity</label>
                                <input type="number" class="form-control" id="item_quantity" value="1" min="1" max="100">
                            </div>
                            <div class="col-md-3">
                                <label for="item_price" class="form-label">Price</label>
                                <input type="number" class="form-control" id="item_price" value="0.00" step="0.01" min="0">
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="mb-4">
                            <button type="button" class="btn btn-primary btn-sm me-2" id="add_item">
                                <i class="fas fa-plus me-2"></i> Add Item
                            </button>
                            <button type="button" class="btn btn-danger btn-sm deleteAll">
                                <i class="fas fa-trash-alt me-2"></i> Clear All
                            </button>
                        </div>

                        <!-- Items Table -->
                        <div class="table-responsive my-3">
                            <table class="table table-bordered table-striped" id="table_items">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th>Quantity</th>
                                        <th>Price</th>
                                        <th>Total</th>
                                        <th class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="text-center text-muted">
                                        <td colspan="5">No items added yet</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Left Column -->

            <!-- Right Column -->
            <div class="col-lg-4 mb-4">
                <div class="card border-light shadow-sm">
                    <div class="card-header" style="background-color: #023047; color: white;">
                        <h5 class="mb-0">Summary</h5>
                    </div>
                    <div class="card-body">
                        {% csrf_token %}
                        <!-- Hidden field for client ID -->
                        <input type="hidden" name="customer" id="customer" value="{{ request.user.client_profile.client.id }}">

                        <div class="alert alert-info mb-3">
                            <i class="fas fa-info-circle me-2"></i> Your audit request will be reviewed by our auditors who will either accept or reject it.
                        </div>

                        <div class="card mb-3 border-light">
                            <div class="card-body bg-light">
                                <h6 class="card-title">Equipment Summary</h6>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Items:</span>
                                    <span id="item_count">0</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Subtotal:</span>
                                    <span id="subtotal">$0.00</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Grand Total:</span>
                                    <span id="grand_total_display" class="fw-bold">$0.00</span>
                                    <input name="grand_total" type="hidden" id="grand_total" value="0.00">
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary w-100" style="background-color: #023047;">Submit Audit Request</button>
                    </div>
                </div>
            </div>
            <!-- End Right Column -->
        </div>
    </form>
</div>
{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
<!-- Datatables -->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js" defer></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js" defer></script>

<!-- Select2 -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js" defer></script>

<!-- Bootstrap Touchspin -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-touchspin/3.1.0/jquery.bootstrap-touchspin.min.js" defer></script>

<!-- Sweet Alert -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.6.15/dist/sweetalert2.all.min.js" defer></script>

<script>
    // Source: https://stackoverflow.com/a/32605063
    function roundTo(n, digits) {
        if (digits === undefined) {
            digits = 0;
        }

        var multiplicator = Math.pow(10, digits);
        n = parseFloat((n * multiplicator).toFixed(11));
        return Math.round(n) / multiplicator;
    }

    // Variable to store audit request details and products
    var auditRequest = {
        products: {
            client: $('#customer').val(), // Get client ID from hidden field
            total: 0.00,
            items: []
        },
        calculate_audit_request: function () {
            // Total of all items added
            var total = 0.00;

            // Calculates the total for each item
            $.each(this.products.items, function (pos, dict) {
                dict.pos = pos;
                dict.total_item = roundTo(dict.quantity * dict.price, 2);
                // Add the item total to the audit request total
                total += roundTo(dict.total_item, 2);
            });

            // Update the audit request total
            this.products.total = roundTo(total, 2);

            // Update display values
            $('#item_count').text(this.products.items.length);
            $('#subtotal').text('$' + this.products.total.toFixed(2));
            $('#grand_total_display').text('$' + this.products.total.toFixed(2));
            $('input#grand_total').val(this.products.total);
        },
        // Adds an item to the audit request object
        add_item: function (item) {
            this.products.items.push(item);
            this.list_item();
        },
        // Shows the selected item in the table
        list_item: function () {
            // Calculate the audit request
            this.calculate_audit_request();

            // Clear the table
            $('#table_items tbody').empty();

            // If no items, show the empty message
            if (this.products.items.length === 0) {
                $('#table_items tbody').html('<tr class="text-center text-muted"><td colspan="5">No items added yet</td></tr>');
                return;
            }

            // Add each item to the table
            $.each(this.products.items, function(index, item) {
                var row = '<tr>' +
                    '<td>' + item.name + '</td>' +
                    '<td class="text-center">' + item.quantity + '</td>' +
                    '<td class="text-end">$' + parseFloat(item.price).toFixed(2) + '</td>' +
                    '<td class="text-end">$' + parseFloat(item.total_item).toFixed(2) + '</td>' +
                    '<td class="text-center"><button type="button" class="btn btn-sm btn-danger delete-item" data-index="' + index + '"><i class="fas fa-trash-alt fa-xs"></i></button></td>' +
                    '</tr>';
                $('#table_items tbody').append(row);
            });
        },
    };

    $(document).ready(function () {
        // Select2 items searchbox
        $('#searchbox_items').select2({
            delay: 250,
            placeholder: 'Select an item',
            minimumInputLength: 1,
            allowClear: true,
            templateResult: template_item_searchbox,
            ajax: {
                url: "{% url 'get_items' %}",
                type: 'POST',
                data: function (params) {
                    return {
                        term: params.term,
                        csrfmiddlewaretoken: $('input[name="csrfmiddlewaretoken"]').val()
                    };
                },
                processResults: function (data) {
                    return {
                        results: data
                    };
                }
            }
        });

        // Add item button click
        $('#add_item').on('click', function() {
            var selectedItem = $('#searchbox_items').select2('data')[0];
            if (!selectedItem) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Please select an item first!'
                });
                return;
            }

            // Get quantity and price
            var quantity = parseInt($('#item_quantity').val());
            var price = parseFloat($('#item_price').val());

            // Validate inputs
            if (isNaN(quantity) || quantity < 1) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Please enter a valid quantity (minimum 1)!'
                });
                return;
            }

            if (isNaN(price) || price < 0) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Please enter a valid price!'
                });
                return;
            }

            // Create item object
            var item = {
                id: selectedItem.id,
                name: selectedItem.text,
                quantity: quantity,
                price: price,
                total_item: quantity * price
            };

            // Add the item to the audit request
            auditRequest.add_item(item);

            // Reset form
            $('#searchbox_items').val(null).trigger('change');
            $('#item_quantity').val(1);
            $('#item_price').val('0.00');
        });

        // Delete item button click
        $(document).on('click', '.delete-item', function() {
            var index = $(this).data('index');
            var item = auditRequest.products.items[index];

            Swal.fire({
                customClass: {
                    confirmButton: 'btn btn-danger ms-3',
                    cancelButton: 'btn btn-secondary'
                },
                buttonsStyling: false,
                title: "Remove Item",
                text: "Are you sure you want to remove this item from your audit request?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, remove it',
                cancelButtonText: 'Cancel',
                reverseButtons: true,
            }).then((result) => {
                if (result.isConfirmed) {
                    // Remove the item
                    auditRequest.products.items.splice(index, 1);
                    auditRequest.list_item();
                }
            });
        });

        // Delete all items
        $('.deleteAll').on('click', function () {
            // If there are no items, don't do anything
            if (auditRequest.products.items.length === 0) return false;

            Swal.fire({
                customClass: {
                    confirmButton: 'btn btn-danger ms-3',
                    cancelButton: 'btn btn-secondary'
                },
                buttonsStyling: false,
                title: "Clear Items",
                text: "Are you sure you want to remove all items from your audit request?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, clear all',
                cancelButtonText: 'Cancel',
                reverseButtons: true,
            }).then((result) => {
                // If confirmed
                if (result.isConfirmed) {
                    // Remove all items from the audit request object
                    auditRequest.products.items = [];
                    // Recalculate the audit request
                    auditRequest.list_item();
                }
            });
        });

        // Items datatable
        tblItems = $('#table_items').DataTable({
            columnDefs: [
                {
                    targets: [-1],
                    orderable: false,
                }
            ],
        });

        // Item searchbox templateResult
        function template_item_searchbox(repo) {
            return $(`
                <div class="card mb-3">
                    <div class="card-body">
                        <small class="card-title">${repo.name}</small>
                    </div>
                </div>
            `);
        }

        // Helper functions
        function getAjax(type, url, data, successCallback, errorCallback) {
            $.ajax({
                type: type,
                url: url,
                data: data,
                success: successCallback,
                error: errorCallback
            });
        }

        // Function to round numbers
        function roundTo(value, decimals) {
            const factor = Math.pow(10, decimals);
            return Math.round(value * factor) / factor;
        }

        // On form submit
        $('form#form_audit_request').on('submit', function (event) {
            event.preventDefault();

            // If there are no items, don't submit
            if (auditRequest.products.items.length === 0) {
                Swal.fire({
                    icon: 'error',
                    title: 'No Items Added',
                    text: 'Please add at least one item to your audit request.'
                });
                return false;
            }

            // Create a FormData object to handle file uploads
            var formData = new FormData();

            // Add audit request details
            formData.append('customer', $('#customer').val());
            formData.append('grand_total', $('input#grand_total').val());
            formData.append('title', $('#audit_title').val());
            formData.append('description', $('#audit_description').val());

            // Add categories
            var categories = $('#audit_categories').val();
            if (categories && categories.length > 0) {
                for (var i = 0; i < categories.length; i++) {
                    formData.append('categories', categories[i]);
                }
            }

            // Add config file if present
            var configFile = $('#config_file')[0].files[0];
            if (configFile) {
                formData.append('config_file', configFile);
            }

            // Add items
            formData.append('items', JSON.stringify(auditRequest.products.items));

            // Get CSRF token
            var csrftoken = $('input[name="csrfmiddlewaretoken"]').val();

            // Submit the form data via AJAX
            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRFToken': csrftoken
                },
                data: formData,
                success: function (response) {
                    // Handle success response
                    auditRequest.products.items = [];
                    auditRequest.list_item();
                    $('form#form_audit_request').trigger('reset');
                    Swal.fire({
                        title: 'Success!',
                        text: 'Your audit request has been submitted successfully.',
                        icon: 'success',
                        confirmButtonText: 'View Audit Requests',
                        confirmButtonColor: '#023047',
                    }).then(function () {
                        // Redirect to the audit request list
                        location.href = "{% url 'audit_request_list' %}";
                    });
                },
                error: function (xhr) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: xhr.responseJSON?.message || 'There was a problem submitting your audit request. Please try again.'
                    });
                }
            });
        });
    });
</script>

{% endblock javascripts %}
