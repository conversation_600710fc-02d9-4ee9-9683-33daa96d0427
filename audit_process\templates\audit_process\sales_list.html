{% extends "equipment/base.html" %}
{% load static %}
{% load render_table from django_tables2 %}
{% load querystring from django_tables2 %}
{% block title %}Sales{% endblock title %}
{% block content %}
<!-- Header Section -->
<div class="container my-4">
    <div class="card shadow-sm rounded p-3">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h4 class="display-6 mb-0 text-success">Sales</h4>
            </div>
            <div class="col-md-6 d-flex justify-content-end gap-2">
                <a class="btn btn-success btn-sm rounded-pill shadow-sm" href="{% url 'sale-create' %}">
                    <i class="fa-solid fa-plus"></i> Add Sale Order
                </a>
                <a class="btn btn-primary btn-sm rounded-pill shadow-sm" href="{% url 'sales-export' %}">
                    <i class="fa-solid fa-download"></i> Export to Excel
                </a>
            </div>
        </div>
    </div>
</div>
<div class="container px-3">
    <style>
        .table th, .table td {
            text-align: center;
        }
        .btn-custom {
            padding: 8px 20px;
            margin: 4px;
        }
    </style>
    <table class="table table-bordered table-striped table-hover table-sm">
        <thead class="thead-light">
            <tr>
                <th>ID</th>
                <th>Date</th>
                <th>Client</th>
                <th>Sub Total</th>
                <th>Grand Total</th>
                <th>Tax Amount</th>
                <th>Tax Percentage</th>
                <th>Amount Paid</th>
                <th>Amount Change</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for sale in sales %}
            <tr>
                <td>{{ sale.id }}</td>
                <td>{{ sale.date_added|date:"Y-m-d H:i:s" }}</td>
                <td>{{ sale.Client }}</td>
                <td>{{ sale.sub_total }}</td>
                <td>{{ sale.grand_total }}</td>
                <td>{{ sale.tax_amount }}</td>
                <td>{{ sale.tax_percentage }}</td>
                <td>{{ sale.amount_paid }}</td>
                <td>{{ sale.amount_change }}</td>
                <td>
                    <a class="btn btn-outline-info btn-sm" href="{% url 'sale-detail' sale.id %}">
                        <i class="fa-solid fa-eye"></i> View Details
                    </a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    <div class="d-flex justify-content-center mt-4">
        {% if is_paginated %}
        <nav aria-label="Page navigation">
            <ul class="pagination">
                {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </span>
                </li>
                {% endif %}
                {% for i in paginator.page_range %}
                {% if page_obj.number == i %}
                <li class="page-item active">
                    <span class="page-link">{{ i }} <span class="sr-only">(current)</span></span>
                </li>
                {% else %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ i }}">{{ i }}</a>
                </li>
                {% endif %}
                {% endfor %}
                {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </span>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>
{% endblock content %}
