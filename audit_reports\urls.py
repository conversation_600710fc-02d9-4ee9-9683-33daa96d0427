# Django core imports
from django.urls import path
from django.conf import settings
from django.conf.urls.static import static

# Local app imports
from .views import (
    AuditReportListView,
    AuditReportDetailView,
    AuditReportCreateView,
    AuditReportUpdateView,
    AuditReportDeleteView,
    ReportsDashboardView,
    ScriptReportsDashboardView,
    ScriptReportDetailView,
    ScriptReportCreateView,
    ScriptReportUpdateView,
    AuditProcessInfoView,
    process_csv_report,
    CSVUploadView,
    WindowsAuditCSVUploadView,
    get_client_equipment,
    convert_audit_report_to_script_report
)

# URL patterns
urlpatterns = [
    # API endpoints
    path(
        'api/client/<int:client_id>/equipment/',
        get_client_equipment,
        name='get_client_equipment'
    ),
    # Equipment Reports Dashboard
    path(
        'reports/',
        ReportsDashboardView.as_view(),
        name='reports_dashboard'
    ),

    # Script-Generated Reports Dashboard
    path(
        'dashboard/',
        ScriptReportsDashboardView.as_view(),
        name='script_reports_dashboard'
    ),

    # Audit Process Information
    path(
        'process-info/',
        AuditProcessInfoView.as_view(),
        name='audit_process_info'
    ),

    # Script-Generated Report URLs
    path(
        'dashboard/report/<slug:slug>/',
        ScriptReportDetailView.as_view(),
        name='script_report_detail'
    ),
    path(
        'dashboard/report/create/',
        ScriptReportCreateView.as_view(),
        name='script_report_create'
    ),
    path(
        'dashboard/report/<slug:slug>/update/',
        ScriptReportUpdateView.as_view(),
        name='script_report_update'
    ),
    path(
        'dashboard/report/<int:report_id>/process-csv/',
        process_csv_report,
        name='process_csv_report'
    ),
    path(
        'dashboard/upload-csv/',
        CSVUploadView.as_view(),
        name='csv_upload'
    ),
    path(
        'dashboard/upload-windows-audit-csv/',
        WindowsAuditCSVUploadView.as_view(),
        name='windows_audit_csv_upload'
    ),

    # Audit Report URLs (legacy)
    path(
        'reports/list/',
        AuditReportListView.as_view(),
        name='auditreportlist'
    ),
    path(
        'report/<slug:slug>/',
        AuditReportDetailView.as_view(),
        name='auditreport-detail'
    ),
    path(
        'new-report/',
        AuditReportCreateView.as_view(),
        name='auditreport-create'
    ),
    path(
        'report/<slug:slug>/update/',
        AuditReportUpdateView.as_view(),
        name='auditreport-update'
    ),
    path(
        'report/<int:pk>/delete/',
        AuditReportDeleteView.as_view(),
        name='auditreport-delete'
    ),
    path(
        'report/<int:report_id>/convert-to-script/',
        convert_audit_report_to_script_report,
        name='convert_audit_report_to_script'
    ),

    # Backward compatibility URLs
    path(
        'invoices/',
        AuditReportListView.as_view(),
        name='invoicelist'
    ),
    path(
        'invoice/<slug:slug>/',
        AuditReportDetailView.as_view(),
        name='invoice-detail'
    ),
    path(
        'new-invoice/',
        AuditReportCreateView.as_view(),
        name='invoice-create'
    ),
    path(
        'invoice/<slug:slug>/update/',
        AuditReportUpdateView.as_view(),
        name='invoice-update'
    ),
    path(
        'invoice/<int:pk>/delete/',
        AuditReportDeleteView.as_view(),
        name='invoice-delete'
    ),
]

# Static media files configuration for development
if settings.DEBUG:
    urlpatterns += static(
        settings.MEDIA_URL,
        document_root=settings.MEDIA_ROOT
    )
