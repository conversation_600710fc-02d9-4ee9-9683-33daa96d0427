# Generated by Django 4.2.21 on 2025-05-13 22:55

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('audit_process', '0005_remove_ongoingaudit_script_file_auditscript_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='ongoingaudit',
            name='audit_type',
            field=models.CharField(choices=[('auto', 'Automated'), ('manual', 'Manual')], default='auto', max_length=10, verbose_name='Audit Type'),
        ),
        migrations.CreateModel(
            name='ManualCompliancePoint',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('point_id', models.CharField(max_length=20, verbose_name='ID')),
                ('description', models.TextField(verbose_name='Description')),
                ('command', models.TextField(blank=True, null=True, verbose_name='Audit Command')),
                ('expected_result', models.TextField(blank=True, null=True, verbose_name='Expected Result')),
                ('actual_result', models.TextField(blank=True, null=True, verbose_name='Actual Result')),
                ('status', models.CharField(choices=[('conforme', 'Conforme'), ('non_conforme', 'Non Conforme'), ('not_checked', 'Not Checked')], default='not_checked', max_length=20, verbose_name='Status')),
                ('date_added', models.DateTimeField(auto_now_add=True, verbose_name='Date Added')),
                ('date_updated', models.DateTimeField(auto_now=True, verbose_name='Date Updated')),
                ('audit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='compliance_points', to='audit_process.ongoingaudit', verbose_name='Audit')),
            ],
            options={
                'verbose_name': 'Manual Compliance Point',
                'verbose_name_plural': 'Manual Compliance Points',
                'db_table': 'manual_compliance_points',
                'ordering': ['point_id'],
            },
        ),
        migrations.CreateModel(
            name='ComplianceEvidence',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='compliance_evidence/', verbose_name='Evidence File')),
                ('description', models.CharField(blank=True, max_length=255, null=True, verbose_name='Description')),
                ('date_added', models.DateTimeField(auto_now_add=True, verbose_name='Date Added')),
                ('compliance_point', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='evidence_files', to='audit_process.manualcompliancepoint', verbose_name='Compliance Point')),
            ],
            options={
                'verbose_name': 'Compliance Evidence',
                'verbose_name_plural': 'Compliance Evidence',
                'db_table': 'compliance_evidence',
                'ordering': ['-date_added'],
            },
        ),
    ]
