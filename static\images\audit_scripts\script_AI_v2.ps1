# --------------------------------------------------------------------------- #
#      Audit de points de conformité Windows – PowerShell 5.x                 #
# --------------------------------------------------------------------------- #

$dbhost   = "localhost"
$port     = 5432
$db       = "audit_db"
$user     = "postgres"
$password = "walid24434"


$apiKey = "AIzaSyB9sFFaszS4vmVQ5MJlSrzQMM1zzuy_yGk"
$uri = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=$apiKey"


Add-Type -Path "C:\Users\<USER>\Downloads\NpgsqlTest\bin\Debug\net9.0\Npgsql.dll"

$osCaption = (Get-CimInstance Win32_OperatingSystem).Caption
Write-Host "`n  Système détecté : $osCaption"

switch -Wildcard ($osCaption) {
    "*Windows 10*"   { $osName = "Windows 10" }
    "*Windows 11*"   { $osName = "Windows 11" }
    "*Windows 7*"    { $osName = "Windows 7"  }
    "*Server 2008*"  { $osName = "Windows Server 2008" }
    "*Server 2012*"  { $osName = "Windows Server 2012" }
    "*Server 2016*"  { $osName = "Windows Server 2016" }
    "*Server 2019*"  { $osName = "Windows Server 2019" }
    "*Server 2022*"  { $osName = "Windows Server 2022" }
    default          { Write‑Error "OS non pris en charge." ; exit 1 }
}

# --------------------------- Requête SQL ------------------------------------
$query = @"
SELECT pc.idpoint,
       pc.descriptionpoint,
       pc.commandeaudit,
       pc.expectedoutput
FROM   point_controle pc
JOIN   version_point vp ON pc.idpoint = vp.idpoint
JOIN   version_os    vo ON vp.idversion = vo.idversion
WHERE  vo.nomversion = '$osName'
ORDER  BY pc.idpoint;
"@

# ------------- Normalisation (fr/en/es + ajouts) ----------------------------
function Normalize-AccountNames([string]$txt) {
    if (!$txt) { return "" }

    $map = @{
        "Administradores"                 = "Administrators"
        "Usuarios"                        = "Users"
        "Operadores de copia de seguridad" = "Backup Operators"
        "Usuarios de escritorio remoto"   = "Remote Desktop Users"
        "SERVICIO LOCAL"                  = "LOCAL SERVICE"
        "Servicio de red"                 = "NETWORK SERVICE"
        "SERVICIO"                        = "SERVICE"
        "Invitado"                        = "Guest"
        "Todos"                           = "Everyone"
        "Window Manager\Window Manager Group" = "Window Manager Group"
        "No One"                          = "No One"
    }

    ($txt -split ',' | ForEach-Object {
        # 1. unicode whitespace, non‑breaking space, zero‑width, etc.
        $clean = ($_ -replace '[\u00A0\u2000-\u200B]')
        # 2. strip BUILTIN\ / NT AUTHORITY\ with either normal or full‑width \
        $clean = $clean -replace '^BUILTIN[\\\uFF3C]' -replace '^NT AUTHORITY[\\\uFF3C]'
        $clean = $clean.Trim()

        $map.ContainsKey($clean) ? $map[$clean] : $clean
    }) -join ', '
}


# -------------------- Export secpol (une seule fois) ------------------------
$tmp = $env:TEMP
$raw = Join-Path $tmp 'secpol_raw.cfg'
$utf = Join-Path $tmp 'secpol_utf8.cfg'

& "$env:SystemRoot\System32\secedit.exe" /export /cfg $raw | Out-Null
Get-Content $raw | Set-Content $utf -Encoding UTF8

# --------------------------- Connexion PGSQL --------------------------------
$conn = [Npgsql.NpgsqlConnection]::new(
        "Host=$dbhost;Port=$port;Username=$user;Password=$password;Database=$db")

$missing = @()
$results = @()

try {
    $conn.Open(); Write-Host " Connecté à PostgreSQL.`n"
    $cmd = [Npgsql.NpgsqlCommand]::new($query, $conn)
    $rd = $cmd.ExecuteReader()

    while ($rd.Read()) {
        $id = $rd['idpoint']
        $desc = $rd['descriptionpoint']
        $cmdAud = $rd['commandeaudit']
        $exp = $rd['expectedoutput']

        Write-Host " [$id] $desc"

        $act = try {
            # --------- cas SecEdit (commande = findstr …) --------------------
            if ($cmdAud -match '^\s*findstr\s+(?<right>Se\w+(Privilege|Right))') {
                $right = $Matches['right']
                if ($right -ieq 'SeSystemTimePrivilege') { $right = 'SeSystemtimePrivilege' }

                # motif exact, insensible aux blancs
                $pattern = '^\s*' + [regex]::Escape($right) + '\s*='
                $match = Select-String -Path $utf -Pattern $pattern

                if ($match) {
                    $sids = ($match.Line -split '=')[1].Trim() -split ','
                    # --- traduction des SID ; on ignore ceux qu'on ne sait pas convertir
                    $names = foreach ($sid in $sids) {
                        $s = $sid.Trim(' *')
                        try {
                            ([System.Security.Principal.SecurityIdentifier]$s).Translate([System.Security.Principal.NTAccount]).Value
                        }
                        catch { continue }   # on n'ajoute rien si la traduction échoue
                    }
                    if ($names) {
                        $names -join ', '
                    }
                    else {
                        $missing += [pscustomobject]@{ idPoint = $id; Right = $right }
                        'Not Found'
                    }
                }
                else {
                    $missing += [pscustomobject]@{ idPoint = $id; Right = $right }
                    'Not Found'
                }
            }
            # ------------- autres commandes (reg query, etc.) ----------------
            else {
                (Invoke-Expression $cmdAud 2>&1 | Out-String).Trim()
            }
        }
        catch { "Erreur : $($_.Exception.Message)" }

        # ---------------------- comparaison ----------------------------------
        $expN = Normalize-AccountNames $exp
        $actN = Normalize-AccountNames $act

        $expS = (($expN -split ',') | % { $_.Trim() } | Sort-Object -Unique) -join ', '
        $actS = (($actN -split ',') | % { $_.Trim() } | Sort-Object -Unique) -join ', '

        # Constructing the prompt.  Added more context and examples within the prompt.
        $promptText = @"
        In a configuration audit context, determine if the 'expected configuration' and the 'actual configuration' represent the same state or outcome, despite potential differences in language, formatting, or the order of elements. Return only 'conforme' if they are equivalent in their functional effect, and 'non conforme' otherwise.

        Expected Configuration: '$expS'
        Actual Configuration: '$actS'

        Here are some examples to help determine equivalence:

        - Example 1 (User Rights - Equivalent):
            Expected Configuration: Administrators, Users
            Actual Configuration: BUILTIN\Administradores, BUILTIN\Usuarios
            Result: conforme

        - Example 2 (User Rights - Not Equivalent):
            Expected Configuration: Administrators
            Actual Configuration: Users
            Result: non conforme

        - Example 3 (Service Status - Equivalent):
            Expected Configuration: Service 'Spooler' is Running
            Actual Configuration: L'état du service 'Spouleur' est : En cours d'exécution
            Result: conforme

        - Example 4 (Firewall Rule - Equivalent):
            Expected Configuration: Firewall Rule 'Allow HTTP' is Enabled
            Actual Configuration: Règle de pare-feu 'Autoriser HTTP' : Activée
            Result: conforme

        Evaluate if the core configuration being audited is consistent between the expected and actual states.  Focus on the meaning and functional outcome, not just the exact text.
"@

        # Log the prompt and the data being sent.  This is CRUCIAL for debugging!
        Write-Host "Prompt being sent to Gemini:"
        Write-Host "--------------------------------------------------------"
        Write-Host $promptText
        Write-Host "--------------------------------------------------------"

        $body = @{
            contents = @(
                @{
                    parts = @(
                        @{
                            text = $promptText
                        }
                    )
                }
            )
        } | ConvertTo-Json -Depth 10  # Explicitly convert to JSON

        # Set the headers for the request
        $headers = @{
            "Content-Type" = "application/json"
        }

        try {
            # Send the POST request to the Gemini API
            $response = Invoke-RestMethod -Uri $uri -Method Post -Headers $headers -Body $body

            # Extract and display the generated text
            $geminiResponse = $response.candidates[0].content.parts[0].text
            Write-Host "Gemini API Response: $geminiResponse" # Echo the response
            $statut = $geminiResponse

        }
        catch {
            # Handle any errors that occur during the API call
            Write-Error "Error communicating with the Gemini API: $($_.Exception.Message)"
            $statut = "API Error" # Set a status for the error case
        }

        $results += [pscustomobject]@{
            idPoint          = $id
            descriptionPoint = $desc
            commandExecuted  = $cmdAud
            expectedOutput   = $expS
            actualOutput     = $actS
            statut           = $statut # Use the $statut variable
        }
    }
    $rd.Close()
}
finally {
    $conn.Close()
    Remove-Item $raw, $utf -EA SilentlyContinue
}

# -------------------- Export CSV --------------------------------------------
$csvMain = ".\audit_result_$($osName -replace ' ', '_').csv"
$csvMiss = ".\missing_privileges_$($osName -replace ' ', '_').csv"

$results | Export-Csv $csvMain -NoTypeInformation -Encoding UTF8
$missing | Export-Csv $csvMiss -NoTypeInformation -Encoding UTF8

Write-Host "`n Résultats            : $csvMain"
Write-Host " Droits introuvables  : $csvMiss"
