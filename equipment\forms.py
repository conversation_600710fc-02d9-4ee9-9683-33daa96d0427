"""
Module: forms.py

Contains Django forms for handling equipment-related data.

This module defines the following form classes:
- ItemForm: Form for creating and updating equipment items.
- CategoryForm: Form for creating and updating equipment categories.
- SolutionTechnologiqueForm: Form for creating and updating technological solutions.
"""

from django import forms
from .models import Item, Category, SolutionTechnologique


class ItemForm(forms.ModelForm):
    """
    Form for creating and updating equipment items.
    """
    class Meta:
        model = Item
        fields = [
            'name',
            'description',
            'category',
            'expiring_date',
            'solution',
            'version',
            'compliance_points'
        ]
        widgets = {
            'expiring_date': forms.DateTimeInput(
                attrs={'type': 'datetime-local'}
            ),
            'description': forms.Textarea(
                attrs={'rows': 3}
            )
        }


class CategoryForm(forms.ModelForm):
    """
    Form for creating and updating equipment categories.
    """
    class Meta:
        model = Category
        fields = ['name']


# DeliveryForm removed as the Delivery model is no longer needed


class SolutionTechnologiqueForm(forms.ModelForm):
    """
    Form for creating and updating technological solutions.
    """
    class Meta:
        model = SolutionTechnologique
        fields = ['name']
