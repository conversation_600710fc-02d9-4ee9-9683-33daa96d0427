<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="title" content="Client Portal - Audit Management System">
    <meta name="author" content="Audit Management System">
    <title>{% block title %}Client Portal{% endblock %}</title>

    {% load static %}

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="{% static 'assets/img/brand/light.svg' %}">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Volt CSS -->
    <link type="text/css" href="{% static 'css/volt.css' %}" rel="stylesheet">

    {% block extra_css %}{% endblock %}
</head>

<body>
    <nav class="navbar navbar-dark navbar-theme-primary px-4 col-12 d-lg-none">
        <a class="navbar-brand me-lg-5" href="{% url 'client_dashboard' %}">
            <img class="navbar-brand-dark" src="{% static 'assets/img/brand/light.svg' %}" alt="Volt logo" />
            <img class="navbar-brand-light" src="{% static 'assets/img/brand/dark.svg' %}" alt="Volt logo" />
        </a>
        <div class="d-flex align-items-center">
            <button class="navbar-toggler d-lg-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
        </div>
    </nav>

    <nav id="sidebarMenu" class="sidebar d-lg-block text-white collapse">
        <div class="sidebar-inner px-4 pt-3">
            <div class="user-card d-flex align-items-center justify-content-between justify-content-md-center pb-4">
                <div class="d-flex align-items-center">
                    <div class="avatar-lg me-4">
                        <img src="{{ user.client_profile.image_url }}" class="card-img-top rounded-circle border-white" alt="Profile Picture">
                    </div>
                    <div class="d-block">
                        <h2 class="h5 mb-3 text-white">{{ user.get_full_name|default:user.username }}</h2>
                        <span class="badge bg-success">Client</span>
                    </div>
                </div>
                <div class="collapse-close d-md-none">
                    <a href="#sidebarMenu" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="true" aria-label="Toggle navigation">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </div>
            <ul class="nav flex-column pt-3 pt-md-0">
                <li class="nav-item mt-3 {% if request.resolver_match.url_name == 'client_dashboard' %}active{% endif %}">
                    <a href="{% url 'client_dashboard' %}" class="nav-link">
                        <span class="sidebar-icon me-3">
                            <i class="fas fa-tachometer-alt"></i>
                        </span>
                        <span class="sidebar-text">Dashboard</span>
                    </a>
                </li>
                <li class="nav-item {% if 'audit_request' in request.resolver_match.url_name %}active{% endif %}">
                    <a href="{% url 'client_audit_request_list' %}" class="nav-link">
                        <span class="sidebar-icon me-3">
                            <i class="fas fa-clipboard-list"></i>
                        </span>
                        <span class="sidebar-text">Audit Requests</span>
                    </a>
                </li>
                <li class="nav-item {% if 'audit_report' in request.resolver_match.url_name %}active{% endif %}">
                    <a href="{% url 'client_audit_report_list' %}" class="nav-link">
                        <span class="sidebar-icon me-3">
                            <i class="fas fa-file-alt"></i>
                        </span>
                        <span class="sidebar-text">Audit Reports</span>
                    </a>
                </li>
                <li class="nav-item {% if request.resolver_match.url_name == 'client_notifications' %}active{% endif %}">
                    <a href="{% url 'client_notifications' %}" class="nav-link d-flex justify-content-between">
                        <span>
                            <span class="sidebar-icon me-3">
                                <i class="fas fa-bell"></i>
                            </span>
                            <span class="sidebar-text">Notifications</span>
                        </span>
                        {% if unread_notifications_count > 0 %}
                        <span class="badge bg-danger ms-1">{{ unread_notifications_count }}</span>
                        {% endif %}
                    </a>
                </li>
                <li class="nav-item {% if request.resolver_match.url_name == 'client_profile' %}active{% endif %}">
                    <a href="{% url 'client_profile' %}" class="nav-link">
                        <span class="sidebar-icon me-3">
                            <i class="fas fa-user-circle"></i>
                        </span>
                        <span class="sidebar-text">Profile</span>
                    </a>
                </li>
                <li role="separator" class="dropdown-divider mt-4 mb-3 border-gray-700"></li>
                <li class="nav-item">
                    <form id="logout-form" action="{% url 'user-logout' %}" method="post" style="display: none;">
                        {% csrf_token %}
                    </form>
                    <a href="javascript:void(0)" onclick="document.getElementById('logout-form').submit();" class="nav-link d-flex align-items-center">
                        <span class="sidebar-icon me-3">
                            <i class="fas fa-sign-out-alt"></i>
                        </span>
                        <span class="sidebar-text">Logout</span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <main class="content">
        <nav class="navbar navbar-top navbar-expand navbar-dashboard navbar-dark ps-0 pe-2 pb-0">
            <div class="container-fluid px-0">
                <div class="d-flex justify-content-between w-100" id="navbarSupportedContent">
                    <div class="d-flex align-items-center">
                        <!-- Search form -->
                        <div class="navbar-search form-inline" id="navbar-search-main">
                            <h4 class="mb-0">{% block page_title %}Client Portal{% endblock %}</h4>
                        </div>
                    </div>
                    <ul class="navbar-nav align-items-center">
                        <li class="nav-item dropdown">
                            <a class="nav-link text-dark notification-bell {% if unread_notifications_count > 0 %}unread{% endif %} dropdown-toggle" data-unread-notifications="{{ unread_notifications_count }}" href="#" role="button" data-bs-toggle="dropdown" data-bs-display="static" aria-expanded="false">
                                <svg class="icon icon-sm text-gray-900" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"></path>
                                </svg>
                            </a>
                            <div class="dropdown-menu dropdown-menu-lg dropdown-menu-center mt-2 py-0">
                                <div class="list-group list-group-flush">
                                    <a href="#" class="text-center text-primary fw-bold border-bottom border-light py-3">Notifications</a>
                                    {% if unread_notifications_count > 0 %}
                                        {% for notification in notifications|slice:":5" %}
                                        <a href="{% url 'client_mark_notification_read' notification.id %}" class="list-group-item list-group-item-action border-bottom">
                                            <div class="row align-items-center">
                                                <div class="col-auto">
                                                    <!-- Avatar -->
                                                    <div class="avatar">
                                                        {% if notification.notification_type == 'audit_request' %}
                                                        <i class="fas fa-clipboard-list"></i>
                                                        {% elif notification.notification_type == 'audit_report' %}
                                                        <i class="fas fa-file-alt"></i>
                                                        {% else %}
                                                        <i class="fas fa-bell"></i>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                                <div class="col ps-0 ms-2">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <h4 class="h6 mb-0 text-small">{{ notification.title }}</h4>
                                                        </div>
                                                        <div class="text-end">
                                                            <small>{{ notification.created_at|timesince }} ago</small>
                                                        </div>
                                                    </div>
                                                    <p class="font-small mt-1 mb-0">{{ notification.message|truncatechars:50 }}</p>
                                                </div>
                                            </div>
                                        </a>
                                        {% endfor %}
                                    {% else %}
                                        <a href="#" class="list-group-item list-group-item-action border-bottom">
                                            <div class="row align-items-center">
                                                <div class="col ps-0 ms-2">
                                                    <p class="font-small mt-1 mb-0 text-center">No new notifications</p>
                                                </div>
                                            </div>
                                        </a>
                                    {% endif %}
                                    <a href="{% url 'client_notifications' %}" class="dropdown-item text-center fw-bold rounded-bottom py-3">
                                        <svg class="icon icon-xxs text-gray-400 me-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        View all
                                    </a>
                                </div>
                            </div>
                        </li>
                        <li class="nav-item dropdown ms-lg-3">
                            <a class="nav-link dropdown-toggle pt-1 px-0" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <div class="media d-flex align-items-center">
                                    <img class="avatar rounded-circle" alt="Image placeholder" src="{{ user.client_profile.image_url }}">
                                    <div class="media-body ms-2 text-dark align-items-center d-none d-lg-block">
                                        <span class="mb-0 font-small fw-bold text-gray-900">{{ user.get_full_name|default:user.username }}</span>
                                    </div>
                                </div>
                            </a>
                            <div class="dropdown-menu dashboard-dropdown dropdown-menu-end mt-2 py-1">
                                <a class="dropdown-item d-flex align-items-center" href="{% url 'client_profile' %}">
                                    <svg class="dropdown-icon text-gray-400 me-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"></path>
                                    </svg>
                                    My Profile
                                </a>
                                <div role="separator" class="dropdown-divider my-1"></div>
                                <form id="dropdown-logout-form" action="{% url 'user-logout' %}" method="post" style="display: none;">
                                    {% csrf_token %}
                                </form>
                                <a class="dropdown-item d-flex align-items-center" href="javascript:void(0)" onclick="document.getElementById('dropdown-logout-form').submit();">
                                    <svg class="dropdown-icon text-danger me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                    </svg>
                                    Logout
                                </a>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Messages -->
        <div class="py-4">
            <div class="dropdown">
                {% if messages %}
                    {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>

        <!-- Content -->
        {% block content %}{% endblock %}

        <footer class="bg-white rounded shadow p-5 mb-4 mt-4">
            <div class="row">
                <div class="col-12 col-md-4 col-xl-6 mb-4 mb-md-0">
                    <p class="mb-0 text-center text-lg-start">© <span class="current-year"></span> <a class="text-primary fw-normal" href="#" target="_blank">Audit Management System</a></p>
                </div>
                <div class="col-12 col-md-8 col-xl-6 text-center text-lg-end">
                    <!-- Add your footer links here if needed -->
                </div>
            </div>
        </footer>
    </main>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Moment JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <!-- Custom JS -->
    <script>
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        })

        // Initialize popovers
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl)
        })

        // Toggle sidebar on mobile
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.querySelector('.navbar-toggler');
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    document.querySelector('.sidebar').classList.toggle('show');
                });
            }
        });
    </script>

    <script>
        // Set current year in footer
        document.querySelector('.current-year').textContent = new Date().getFullYear();
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
