from django.contrib import admin
from .models import AuditRequest as ClientAuditRequest  # Renamed to avoid confusion


@admin.register(ClientAuditRequest)
class AuditRequestAdmin(admin.ModelAdmin):
    """Admin interface for managing Client Audit Request instances."""

    fields = (
        'date',
        'institution_name',
        'phone_number',
        'email',
        'address',
        'description',
        'status'
    )

    list_display = (
        'slug',
        'date',
        'institution_name',
        'phone_number',
        'email',
        'description',
        'status'
    )
