from django import forms
from django.contrib.auth.models import User
from django.contrib.auth.forms import UserCreationForm
from .models import ClientProfile

class ClientRegistrationForm(UserCreationForm):
    """
    Form for client registration.
    """
    email = forms.EmailField(required=True)
    first_name = forms.CharField(max_length=30, required=True)
    last_name = forms.CharField(max_length=30, required=True)
    company_name = forms.CharField(max_length=100, required=True)
    address = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=True)
    phone = forms.CharField(max_length=20, required=True)

    class Meta:
        model = User
        fields = ('username', 'email', 'first_name', 'last_name', 'password1', 'password2')

    def clean_username(self):
        username = self.cleaned_data.get('username')
        if User.objects.filter(username=username).exists():
            raise forms.ValidationError('This username is already taken. Please choose another one.')
        return username

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if User.objects.filter(email=email).exists():
            raise forms.ValidationError('This email is already registered. Please use another email.')
        return email

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']

        if commit:
            user.save()
            # Create client profile
            ClientProfile.objects.create(
                user=user,
                company_name=self.cleaned_data['company_name'],
                address=self.cleaned_data['address'],
                phone=self.cleaned_data['phone']
            )

        return user

class ClientProfileUpdateForm(forms.ModelForm):
    """
    Form for updating client profile.
    """
    first_name = forms.CharField(max_length=30, required=True)
    last_name = forms.CharField(max_length=30, required=True)
    email = forms.EmailField(required=True)

    phone = forms.CharField(
        max_length=20,
        required=False,
        error_messages={
            'invalid': 'Enter a valid phone number (e.g. +212 *********).'
        },
        help_text='Enter a valid phone number (e.g. +212 *********).'
    )

    class Meta:
        model = ClientProfile
        fields = ('company_name', 'address', 'phone', 'profile_picture')
        widgets = {
            'profile_picture': forms.FileInput(attrs={'class': 'form-control'})
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.user:
            self.fields['first_name'].initial = self.instance.user.first_name
            self.fields['last_name'].initial = self.instance.user.last_name
            self.fields['email'].initial = self.instance.user.email

    def save(self, commit=True):
        profile = super().save(commit=False)

        # Update user fields
        user = profile.user
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        user.email = self.cleaned_data['email']

        if commit:
            user.save()
            profile.save()

        return profile

# Temporarily commented out to avoid import errors during migration
# class AuditRequestForm(forms.ModelForm):
#     """
#     Form for creating an audit request.
#     """
#     title = forms.CharField(max_length=200, required=True, help_text="Enter a title for this audit request")
#     description = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False,
#                                 help_text="Describe what you need audited")
#
#     class Meta:
#         model = AuditRequest
#         fields = ['title', 'description', 'categories', 'config_file']
#         widgets = {
#             'categories': forms.SelectMultiple(attrs={'class': 'form-select select2'}),
#             'config_file': forms.FileInput(attrs={'class': 'form-control'})
#         }
#         help_texts = {
#             'categories': 'Select one or more categories that apply to this audit',
#             'config_file': 'Upload any configuration files needed for the audit (optional)'
#         }
#
#     def __init__(self, *args, **kwargs):
#         self.client = kwargs.pop('client', None)
#         super().__init__(*args, **kwargs)
#
#         # Make available items accessible to the template
#         self.available_items = Item.objects.all()
#
# class AuditRequestDetailForm(forms.ModelForm):
#     """
#     Form for creating audit request details.
#     """
#     item = forms.ModelChoiceField(
#         queryset=Item.objects.all(),
#         widget=forms.Select(attrs={'class': 'form-select select2'})
#     )
#     quantity = forms.IntegerField(min_value=1, initial=1)
#
#     class Meta:
#         model = AuditRequestDetail
#         fields = ('item', 'quantity')
