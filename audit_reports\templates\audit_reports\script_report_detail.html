{% extends "equipment/base.html" %}
{% load static %}

{% block title %}{{ report.title }}{% endblock title %}

{% block content %}
<div class="container p-5">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'script_reports_dashboard' %}">Script Reports</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ report.title }}</li>
                </ol>
            </nav>
            <h2 class="text-success">{{ report.title }}</h2>
        </div>
    </div>

    <!-- Report Status and Actions -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="d-flex align-items-center">
                <span class="me-3">Status:</span>
                {% if report.status == 'pending' %}
                    <span class="badge bg-warning">Pending Review</span>
                {% elif report.status == 'approved' %}
                    <span class="badge bg-success">Approved</span>
                {% elif report.status == 'rejected' %}
                    <span class="badge bg-danger">Rejected</span>
                {% elif report.status == 'archived' %}
                    <span class="badge bg-secondary">Archived</span>
                {% endif %}

                <span class="ms-4 me-3">Compliance:</span>
                {% if report.is_compliant %}
                    <span class="badge bg-success">Compliant</span>
                {% else %}
                    <span class="badge bg-danger">Non-Compliant</span>
                {% endif %}

                <span class="ms-4 me-3">Score:</span>
                <div class="progress" style="width: 150px; height: 20px;">
                    <div class="progress-bar {% if report.is_compliant %}bg-success{% else %}bg-danger{% endif %}"
                         role="progressbar"
                         style="width: {{ report.compliance_score }}%;"
                         aria-valuenow="{{ report.compliance_score }}"
                         aria-valuemin="0"
                         aria-valuemax="100">
                        {{ report.compliance_score }}%
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ report.report_file.url }}" class="btn btn-primary" download>
                <i class="fa-solid fa-download me-2"></i> Download Report
            </a>
            <a href="{% url 'script_report_update' report.slug %}" class="btn btn-warning">
                <i class="fa-solid fa-edit me-2"></i> Edit
            </a>
            <a href="{% url 'csv_upload' %}" class="btn btn-info">
                <i class="fa-solid fa-file-csv me-2"></i> Upload CSV Data
            </a>
        </div>
    </div>

    <!-- Report Details -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Report Information</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th style="width: 30%">Date Created:</th>
                            <td>{{ report.date_created|date:"Y-m-d H:i" }}</td>
                        </tr>
                        <tr>
                            <th>Date Uploaded:</th>
                            <td>{{ report.date_uploaded|date:"Y-m-d H:i" }}</td>
                        </tr>
                        <tr>
                            <th>Equipment:</th>
                            <td>{{ report.equipment.name }}</td>
                        </tr>
                        <tr>
                            <th>Category:</th>
                            <td>{{ report.category.name }}</td>
                        </tr>
                        <tr>
                            <th>Related Audit:</th>
                            <td>
                                {% if report.ongoing_audit %}
                                    <a href="{% url 'ongoing_audit_detail' report.ongoing_audit.id %}">
                                        {{ report.ongoing_audit }}
                                    </a>
                                {% else %}
                                    <span class="text-muted">No related audit</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>Client:</th>
                            <td>
                                {% if report.client %}
                                    {{ report.client.user.get_full_name }} ({{ report.client.user.email }})
                                {% else %}
                                    <span class="text-muted">No client assigned</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>Auditor:</th>
                            <td>{{ report.auditor.user.get_full_name }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Script Information</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th style="width: 30%">Script Name:</th>
                            <td>{{ report.script_name|default:"Not specified" }}</td>
                        </tr>
                        <tr>
                            <th>Script Version:</th>
                            <td>{{ report.script_version|default:"Not specified" }}</td>
                        </tr>
                        <tr>
                            <th>Report File:</th>
                            <td>
                                <a href="{{ report.report_file.url }}" download>
                                    {{ report.report_file.name|cut:"script_reports/" }}
                                </a>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Content -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Description</h5>
                </div>
                <div class="card-body">
                    {{ report.description|linebreaks|default:"No description provided." }}
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Key Findings</h5>
                </div>
                <div class="card-body">
                    {{ report.findings|linebreaks|default:"No findings provided." }}
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Recommendations</h5>
                </div>
                <div class="card-body">
                    {{ report.recommendations|linebreaks|default:"No recommendations provided." }}
                </div>
            </div>
        </div>
    </div>

    <!-- Compliance Data -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Compliance Data</h5>
                    {% if request.user.is_staff or request.user.profile.role == 'AU' %}
                    <a href="{% url 'csv_upload' %}?report_id={{ report.id }}" class="btn btn-primary btn-sm">
                        <i class="fa-solid fa-file-csv me-2"></i> Upload New CSV Data
                    </a>
                    {% endif %}
                </div>
                {% if compliance_data %}
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Total Checks</h5>
                                    <h2 class="display-4">{{ compliance_data.total_checks }}</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Passed Checks</h5>
                                    <h2 class="display-4 text-success">{{ compliance_data.passed_checks }}</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Failed Checks</h5>
                                    <h2 class="display-4 text-danger">{{ compliance_data.failed_checks }}</h2>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Category Breakdown -->
                    {% if categories %}
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>Category Breakdown</h5>
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Category</th>
                                        <th>Total Checks</th>
                                        <th>Passed Checks</th>
                                        <th>Compliance Score</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for category, data in categories.items %}
                                    <tr>
                                        <td>{{ category }}</td>
                                        <td>{{ data.total }}</td>
                                        <td>{{ data.passed }}</td>
                                        <td>
                                            <div class="progress" style="height: 8px; width: 120px;">
                                                <div class="progress-bar {% if data.score >= 70 %}bg-success{% elif data.score >= 50 %}bg-warning{% else %}bg-danger{% endif %}"
                                                     role="progressbar"
                                                     style="width: {{ data.score }}%;"
                                                     aria-valuenow="{{ data.score }}"
                                                     aria-valuemin="0"
                                                     aria-valuemax="100">
                                                </div>
                                            </div>
                                            <span class="ml-2">{{ data.score|floatformat:1 }}%</span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Detailed Data -->
                    {% if detailed_data %}
                    <div class="row">
                        <div class="col-12">
                            <h5>Detailed Compliance Data</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            {% for key in detailed_data.0.keys %}
                                            <th>{{ key }}</th>
                                            {% endfor %}
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in detailed_data %}
                                        <tr>
                                            {% for key, value in item.items %}
                                            <td>
                                                {% if key == 'Result' %}
                                                    {% if value == 'Pass' %}
                                                        <span class="badge bg-success">Pass</span>
                                                    {% elif value == 'Fail' %}
                                                        <span class="badge bg-danger">Fail</span>
                                                    {% else %}
                                                        {{ value }}
                                                    {% endif %}
                                                {% else %}
                                                    {{ value }}
                                                {% endif %}
                                            </td>
                                            {% endfor %}
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% else %}
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> No Compliance Data Available</h5>
                        <p>No detailed compliance data is available for this report yet.</p>
                        {% if request.user.is_staff or request.user.profile.role == 'AU' %}
                        <p>Please upload the corresponding CSV file to see detailed compliance information.</p>
                        <a href="{% url 'csv_upload' %}?report_id={{ report.id }}" class="btn btn-primary">
                            <i class="fa-solid fa-file-csv me-2"></i> Upload CSV Data
                        </a>
                        {% else %}
                        <p>Compliance data will be available once the auditor uploads the CSV file.</p>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock content %}
