"""
Module: equipment.views

Contains Django views for managing equipment items, categories,
and technological solutions in the audit platform.

Classes handle equipment listing, creation, updating,
deletion, and related functionalities.
The module integrates with Django's authentication
and querying functionalities.
"""

# Standard library imports
import operator
from functools import reduce

# Django core imports
from django.shortcuts import render, redirect
from django.urls import reverse, reverse_lazy
from django.http import JsonResponse
from django.views.decorators.http import require_POST, require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.db.models import Q, Count, Sum
import json

# Authentication and permissions
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin

# Class-based views
from django.views.generic import (
    DetailView, CreateView, UpdateView, DeleteView, ListView
)
from django.views.generic.edit import FormMixin

# Third-party packages
from django_tables2 import SingleTableView
import django_tables2 as tables
from django_tables2.export.views import ExportMixin

# Local app imports
from accounts.models import Profile, Auditor, AuditorNotification
from audit_process.models import AuditRequest, OngoingAudit, AuditCategory
from .models import Category, Item, SolutionTechnologique
from .forms import ItemForm, CategoryForm, SolutionTechnologiqueForm
from .tables import ItemTable


@login_required
def dashboard(request):
    # Check if user is a client and redirect to client dashboard if so
    try:
        from client_portal.models import ClientProfile
        if ClientProfile.objects.filter(user=request.user).exists():
            return redirect('client_dashboard')
    except ImportError:
        pass

    profiles = Profile.objects.all()
    Category.objects.annotate(nitem=Count("item"))
    items = Item.objects.all()
    items_count = items.count()
    total_items = items_count
    profiles_count = profiles.count()

    # Prepare data for charts
    category_counts = Category.objects.annotate(
        item_count=Count("item")
    ).values("name", "item_count")
    categories = [cat["name"] for cat in category_counts]
    category_counts = [cat["item_count"] for cat in category_counts]

    # Get audit request data for chart
    audit_request_dates = (
        AuditRequest.objects.values("date_added__date")
        .annotate(total_sales=Sum("total"))
        .order_by("date_added__date")
    )
    audit_request_dates_labels = [
        date["date_added__date"].strftime("%Y-%m-%d") for date in audit_request_dates
    ]
    audit_request_dates_values = [float(date["total_sales"]) for date in audit_request_dates]

    # Get ongoing audit data for chart
    ongoing_audit_dates = (
        OngoingAudit.objects.values("start_date__date")
        .annotate(total_audits=Count("id"))
        .order_by("start_date__date")
    )

    # Combine both datasets for a comprehensive chart
    all_dates = set(audit_request_dates_labels)
    for date in ongoing_audit_dates:
        all_dates.add(date["start_date__date"].strftime("%Y-%m-%d"))

    all_dates = sorted(list(all_dates))

    # Create a dictionary to store audit counts by date
    audit_counts = {date: 0 for date in all_dates}

    # Add ongoing audit counts
    for date in ongoing_audit_dates:
        date_str = date["start_date__date"].strftime("%Y-%m-%d")
        audit_counts[date_str] = date["total_audits"]

    # Final chart data
    audit_dates_labels = all_dates
    audit_dates_values = [audit_counts[date] for date in all_dates]

    # Get both types of audit requests
    from audit_requests.models import AuditRequest as ClientAuditRequest

    # Count audit requests from both models
    audit_process_requests = AuditRequest.objects.all()
    client_audit_requests = ClientAuditRequest.objects.all()
    total_audit_requests = audit_process_requests.count() + client_audit_requests.count()

    context = {
        "items": items,
        "profiles": profiles,
        "profiles_count": profiles_count,
        "items_count": items_count,
        "total_items": total_items,
        "auditors": Auditor.objects.all(),
        "audit_requests": client_audit_requests,  # Use client audit requests for display
        "audit_requests_count": total_audit_requests,  # Use total count for the dashboard
        "ongoing_audits": OngoingAudit.objects.all(),
        "categories": categories,
        "category_counts": category_counts,
        # Use the combined audit data for the chart
        "audit_request_dates_labels": audit_dates_labels,
        "audit_request_dates_values": audit_dates_values,
    }
    return render(request, "equipment/dashboard.html", context)


class ProductListView(LoginRequiredMixin, ExportMixin, tables.SingleTableView):
    """
    View class to display a list of products.

    Attributes:
    - model: The model associated with the view.
    - table_class: The table class used for rendering.
    - template_name: The HTML template used for rendering the view.
    - context_object_name: The variable name for the context object.
    - paginate_by: Number of items per page for pagination.
    """

    model = Item
    table_class = ItemTable
    template_name = "equipment/productslist.html"
    context_object_name = "items"
    paginate_by = 10
    SingleTableView.table_pagination = False

    def get_queryset(self):
        """
        Return the queryset ordered by ID.
        """
        return Item.objects.all().order_by('id')


class ItemSearchListView(ProductListView):
    """
    View class to search and display a filtered list of items.

    Attributes:
    - paginate_by: Number of items per page for pagination.
    """

    paginate_by = 10

    def get_queryset(self):
        result = super(ItemSearchListView, self).get_queryset()

        query = self.request.GET.get("q")
        if query:
            query_list = query.split()
            result = result.filter(
                reduce(
                    operator.and_, ( Q(name__icontains=q) for q in query_list)
                )
            )
        return result


class ProductDetailView(LoginRequiredMixin, DetailView):
    """
    View class to display detailed information about a product.

    Attributes:
    - model: The model associated with the view.
    - template_name: The HTML template used for rendering the view.
    """

    model = Item
    template_name = "equipment/productdetail.html"


class ProductCreateView(LoginRequiredMixin, CreateView):
    """
    View class to create a new product.

    Attributes:
    - model: The model associated with the view.
    - template_name: The HTML template used for rendering the view.
    - form_class: The form class used for data input.
    - success_url: The URL to redirect to upon successful form submission.
    """

    model = Item
    template_name = "equipment/productcreate.html"
    form_class = ItemForm
    success_url = "/products"

    def test_func(self):
        quantity = int(self.request.POST.get("quantity", 0))  # Convertir en int avec 0 par défaut
        return quantity >= 1



class ProductUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    """
    View class to update product information.

    Attributes:
    - model: The model associated with the view.
    - template_name: The HTML template used for rendering the view.
    - fields: The fields to be updated.
    - success_url: The URL to redirect to upon successful form submission.
    """

    model = Item
    template_name = "equipment/productupdate.html"
    form_class = ItemForm
    success_url = "/products"

    def test_func(self):
        # Permettre aux administrateurs et aux auditeurs de modifier les équipements
        if self.request.user.is_superuser:
            return True
        elif hasattr(self.request.user, 'profile'):
            return self.request.user.profile.role in ['AD', 'AU']  # Admin ou Auditeur
        else:
            return False


class ProductDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    """
    View class to delete a product.

    Attributes:
    - model: The model associated with the view.
    - template_name: The HTML template used for rendering the view.
    - success_url: The URL to redirect to upon successful deletion.
    """

    model = Item
    template_name = "equipment/productdelete.html"
    success_url = "/products"

    def test_func(self):
        # Permettre aux administrateurs et aux auditeurs de supprimer les équipements
        if self.request.user.is_superuser:
            return True
        elif hasattr(self.request.user, 'profile'):
            return self.request.user.profile.role in ['AD', 'AU']  # Admin ou Auditeur
        else:
            return False


# Delivery-related views removed as the Delivery model is no longer needed


class CategoryListView(LoginRequiredMixin, ListView):
    model = Category
    template_name = 'equipment/category_list.html'
    context_object_name = 'categories'
    paginate_by = 10
    login_url = 'login'

    def get_queryset(self):
        """
        Return the queryset ordered by ID to avoid pagination warning.
        """
        return Category.objects.all().order_by('id')


class CategoryDetailView(LoginRequiredMixin, DetailView):
    model = Category
    template_name = 'equipment/category_detail.html'
    context_object_name = 'category'
    login_url = 'login'


class CategoryCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    model = Category
    template_name = 'equipment/category_form.html'
    form_class = CategoryForm
    login_url = 'login'

    def test_func(self):
        # Permettre aux administrateurs et aux auditeurs de créer des catégories
        if self.request.user.is_superuser:
            print(f"User {self.request.user.username} is superuser, access granted")
            return True
        elif hasattr(self.request.user, 'profile'):
            user_role = self.request.user.profile.role
            print(f"User {self.request.user.username} has role: {user_role}")
            has_permission = user_role in ['AD', 'AU']  # Admin ou Auditeur
            print(f"Permission granted: {has_permission}")
            return has_permission
        else:
            print(f"User {self.request.user.username} has no profile, access denied")
            return False

    def get_success_url(self):
        return reverse_lazy('category-detail', kwargs={'pk': self.object.pk})


class CategoryUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    model = Category
    template_name = 'equipment/category_form.html'
    form_class = CategoryForm
    login_url = 'login'

    def test_func(self):
        # Permettre aux administrateurs et aux auditeurs de modifier des catégories
        if self.request.user.is_superuser:
            return True
        elif hasattr(self.request.user, 'profile'):
            return self.request.user.profile.role in ['AD', 'AU']  # Admin ou Auditeur
        else:
            return False

    def get_success_url(self):
        return reverse_lazy('category-detail', kwargs={'pk': self.object.pk})


@login_required
def category_delete_view(request, pk):
    """
    Function-based view to delete a category.
    """
    # Vérifier si l'utilisateur est autorisé (admin ou auditeur)
    is_authorized = False
    if request.user.is_superuser:
        is_authorized = True
    elif hasattr(request.user, 'profile'):
        is_authorized = request.user.profile.role in ['AD', 'AU']  # Admin ou Auditeur

    if not is_authorized:
        return redirect('category-list')

    try:
        category = Category.objects.get(pk=pk)

        if request.method == 'POST':
            # Delete the category
            category.delete()
            # Redirect to the category list
            return redirect('category-list')

        return render(request, 'equipment/category_confirm_delete.html', {'category': category})
    except Category.DoesNotExist:
        # If the category doesn't exist, redirect to the category list
        return redirect('category-list')


def is_ajax(request):
    return request.META.get('HTTP_X_REQUESTED_WITH') == 'XMLHttpRequest'


@csrf_exempt
@require_POST
@login_required
def get_items_ajax_view(request):
    if is_ajax(request):
        try:
            term = request.POST.get("term", "")
            data = []

            items = Item.objects.filter(name__icontains=term)
            for item in items[:10]:
                data.append(item.to_json())

            return JsonResponse(data, safe=False)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
    return JsonResponse({'error': 'Not an AJAX request'}, status=400)


# API endpoints for React components
@login_required
@require_http_methods(["GET"])
def dashboard_stats(request):
    """
    API endpoint to get dashboard statistics.
    """
    # Get counts with detailed logging
    total_audits = OngoingAudit.objects.count()
    print(f"Total audits: {total_audits}")

    pending_audits = OngoingAudit.objects.filter(audit_status='P').count()
    print(f"Pending audits (status='P'): {pending_audits}")

    completed_audits = OngoingAudit.objects.filter(audit_status='S').count()
    print(f"Completed audits (status='S'): {completed_audits}")

    client_count = Profile.objects.filter(role='CL').count()
    print(f"Client count (role='CL'): {client_count}")

    # Log all audit statuses to help debug
    audit_statuses = OngoingAudit.objects.values_list('audit_status', flat=True).distinct()
    print(f"All audit statuses in database: {list(audit_statuses)}")

    # Log all profile roles to help debug
    profile_roles = Profile.objects.values_list('role', flat=True).distinct()
    print(f"All profile roles in database: {list(profile_roles)}")

    # Return JSON response
    return JsonResponse({
        'totalAudits': total_audits,
        'pendingAudits': pending_audits,
        'completedAudits': completed_audits,
        'clientCount': client_count
    })

@login_required
@require_http_methods(["GET"])
def notifications(request):
    """
    API endpoint to get user notifications.
    """
    # Get the current user's profile
    try:
        user_profile = request.user.profile

        # Get notifications for this auditor
        auditor_notifications = AuditorNotification.objects.filter(auditor=user_profile).order_by('-created_at')[:10]

        # Format notifications for the frontend
        notifications = []
        for notification in auditor_notifications:
            notifications.append({
                'id': notification.id,
                'message': notification.message,
                'title': notification.title,
                'isRead': notification.is_read,
                'timestamp': notification.created_at.isoformat(),
                'relatedId': notification.related_id,
                'type': notification.notification_type
            })

        # If no notifications, provide a default message
        if not notifications:
            notifications = [{
                'id': 0,
                'message': 'No new notifications',
                'title': 'Welcome',
                'isRead': False,
                'timestamp': '2025-04-25T10:30:00Z',
                'relatedId': None,
                'type': 'system'
            }]

        return JsonResponse(notifications, safe=False)
    except Exception as e:
        # Log the error and return a fallback response
        print(f"Error fetching notifications: {str(e)}")
        return JsonResponse([{
            'id': 0,
            'message': 'Error fetching notifications',
            'title': 'Error',
            'isRead': False,
            'timestamp': '2025-04-25T10:30:00Z',
            'relatedId': None,
            'type': 'system'
        }], safe=False)

@login_required
@require_http_methods(["POST"])
def mark_notification_read(request, notification_id):
    """
    API endpoint to mark a notification as read.
    """
    try:
        # Get the current user's profile
        user_profile = request.user.profile

        # Find the notification and mark it as read
        notification = AuditorNotification.objects.get(id=notification_id, auditor=user_profile)
        notification.is_read = True
        notification.save()

        return JsonResponse({
            'success': True,
            'message': f'Notification {notification_id} marked as read'
        })
    except AuditorNotification.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': f'Notification {notification_id} not found'
        }, status=404)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error: {str(e)}'
        }, status=500)

@require_http_methods(["GET"])
def get_audit_categories(request):
    """
    API endpoint to get all available categories.
    This endpoint doesn't require login to allow public access to category information.
    """
    # Get categories from the equipment.models.Category table
    from equipment.models import Category
    categories = Category.objects.all().values('id', 'name')

    # Convert to the format expected by the client
    formatted_categories = []
    for category in categories:
        formatted_categories.append({
            'id': category['id'],
            'name': category['name'],
            'description': ''  # Equipment categories don't have descriptions
        })

    response = JsonResponse(formatted_categories, safe=False)
    # Add CORS headers
    response["Access-Control-Allow-Origin"] = "*"
    response["Access-Control-Allow-Methods"] = "GET, OPTIONS"
    response["Access-Control-Allow-Headers"] = "Content-Type, X-CSRFToken"
    return response

@require_http_methods(["GET"])
def get_categories_api(request):
    """
    API endpoint to get all categories for the equipment creation form.
    """
    categories = Category.objects.all().values('id', 'name')
    return JsonResponse(list(categories), safe=False)

@csrf_exempt
@require_http_methods(["POST"])
def create_equipment_api(request):
    """
    API endpoint to create a new equipment item.
    """
    try:
        # Parse the JSON data from the request body
        data = json.loads(request.body)

        # Extract the required fields
        name = data.get('name')
        description = data.get('description')
        category_id = data.get('category')
        version = data.get('version')
        compliance_points = data.get('compliance_points')

        # Validate required fields
        if not all([name, description, category_id, version]):
            return JsonResponse({
                'success': False,
                'message': 'Missing required fields'
            }, status=400)

        # Set default value for compliance_points if not provided
        if not compliance_points:
            compliance_points = "0"

        # Get the category
        try:
            category = Category.objects.get(pk=category_id)
        except Category.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': f'Category with ID {category_id} does not exist'
            }, status=404)

        # Create the new equipment
        equipment = Item.objects.create(
            name=name,
            description=description,
            category=category,
            version=version,
            compliance_points=compliance_points
        )

        # Return the created equipment
        return JsonResponse({
            'success': True,
            'id': equipment.id,
            'name': equipment.name,
            'description': equipment.description,
            'category_id': equipment.category.id,
            'category_name': equipment.category.name,
            'version': equipment.version,
            'compliance_points': equipment.compliance_points
        })
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        }, status=500)

class SolutionTechnologiqueListView(LoginRequiredMixin, ListView):
    model = SolutionTechnologique
    template_name = 'equipment/solutiontechnologique_list.html'
    context_object_name = 'solutions'
    paginate_by = 10
    login_url = 'login'

    def get_queryset(self):
        """
        Return the queryset ordered by ID to avoid pagination warning.
        """
        return SolutionTechnologique.objects.all().order_by('id')


class SolutionTechnologiqueDetailView(LoginRequiredMixin, DetailView):
    model = SolutionTechnologique
    template_name = 'equipment/solutiontechnologique_detail.html'
    context_object_name = 'solution'
    login_url = 'login'


class SolutionTechnologiqueCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    model = SolutionTechnologique
    template_name = 'equipment/solutiontechnologique_form.html'
    form_class = SolutionTechnologiqueForm
    login_url = 'login'

    def test_func(self):
        # Permettre aux administrateurs et aux auditeurs de créer des solutions technologiques
        if self.request.user.is_superuser:
            return True
        elif hasattr(self.request.user, 'profile'):
            return self.request.user.profile.role in ['AD', 'AU']  # Admin ou Auditeur
        else:
            return False

    def get_success_url(self):
        return reverse_lazy('solutiontechnologique-detail', kwargs={'pk': self.object.pk})


class SolutionTechnologiqueUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    model = SolutionTechnologique
    template_name = 'equipment/solutiontechnologique_form.html'
    form_class = SolutionTechnologiqueForm
    login_url = 'login'

    def test_func(self):
        # Permettre aux administrateurs et aux auditeurs de modifier des solutions technologiques
        if self.request.user.is_superuser:
            return True
        elif hasattr(self.request.user, 'profile'):
            return self.request.user.profile.role in ['AD', 'AU']  # Admin ou Auditeur
        else:
            return False

    def get_success_url(self):
        return reverse_lazy('solutiontechnologique-detail', kwargs={'pk': self.object.pk})


@login_required
def solution_delete_view(request, pk):
    """
    Function-based view to delete a technological solution.
    """
    # Vérifier si l'utilisateur est autorisé (admin ou auditeur)
    is_authorized = False
    if request.user.is_superuser:
        is_authorized = True
    elif hasattr(request.user, 'profile'):
        is_authorized = request.user.profile.role in ['AD', 'AU']  # Admin ou Auditeur

    if not is_authorized:
        return redirect('solutiontechnologique-list')

    try:
        solution = SolutionTechnologique.objects.get(pk=pk)

        if request.method == 'POST':
            # Delete the solution
            solution.delete()
            # Redirect to the solution list
            return redirect('solutiontechnologique-list')

        return render(request, 'equipment/solutiontechnologique_confirm_delete.html', {'solution': solution})
    except SolutionTechnologique.DoesNotExist:
        # If the solution doesn't exist, redirect to the solution list
        return redirect('solutiontechnologique-list')
