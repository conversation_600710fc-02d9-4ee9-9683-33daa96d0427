{% extends 'client_portal/base.html' %}
{% load crispy_forms_tags %}
{% load static %}

{% block title %}Create Audit Request{% endblock %}
{% block page_title %}Create Audit Request{% endblock %}

{% block extra_css %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@1.5.2/dist/select2-bootstrap4.min.css">
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>Create Audit Request</h2>
                <a href="{% url 'client_audit_request_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to List
                </a>
            </div>
        </div>
    </div>
    
    <form id="audit_request_form" method="POST">
        {% csrf_token %}
        <input type="hidden" name="items_json" id="items_json" value="[]">
        
        <div class="row">
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Items to Audit</h5>
                    </div>
                    <div class="card-body">
                        <!-- Search item -->
                        <div class="mb-4">
                            <label for="searchbox_items" class="form-label">Search Item:</label>
                            <select class="form-select select2" id="searchbox_items" aria-label="Search items"></select>
                        </div>
                        
                        <!-- Delete all items button -->
                        <button type="button" class="btn btn-danger btn-sm mb-4" id="delete_all_items">
                            <i class="fas fa-trash-alt me-2"></i> Delete All Items
                        </button>
                        
                        <!-- Items Table -->
                        <div class="table-responsive my-3">
                            <table class="table table-bordered table-striped" id="items_table">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Name</th>
                                        <th>Price</th>
                                        <th>Quantity</th>
                                        <th>Total</th>
                                        <th class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Items will be added here dynamically -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Request Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="sub_total" class="form-label">Subtotal</label>
                            <input type="number" class="form-control" id="sub_total" name="sub_total" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="tax_percentage" class="form-label">Tax (%)</label>
                            <input type="number" class="form-control" id="tax_percentage" name="tax_percentage" value="0">
                        </div>
                        <div class="mb-3">
                            <label for="tax_amount" class="form-label">Tax Amount</label>
                            <input type="number" class="form-control" id="tax_amount" name="tax_amount" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="grand_total" class="form-label">Grand Total</label>
                            <input type="number" class="form-control" id="grand_total" name="grand_total" readonly>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">Submit Audit Request</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<!-- Select2 -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    $(document).ready(function() {
        // Initialize Select2
        $('#searchbox_items').select2({
            placeholder: 'Search for items to audit',
            minimumInputLength: 2,
            ajax: {
                url: '{% url "get_items" %}',
                dataType: 'json',
                delay: 250,
                data: function(params) {
                    return {
                        term: params.term,
                        csrfmiddlewaretoken: $('input[name="csrfmiddlewaretoken"]').val()
                    };
                },
                processResults: function(data) {
                    return {
                        results: data
                    };
                },
                cache: true
            }
        });
        
        // Items array to store selected items
        let items = [];
        let itemCounter = 1;
        
        // Function to update the items JSON input
        function updateItemsJson() {
            $('#items_json').val(JSON.stringify(items));
        }
        
        // Function to calculate totals
        function calculateTotals() {
            let subTotal = 0;
            
            // Calculate subtotal
            items.forEach(item => {
                subTotal += parseFloat(item.price) * parseInt(item.quantity);
            });
            
            // Calculate tax
            const taxPercentage = parseFloat($('#tax_percentage').val()) || 0;
            const taxAmount = (subTotal * taxPercentage) / 100;
            const grandTotal = subTotal + taxAmount;
            
            // Update form fields
            $('#sub_total').val(subTotal.toFixed(2));
            $('#tax_amount').val(taxAmount.toFixed(2));
            $('#grand_total').val(grandTotal.toFixed(2));
        }
        
        // Function to render the items table
        function renderItemsTable() {
            const tbody = $('#items_table tbody');
            tbody.empty();
            
            if (items.length === 0) {
                tbody.append('<tr><td colspan="6" class="text-center">No items added yet</td></tr>');
                return;
            }
            
            items.forEach((item, index) => {
                const total = parseFloat(item.price) * parseInt(item.quantity);
                
                tbody.append(`
                    <tr>
                        <td>${index + 1}</td>
                        <td>${item.name}</td>
                        <td>$${parseFloat(item.price).toFixed(2)}</td>
                        <td>
                            <input type="number" class="form-control form-control-sm item-quantity" 
                                   data-index="${index}" value="${item.quantity}" min="1">
                        </td>
                        <td>$${total.toFixed(2)}</td>
                        <td class="text-center">
                            <button type="button" class="btn btn-danger btn-sm delete-item" data-index="${index}">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </td>
                    </tr>
                `);
            });
            
            // Update the items JSON and calculate totals
            updateItemsJson();
            calculateTotals();
        }
        
        // Add item when selected from the dropdown
        $('#searchbox_items').on('select2:select', function(e) {
            const data = e.params.data;
            
            // Check if item already exists
            const existingItemIndex = items.findIndex(item => item.item_id === data.id);
            
            if (existingItemIndex !== -1) {
                // Increment quantity if item already exists
                items[existingItemIndex].quantity += 1;
            } else {
                // Add new item
                items.push({
                    item_id: data.id,
                    name: data.text,
                    price: data.price || 0,
                    quantity: 1
                });
            }
            
            // Clear the select
            $(this).val(null).trigger('change');
            
            // Render the updated table
            renderItemsTable();
        });
        
        // Handle quantity change
        $(document).on('change', '.item-quantity', function() {
            const index = $(this).data('index');
            const newQuantity = parseInt($(this).val());
            
            if (newQuantity < 1) {
                $(this).val(1);
                items[index].quantity = 1;
            } else {
                items[index].quantity = newQuantity;
            }
            
            renderItemsTable();
        });
        
        // Handle item deletion
        $(document).on('click', '.delete-item', function() {
            const index = $(this).data('index');
            
            Swal.fire({
                title: 'Are you sure?',
                text: `Remove ${items[index].name} from the audit request?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Yes, remove it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    items.splice(index, 1);
                    renderItemsTable();
                }
            });
        });
        
        // Handle delete all items
        $('#delete_all_items').on('click', function() {
            if (items.length === 0) return;
            
            Swal.fire({
                title: 'Are you sure?',
                text: 'Remove all items from the audit request?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Yes, remove all!'
            }).then((result) => {
                if (result.isConfirmed) {
                    items = [];
                    renderItemsTable();
                }
            });
        });
        
        // Handle tax percentage change
        $('#tax_percentage').on('change', function() {
            calculateTotals();
        });
        
        // Handle form submission
        $('#audit_request_form').on('submit', function(e) {
            if (items.length === 0) {
                e.preventDefault();
                Swal.fire({
                    title: 'Error!',
                    text: 'Please add at least one item to the audit request.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
        
        // Initial render
        renderItemsTable();
    });
</script>
{% endblock %}
