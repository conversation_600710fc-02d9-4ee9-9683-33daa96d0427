{% extends "equipment/base.html" %} {% load static %}{% block title %}Audit Request Detail{% endblock %}
{% block content %}
<style>
	section .receipt {
		margin: 0;
		font-size: 12px;
		position: relative;
		font-family: 'courier';
   }
	.serif {
		font-family: serif;
   }
	.sans-serif {
		font-family: 'sans-serif';
   }
	.bold {
		font-weight: 700;
   }
	.x-bold {
		font-weight: 900;
		text-shadow: 0px 0px 1px #000;
   }
	.hr, .hr-sm, .hr-lg {
		border-bottom: 1.5px dashed #333;
		margin: 10px 0;
   }
	.hr-sm {
		width: 30%;
		float: right;
   }
	.hr-lg {
		width: 100%;
   }
	.col2 {
		display: flex;
		width: 100%;
		justify-content: space-between;
   }
	.container-ticket {
		background: #e6e6e6;
		width: 100%;
		min-height: 100vh;
		display: flex;
		justify-content: center;
		padding: 50px 0;
		box-sizing: border-box;
   }
	.container-ticket .ticket {
		cursor: default;
		position: relative;
		width: 300px;
		padding: 10px 20px;
		background: #fff url(https://static.licdn.com/scds/common/u/images/apps/payments/textures/texture_paper_304x128_v1.png);
		box-shadow: 0px 5px 10px rgba(0, 0, 0, .1);
   }
	.container-ticket .ticket .head-ticket {
		text-align: center;
		padding: 0px 17px;
   }
	.container-ticket .ticket .head-ticket p {
		font-size: 14px;
   }
	.container-ticket .ticket .head-ticket p:nth-child(1) {
		font-size: 18px;
   }
	.container-ticket .ticket .head-ticket p:nth-child(6), .container-ticket .ticket .head-ticket p:nth-child(7) {
		font-size: 12px;
		text-align: left;
   }
	.container-ticket .ticket .head-ticket .code-barre {
		height: 50px;
		display: flex;
		justify-content: space-between;
		margin-left: -17px;
		margin-right: -17px;
		margin-top: 5px;
   }
	.container-ticket .ticket .head-ticket .code-barre span {
		height: 100%;
		width: 10px;
		display: inline-block;
		background: #333;
   }
	.container-ticket .ticket .body-ticket {
		padding: 0px 17px;
   }
	.container-ticket .ticket .body-ticket .produits {
		margin: 30px 0;
   }
	.container-ticket .ticket .body-ticket .carte {
		text-align: justify;
		text-align-last: center;
   }
	.container-ticket .ticket .body-ticket .carte .title-carte {
		font-size: 15px;
		font-weight: 600;
		letter-spacing: -2px;
   }
	.container-ticket .ticket .footer-ticket {
		padding: 0px 17px;
   }
	.container-ticket .ticket .footer-ticket .title-footer {
		font-size: 16px;
		font-weight: 900;
		text-shadow: 0px 1px 0px rgba(0, 0, 0, .5);
		text-align: center;
		letter-spacing: 2px;
   }
</style>
<div>
<div class="container mb-4">
    <div class="row">
        <div class="col-md-8">
            <h2>{{ audit_request.title }}</h2>
            <div class="badge bg-{{ audit_request.status|yesno:'success,warning,danger' }} mb-3">{{ audit_request.status|title }}</div>
            <p class="text-muted">Requested on {{ audit_request.date_added|date:"F d, Y" }} at {{ audit_request.date_added|time:"H:i" }}</p>

            {% if audit_request.description %}
            <div class="card mb-3">
                <div class="card-header" style="background-color: #023047; color: white;">
                    <h5 class="mb-0">Description</h5>
                </div>
                <div class="card-body">
                    <p>{{ audit_request.description }}</p>
                </div>
            </div>
            {% endif %}

            {% if audit_request.categories.all %}
            <div class="card mb-3">
                <div class="card-header" style="background-color: #023047; color: white;">
                    <h5 class="mb-0">Audit Categories</h5>
                </div>
                <div class="card-body">
                    {% for category in audit_request.categories.all %}
                    <span class="badge bg-info me-2">{{ category.name }}</span>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            {% if audit_request.config_file %}
            <div class="card mb-3">
                <div class="card-header" style="background-color: #023047; color: white;">
                    <h5 class="mb-0">Configuration File</h5>
                </div>
                <div class="card-body">
                    <a href="{{ audit_request.config_file.url }}" class="btn btn-sm btn-outline-primary" download>
                        <i class="fas fa-download me-2"></i> Download Configuration File
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
        <div class="col-md-4 text-end">
            <div class="d-flex justify-content-end mb-3">
                <a href="{% url 'audit_request_list' %}" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-left me-2"></i> Back
                </a>
                <a href="#" class="btn btn-outline-primary">
                    <i class="fa fa-print me-2"></i> Print
                </a>
            </div>

            {% if audit_request.status == 'pending' %}
            <div class="card border-light shadow-sm mb-4">
                <div class="card-header" style="background-color: #023047; color: white;">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">This audit request is waiting for your response.</p>
                    <a href="{% url 'launch_audit' audit_request.id %}" class="btn btn-success w-100 mb-2">
                        <i class="fa fa-check me-2"></i> Accept and Launch Audit
                    </a>
                    <a href="{% url 'reject_audit' audit_request.id %}" class="btn btn-danger w-100">
                        <i class="fa fa-times me-2"></i> Reject Request
                    </a>
                </div>
            </div>
            {% endif %}

            <div class="card border-light shadow-sm">
                <div class="card-header" style="background-color: #023047; color: white;">
                    <h5 class="mb-0">Client Information</h5>
                </div>
                <div class="card-body">
                    <p><strong>Name:</strong> {{ audit_request.customer.first_name }} {{ audit_request.customer.last_name }}</p>
                    <p><strong>Email:</strong> {{ audit_request.customer.email }}</p>
                    <p><strong>Phone:</strong> {{ audit_request.customer.phone }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
<section class="receipt container-ticket">
    <div class="ticket">
        <div class="head-ticket">
            <p class="x-bold">AuditManager</p>
            <p class="bold">Tom Mboya Street, Tudor</p>
            <p class="bold">Tel: +2547 00 000000</p>
            <br>
            <p class="bold">P.O BOX. 90420-80100 MSA</p>
            <p>Date: {{ audit_request.date_added|date:"Y/m/d H:i:s" }}</p>
            <p>Request code: AUD{{ audit_request.id }}</p>
            <div class="code-barre">
				{% for i in "12345678901234567890123456789012345"|make_list %}
				<span></span>
				{% endfor %}
            </div>
        </div>
        <div class="body-ticket">
            <div class="produits">
                <div class="hr-sm"></div>
                <div class="col2">
                    <p>Audit Request</p>
                    <p class="prix"><b>ID: {{ audit_request.id }}</b></p>
                </div>
            </div>
            <div class="hr-lg"></div>
            <div class="carte">
                <p class="title-carte">Client: {{ audit_request.customer.first_name }}</p>
            </div>
            <div class="hr-lg"></div>
        </div>
        <div class="footer-ticket">
            <p class="title-footer">THANK YOU</p>
        </div>
    </div>
</section>

</div>
{% endblock %}
