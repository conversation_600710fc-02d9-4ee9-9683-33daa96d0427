{% extends 'equipment/base.html' %}
{% load static %}
{% load render_table from django_tables2 %}
{% load querystring from django_tables2 %}

{% block title %}User Profiles{% endblock title %}

{% block stylesheets %}
<style>
    .avatar {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 16px;
    }

    .bg-primary {
        background-color: #023047 !important;
    }

    .profile-image-wrapper {
        width: 40px;
        height: 40px;
        overflow: hidden;
        border-radius: 50%;
        background-color: #f0f0f0;
    }

    .profile-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
</style>
{% endblock %}

{% block content %}
<div class="container p-5">
    <style>
        .table-responsive {
            overflow-x: auto;
        }
    </style>
    <h1 class="mb-4">User Profiles</h1>
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th scope="col">Id</th>
                    <th scope="col">Profile Image</th>
                    <th scope="col">Username</th>
                    <th scope="col">Phone Number</th>
                    <th scope="col">Status</th>
                    <th scope="col">Role</th>
                    <th scope="col">Action</th>
                </tr>
            </thead>
            <tbody>
                {% for profile in profiles %}
                <tr>
                    <th scope="row">{{ profile.id }}</th>
                    <td>
                        <div class="profile-image-wrapper">
                            <img alt="Profile Image" src="{{ profile.profile_picture.url }}?v={% now 'U' %}" class="profile-image" onerror="this.onerror=null; this.src='{% static 'assets/img/team/profile-picture-3.jpg' %}';">

                        </div>
                    </td>
                    <td>{{ profile.user.username }}</td>
                    <td>{% if profile.telephone and profile.telephone != 'None' %}{{ profile.telephone }}{% else %}Not set{% endif %}</td>
                    <td>
                        {% if profile.status == 'A' %}
                        <span class="badge bg-success text-light">Active</span>
                        {% elif profile.status == 'OL' %}
                        <span class="badge bg-warning text-dark">On Leave</span>
                        {% else %}
                        <span class="badge bg-danger text-light">Inactive</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if profile.role == 'AD' %}
                        <span class="badge bg-primary">Admin</span>
                        {% elif profile.role == 'CL' %}
                        <span class="badge bg-info">Client</span>
                        {% elif profile.role == 'AU' %}
                        <span class="badge bg-secondary">Auditor</span>
                        {% else %}
                        <span class="badge bg-dark">Unknown</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if user.profile.role == 'AD' or user.profile.role == 'CL' %}
                        <a class="text-info" href="{% url 'profile-update' profile.id %}">
                            <i class="fa-solid fa-pen"></i>
                        </a>
                        {% endif %}
                        {% if user.profile.role == 'AD' %}
                        <a class="text-danger float-end" href="{% url 'profile-delete' profile.id %}">
                            <i class="fa-solid fa-trash"></i>
                        </a>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}
