import React, { useState, useEffect } from 'react';
import <PERSON>actD<PERSON> from 'react-dom';
import axios from 'axios';

// Set up CSRF token for all axios requests
function getCookie(name) {
  let cookieValue = null;
  if (document.cookie && document.cookie !== '') {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.substring(0, name.length + 1) === (name + '=')) {
        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
        break;
      }
    }
  }
  return cookieValue;
}

const csrftoken = getCookie('csrftoken');
axios.defaults.headers.common['X-CSRFToken'] = csrftoken;

const NotificationCenter = () => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Function to fetch notifications
    const fetchNotifications = async () => {
      try {
        // Fetch notifications from our Django API endpoint
        // Use only the working URL
        const response = await axios.get('/api/notifications/');
        setNotifications(response.data);
        setLoading(false);
      } catch (err) {
        // Fallback to dummy data if API fails
        console.error('Error fetching notifications:', err);
        setNotifications([
          { id: 1, message: 'New audit request received', isRead: false, timestamp: '2025-04-25T10:30:00Z' },
          { id: 2, message: 'Audit report #123 is ready for review', isRead: true, timestamp: '2025-04-24T15:45:00Z' },
          { id: 3, message: 'Client updated their profile information', isRead: false, timestamp: '2025-04-23T09:15:00Z' }
        ]);
        setLoading(false);
      }
    };

    fetchNotifications();

    // Set up polling every 30 seconds
    const intervalId = setInterval(fetchNotifications, 30000);

    // Clean up on unmount
    return () => clearInterval(intervalId);
  }, []);

  const markAsRead = async (id) => {
    try {
      // Call the API to mark notification as read
      // Use only the working URL
      await axios.post(`/api/notifications/${id}/read/`);

      // Update the state
      setNotifications(notifications.map(notification =>
        notification.id === id ? { ...notification, isRead: true } : notification
      ));
    } catch (err) {
      console.error('Error marking notification as read:', err);
      // Still update the UI even if the API call fails
      setNotifications(notifications.map(notification =>
        notification.id === id ? { ...notification, isRead: true } : notification
      ));
    }
  };

  const unreadCount = notifications.filter(notification => !notification.isRead).length;

  if (loading) return <div className="loading">Loading notifications...</div>;
  if (error) return <div className="error">{error}</div>;

  return (
    <div className="notification-center">
      <div className="notification-header">
        <h4>Notifications {unreadCount > 0 && <span className="badge bg-danger">{unreadCount}</span>}</h4>
      </div>
      <div className="notification-list">
        {notifications.length === 0 ? (
          <div className="p-3 text-center">
            <p className="text-muted mb-0">No notifications</p>
          </div>
        ) : (
          <ul className="list-group">
            {notifications.map(notification => (
              <li
                key={notification.id}
                className={`list-group-item ${!notification.isRead ? 'list-group-item-warning' : ''}`}
                onClick={() => markAsRead(notification.id)}
              >
                <div className="d-flex w-100 justify-content-between">
                  <div>
                    <p className="mb-1 fw-semibold">{notification.message}</p>
                    <small className="text-muted">{new Date(notification.timestamp).toLocaleString()}</small>
                  </div>
                  {!notification.isRead && (
                    <span className="badge bg-primary align-self-start mt-1">New</span>
                  )}
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
      {/* No notifications page exists yet, so we'll just show a message */}
      <div className="p-2 border-top text-center">
        <span className="text-muted small">All notifications shown</span>
      </div>
    </div>
  );
};

// Find all notification mount points in the page
document.addEventListener('DOMContentLoaded', () => {
  const notificationRoot = document.getElementById('notification-center');
  if (notificationRoot) {
    ReactDOM.render(<NotificationCenter />, notificationRoot);
  }
});
