{% extends 'client_portal/volt_base.html' %}

{% block title %}Client Dashboard{% endblock %}
{% block page_title %}Dashboard{% endblock %}

{% load static %}
{% load client_filters %}

{% block content %}
<div class="py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow">
                <div class="card-header d-flex flex-row align-items-center flex-0 border-bottom">
                    <div class="d-block">
                        <h2 class="h5 mb-0">Welcome, {{ user.get_full_name|default:user.username }}!</h2>
                    </div>
                </div>
                <div class="card-body">
                    <p>Welcome to your client dashboard. Here you can:</p>
                    <ul class="mb-4">
                        <li>Create new audit requests for your equipment</li>
                        <li>Track the status of your audit requests</li>
                        <li>View audit reports generated by auditors</li>
                    </ul>
                    <a href="{% url 'client_create_audit_request' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i> Create New Audit Request
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12 col-sm-6 col-xl-4 mb-4">
            <div class="card border-0 shadow">
                <div class="card-body">
                    <div class="row d-block d-xl-flex align-items-center">
                        <div class="col-12 col-xl-5 text-xl-center mb-3 mb-xl-0 d-flex align-items-center justify-content-xl-center">
                            <div class="icon-shape icon-shape-primary rounded me-4 me-sm-0">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="d-sm-none">
                                <h2 class="h5">Audit Requests</h2>
                                <h3 class="fw-extrabold mb-1">{{ audit_requests_count }}</h3>
                            </div>
                        </div>
                        <div class="col-12 col-xl-7 px-xl-0">
                            <div class="d-none d-sm-block">
                                <h2 class="h6 text-gray-400 mb-0">Audit Requests</h2>
                                <h3 class="fw-extrabold mb-2">{{ audit_requests_count }}</h3>
                            </div>
                            <small class="d-flex align-items-center text-gray-500">
                                {% if pending_requests_count > 0 %}
                                <span class="badge bg-warning me-2">{{ pending_requests_count }} Pending</span>
                                {% endif %}
                            </small>
                            <div class="small d-flex mt-1">
                                <a href="{% url 'client_audit_request_list' %}" class="text-primary fw-bold">View All</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-xl-4 mb-4">
            <div class="card border-0 shadow">
                <div class="card-body">
                    <div class="row d-block d-xl-flex align-items-center">
                        <div class="col-12 col-xl-5 text-xl-center mb-3 mb-xl-0 d-flex align-items-center justify-content-xl-center">
                            <div class="icon-shape icon-shape-secondary rounded me-4 me-sm-0">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="d-sm-none">
                                <h2 class="h5">Audit Reports</h2>
                                <h3 class="fw-extrabold mb-1">{{ audit_reports_count }}</h3>
                            </div>
                        </div>
                        <div class="col-12 col-xl-7 px-xl-0">
                            <div class="d-none d-sm-block">
                                <h2 class="h6 text-gray-400 mb-0">Audit Reports</h2>
                                <h3 class="fw-extrabold mb-2">{{ audit_reports_count }}</h3>
                            </div>
                            <small class="d-flex align-items-center text-gray-500">
                                <span class="badge bg-success me-2">{{ passed_reports_count }} Passed</span>
                                <span class="badge bg-danger me-2">{{ failed_reports_count }} Failed</span>
                            </small>
                            <div class="small d-flex mt-1">
                                <a href="{% url 'client_audit_report_list' %}" class="text-primary fw-bold">View All</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-xl-4 mb-4">
            <div class="card border-0 shadow">
                <div class="card-body">
                    <div class="row d-block d-xl-flex align-items-center">
                        <div class="col-12 col-xl-5 text-xl-center mb-3 mb-xl-0 d-flex align-items-center justify-content-xl-center">
                            <div class="icon-shape icon-shape-tertiary rounded me-4 me-sm-0">
                                <i class="fas fa-bell"></i>
                            </div>
                            <div class="d-sm-none">
                                <h2 class="h5">Notifications</h2>
                                <h3 class="fw-extrabold mb-1">{{ unread_notifications_count }}</h3>
                            </div>
                        </div>
                        <div class="col-12 col-xl-7 px-xl-0">
                            <div class="d-none d-sm-block">
                                <h2 class="h6 text-gray-400 mb-0">Notifications</h2>
                                <h3 class="fw-extrabold mb-2">{{ unread_notifications_count }}</h3>
                            </div>
                            <small class="text-gray-500">
                                Unread notifications
                            </small>
                            <div class="small d-flex mt-1">
                                <a href="{% url 'client_notifications' %}" class="text-primary fw-bold">View All</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12 col-xl-8">
            <div class="card border-0 shadow mb-4">
                <div class="card-header d-flex flex-row align-items-center flex-0 border-bottom">
                    <div class="d-block">
                        <h2 class="h6 mb-0">Recent Audit Requests</h2>
                    </div>
                    <div class="d-block ms-auto">
                        <a href="{% url 'client_audit_request_list' %}" class="btn btn-sm btn-primary">
                            <i class="fas fa-list me-2"></i> View All
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if audit_requests %}
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap mb-0 rounded">
                            <thead class="thead-light">
                                <tr>
                                    <th class="border-0 rounded-start">#</th>
                                    <th class="border-0">Date</th>
                                    <th class="border-0">Items</th>
                                    <th class="border-0">Status</th>
                                    <th class="border-0 rounded-end">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for request in audit_requests %}
                                <tr>
                                    <td>{{ request.id }}</td>
                                    <td>{{ request.date|date:"M d, Y" }}</td>
                                    <td>1</td>
                                    <td>
                                        {% if request.status == 'P' or request.status == 'pending' %}
                                        <span class="badge bg-warning">Pending Review</span>
                                        {% elif request.status == 'A' or request.status == 'approved' %}
                                        <span class="badge bg-success">Accepted - Audit in Progress</span>
                                        {% elif request.status == 'R' or request.status == 'rejected' %}
                                        <span class="badge bg-danger">Rejected</span>
                                        {% elif request.status == 'C' or request.status == 'completed' %}
                                        <span class="badge bg-info">Completed</span>
                                        {% else %}
                                        <span class="badge bg-secondary">Unknown ({{ request.status }})</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'client_audit_request_detail' request.id %}" class="btn btn-sm btn-gray-800">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted">You haven't made any audit requests yet.</p>
                        <a href="{% url 'client_create_audit_request' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i> Create Your First Audit Request
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-12 col-xl-4">
            <div class="card border-0 shadow mb-4">
                <div class="card-header d-flex flex-row align-items-center flex-0 border-bottom">
                    <div class="d-block">
                        <h2 class="h6 mb-0">Recent Audit Reports</h2>
                    </div>
                </div>
                <div class="card-body">
                    {% if audit_reports %}
                    <div class="list-group list-group-flush">
                        {% for report in audit_reports %}
                            {% if report|get_class_name == 'ScriptGeneratedReport' %}
                            <!-- Script-generated report -->
                            <a href="{% url 'client_script_report_detail' report.slug %}" class="list-group-item list-group-item-action border-bottom">
                                <div class="row align-items-center">
                                    <div class="col-auto">
                                        <div class="icon-shape icon-sm {% if report.is_compliant %}bg-success{% else %}bg-danger{% endif %} text-white rounded-circle">
                                            <i class="fas fa-file-code"></i>
                                        </div>
                                    </div>
                                    <div class="col ms-n2">
                                        <h4 class="h6 mb-0">
                                            <span class="text-dark">{{ report.title }}</span>
                                        </h4>
                                        <span class="text-muted small">{{ report.date_created|date:"M d, Y" }}</span>
                                    </div>
                                    <div class="col-auto">
                                        <div class="d-flex align-items-center">
                                            <span class="small me-2">{{ report.compliance_score }}%</span>
                                            <span class="badge {% if report.is_compliant %}bg-success{% else %}bg-danger{% endif %}">
                                                {% if report.is_compliant %}Compliant{% else %}Non-Compliant{% endif %}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </a>
                            {% else %}
                            <!-- Standard audit report -->
                            <a href="{% url 'client_audit_report_detail' report.slug %}" class="list-group-item list-group-item-action border-bottom">
                                <div class="row align-items-center">
                                    <div class="col-auto">
                                        <div class="icon-shape icon-sm {% if report.audit_result %}bg-success{% else %}bg-danger{% endif %} text-white rounded-circle">
                                            {% if report.audit_result %}
                                            <i class="fas fa-check"></i>
                                            {% else %}
                                            <i class="fas fa-times"></i>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col ms-n2">
                                        <h4 class="h6 mb-0">
                                            <span class="text-dark">Audit Report #{{ report.id }}</span>
                                        </h4>
                                        <span class="text-muted small">{{ report.date|date:"M d, Y" }}</span>
                                    </div>
                                    <div class="col-auto">
                                        <span class="badge {% if report.audit_result %}bg-success{% else %}bg-danger{% endif %}">
                                            {% if report.audit_result %}Pass{% else %}Fail{% endif %}
                                        </span>
                                    </div>
                                </div>
                            </a>
                            {% endif %}
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted">No audit reports found.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card border-0 shadow">
                <div class="card-header d-flex flex-row align-items-center flex-0 border-bottom">
                    <div class="d-block">
                        <h2 class="h6 mb-0">Recent Notifications</h2>
                    </div>
                    <div class="d-block ms-auto">
                        <a href="{% url 'client_notifications' %}" class="btn btn-sm btn-primary">View All</a>
                    </div>
                </div>
                <div class="card-body">
                    {% if notifications %}
                    <div class="list-group list-group-flush">
                        {% for notification in notifications %}
                        <a href="{% url 'client_mark_notification_read' notification.id %}" class="list-group-item list-group-item-action border-bottom">
                            <div class="row align-items-center">
                                <div class="col-auto">
                                    <div class="icon-shape icon-sm
                                        {% if notification.notification_type == 'audit_request' %}bg-primary
                                        {% elif notification.notification_type == 'audit_report' %}bg-info
                                        {% else %}bg-secondary{% endif %}
                                        text-white rounded-circle">
                                        {% if notification.notification_type == 'audit_request' %}
                                        <i class="fas fa-clipboard-list"></i>
                                        {% elif notification.notification_type == 'audit_report' %}
                                        <i class="fas fa-file-alt"></i>
                                        {% else %}
                                        <i class="fas fa-bell"></i>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col ms-n2">
                                    <h4 class="h6 mb-0">
                                        <span class="text-dark">{{ notification.title }}</span>
                                    </h4>
                                    <p class="text-muted small mb-0">{{ notification.message|truncatechars:100 }}</p>
                                    <span class="text-muted small">{{ notification.created_at|date:"M d, Y" }}</span>
                                </div>
                                <div class="col-auto">
                                    {% if not notification.is_read %}
                                    <span class="badge bg-danger">New</span>
                                    {% endif %}
                                </div>
                            </div>
                        </a>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted">No unread notifications.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // Chart.js configuration for Audit Results chart
    const resultsChartCtx = document.getElementById('resultsChart').getContext('2d');
    const resultsChart = new Chart(resultsChartCtx, {
        type: 'doughnut',
        data: {
            labels: ['Passed', 'Failed'],
            datasets: [{
                data: [{{ passed_reports_count }}, {{ failed_reports_count }}],
                backgroundColor: [
                    'rgba(25, 135, 84, 0.8)',
                    'rgba(220, 53, 69, 0.8)'
                ],
                borderColor: [
                    'rgba(25, 135, 84, 1)',
                    'rgba(220, 53, 69, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                },
                title: {
                    display: false,
                    text: 'Audit Results'
                }
            }
        }
    });

</script>
{% endblock %}
